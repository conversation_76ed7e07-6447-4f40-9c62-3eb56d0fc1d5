import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split

import os


# Load the dataset
d_pth = "./datasets/non_conflict_depression.csv"
df = pd.read_csv(d_pth)

# Extract notes and labels
note = list(df['TEXT'])
label = list(df['DEPRESSION'])

# Generate indices and split them into training and testing sets
indices = np.arange(len(df))
train_indices, test_indices = train_test_split(indices, test_size=0.2, random_state=48)

# Convert indices to lists of integers
train_indices = train_indices.tolist()
test_indices = test_indices.tolist()

# Split the data using the indices
x_train = [note[i] for i in train_indices]
x_label = [label[i] for i in train_indices]
x_train_df = pd.DataFrame({"note": x_train, "ROW_ID": [df["ROW_ID"].iloc[i] for i in train_indices]})
y_train_df = pd.DataFrame({"label": x_label, "ROW_ID": [df["ROW_ID"].iloc[i] for i in  train_indices]})



y_train = [note[i] for i in test_indices]
y_label = [label[i] for i in test_indices]
x_test_df  = pd.DataFrame({"note": y_train, "ROW_ID": [df["ROW_ID"].iloc[i] for i in test_indices]})
y_test_df = pd.DataFrame({"label": y_label, "ROW_ID": [df["ROW_ID"].iloc[i] for i in test_indices]})

pth = "./datasets/train_test_split/"
if not os.path.exists(pth):
    os.mkdir(pth)
x_train_df.to_csv( pth+"x_train.csv", index=False)
y_train_df.to_csv(pth+"y_train.csv", index=False)
x_test_df.to_csv(pth+"x_test.csv", index=False)
y_test_df.to_csv(pth+"y_test.csv", index=False)

len(x_train_df), len(y_train_df), len(x_test_df), len(y_test_df)