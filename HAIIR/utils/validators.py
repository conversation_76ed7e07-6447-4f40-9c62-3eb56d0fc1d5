"""
Data validation utilities for HAIIR Application
"""

import pandas as pd
from pathlib import Path
import json
from typing import Dict, Any, Optional


def validate_mimic_data_path(mimic_path: Path) -> bool:
    """Validate that MIMIC data path exists and contains required files."""
    if not mimic_path.exists():
        return False
        
    required_files = ["PATIENTS.csv.gz", "NOTEEVENTS.csv.gz"]
    for file_name in required_files:
        if not (mimic_path / file_name).exists():
            return False
            
    return True


def validate_patients_dataframe(df: pd.DataFrame) -> bool:
    """Validate patients dataframe structure."""
    required_columns = ["ROW_ID", "SUBJECT_ID", "GENDER"]
    
    if df is None or df.empty:
        return False
        
    for col in required_columns:
        if col not in df.columns:
            return False
            
    return True


def validate_notes_dataframe(df: pd.DataFrame) -> bool:
    """Validate notes dataframe structure."""
    required_columns = ["ROW_ID", "SUBJECT_ID", "TEXT"]
    
    if df is None or df.empty:
        return False
        
    for col in required_columns:
        if col not in df.columns:
            return False
            
    return True


def validate_annotation_data(annotation: Dict[str, Any]) -> bool:
    """Validate annotation data structure."""
    required_fields = ["note_id", "annotator_id", "timestamp", "criteria"]
    
    for field in required_fields:
        if field not in annotation:
            return False
            
    # Validate criteria structure
    criteria = annotation.get("criteria", {})
    criteria_types = ["diagnosis", "treatment", "self_harm"]
    
    for criteria_type in criteria_types:
        if criteria_type not in criteria:
            return False
            
        criteria_data = criteria[criteria_type]
        required_criteria_fields = ["positive", "confidence"]
        
        for field in required_criteria_fields:
            if field not in criteria_data:
                return False
                
        # Validate confidence range
        confidence = criteria_data.get("confidence")
        if not isinstance(confidence, (int, float)) or not (0.0 <= confidence <= 1.0):
            return False
            
    return True


def validate_json_file(file_path: Path) -> bool:
    """Validate that a file contains valid JSON."""
    try:
        with open(file_path, 'r') as f:
            json.load(f)
        return True
    except (json.JSONDecodeError, FileNotFoundError, IOError):
        return False


def sanitize_text_for_display(text: str, max_length: Optional[int] = None) -> str:
    """Sanitize text for safe display in UI."""
    if not isinstance(text, str):
        text = str(text)
        
    # Remove null characters and other problematic characters
    text = text.replace('\x00', '').replace('\r\n', '\n').replace('\r', '\n')
    
    # Truncate if needed
    if max_length and len(text) > max_length:
        text = text[:max_length] + "..."
        
    return text


def validate_subject_id(subject_id: Any) -> bool:
    """Validate subject ID format."""
    try:
        int(subject_id)
        return True
    except (ValueError, TypeError):
        return False


def validate_confidence_score(confidence: Any) -> bool:
    """Validate confidence score."""
    try:
        score = float(confidence)
        return 0.0 <= score <= 1.0
    except (ValueError, TypeError):
        return False
