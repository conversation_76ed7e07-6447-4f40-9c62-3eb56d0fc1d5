"""
Configuration settings for HAIIR Application
"""

from pathlib import Path

# Application settings
APP_NAME = "HAIIR"
APP_VERSION = "1.0.0"
APP_DESCRIPTION = "Human-AI Interactive Review"

# Paths
BASE_DIR = Path(__file__).parent.parent
RESOURCES_DIR = BASE_DIR / "resources"
DATA_DIR = BASE_DIR / "data"

# MIMIC data settings
MIMIC_DIR = BASE_DIR.parent / "mimic"
PATIENTS_FILE = "PATIENTS.csv.gz"
NOTES_FILE = "NOTEEVENTS.csv.gz"

# UI settings
DEFAULT_WINDOW_WIDTH = 1400
DEFAULT_WINDOW_HEIGHT = 900
DEFAULT_FONT_SIZE = 10
DEFAULT_FONT_FAMILY = "Arial"

# Data loading settings
CHUNK_SIZE = 10000  # For loading large CSV files
MAX_DISPLAY_NOTES = 1000  # Maximum notes to display at once

# Annotation settings (for future phases)
CRITERIA_TYPES = ["diagnosis", "treatment", "self_harm"]
CONFIDENCE_SCALE = (0.0, 1.0)
ANNOTATION_TIMESTAMP_FORMAT = "%Y-%m-%d %H:%M:%S"

# File formats
ANNOTATION_FILE_EXTENSION = ".json"
EXPORT_FILE_EXTENSION = ".csv"
