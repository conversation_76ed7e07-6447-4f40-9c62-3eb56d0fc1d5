#!/usr/bin/env python3
"""
HAIIR Installation Script
Automated setup for the HAIIR application.
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description):
    """Run a command and handle errors."""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"   Command: {command}")
        print(f"   Error: {e.stderr}")
        return False


def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major != 3 or version.minor < 9:
        print(f"❌ Python {version.major}.{version.minor} detected. Python 3.9+ required.")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} detected")
    return True


def check_conda_environment():
    """Check if we're in the correct conda environment."""
    conda_env = os.environ.get('CONDA_DEFAULT_ENV')
    if conda_env:
        print(f"✅ Conda environment detected: {conda_env}")
        return True
    else:
        print("⚠️  No conda environment detected")
        print("   Consider activating the .conda environment: conda activate .conda")
        return True  # Not fatal, just a warning


def check_mimic_data():
    """Check if MIMIC data files exist."""
    mimic_path = Path(__file__).parent.parent / "mimic"
    required_files = ["PATIENTS.csv.gz", "NOTEEVENTS.csv.gz"]
    
    if not mimic_path.exists():
        print(f"⚠️  MIMIC data directory not found: {mimic_path}")
        print("   The application will show an error when trying to load data")
        return False
    
    missing_files = []
    for file_name in required_files:
        if not (mimic_path / file_name).exists():
            missing_files.append(file_name)
    
    if missing_files:
        print(f"⚠️  Missing MIMIC data files: {', '.join(missing_files)}")
        print(f"   Expected location: {mimic_path}")
        return False
    
    print(f"✅ MIMIC data files found in {mimic_path}")
    return True


def install_requirements(minimal=False):
    """Install Python requirements."""
    requirements_file = "requirements-minimal.txt" if minimal else "requirements.txt"
    
    if not Path(requirements_file).exists():
        print(f"❌ Requirements file not found: {requirements_file}")
        return False
    
    command = f"pip install -r {requirements_file}"
    description = f"Installing {'minimal ' if minimal else ''}requirements"
    
    return run_command(command, description)


def run_tests():
    """Run basic functionality tests."""
    if not Path("test_basic.py").exists():
        print("⚠️  Test file not found: test_basic.py")
        return True  # Not fatal
    
    return run_command("python test_basic.py", "Running basic functionality tests")


def main():
    """Main installation process."""
    print("HAIIR Installation Script")
    print("=" * 50)
    
    # Check prerequisites
    if not check_python_version():
        sys.exit(1)
    
    check_conda_environment()
    
    # Ask user for installation type
    print("\nInstallation Options:")
    print("1. Minimal (Phase 1 only - recommended for testing)")
    print("2. Full (All phases - includes optional dependencies)")
    
    while True:
        choice = input("\nSelect installation type (1 or 2): ").strip()
        if choice in ["1", "2"]:
            break
        print("Please enter 1 or 2")
    
    minimal = choice == "1"
    
    # Install requirements
    print(f"\n🚀 Starting {'minimal' if minimal else 'full'} installation...")
    
    if not install_requirements(minimal):
        print("\n❌ Installation failed. Please check the error messages above.")
        sys.exit(1)
    
    # Check MIMIC data
    print("\n📊 Checking MIMIC data availability...")
    mimic_available = check_mimic_data()
    
    # Run tests
    print("\n🧪 Running tests...")
    tests_passed = run_tests()
    
    # Summary
    print("\n" + "=" * 50)
    print("Installation Summary:")
    print(f"✅ Requirements installed ({'minimal' if minimal else 'full'})")
    print(f"{'✅' if mimic_available else '⚠️ '} MIMIC data {'available' if mimic_available else 'not found'}")
    print(f"{'✅' if tests_passed else '⚠️ '} Tests {'passed' if tests_passed else 'had issues'}")
    
    if mimic_available and tests_passed:
        print("\n🎉 Installation completed successfully!")
        print("\nTo launch HAIIR:")
        print("   python main.py")
    else:
        print("\n⚠️  Installation completed with warnings.")
        print("   The application may still work, but some features might be limited.")
        print("\nTo launch HAIIR anyway:")
        print("   python main.py")
    
    print("\nFor troubleshooting, see README.md")


if __name__ == "__main__":
    main()
