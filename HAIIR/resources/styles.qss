/* HAIIR Application Stylesheet */

/* Main Window */
QMainWindow {
    background-color: #f5f5f5;
}

/* Group Boxes */
QGroupBox {
    font-weight: bold;
    border: 2px solid #cccccc;
    border-radius: 5px;
    margin-top: 1ex;
    padding-top: 10px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px 0 5px;
}

/* Tables */
QTableWidget {
    gridline-color: #d0d0d0;
    background-color: white;
    alternate-background-color: #f8f8f8;
    selection-background-color: #3daee9;
    border: 1px solid #cccccc;
    border-radius: 3px;
}

QTableWidget::item {
    padding: 5px;
    border: none;
}

QTableWidget::item:selected {
    background-color: #3daee9;
    color: white;
}

QHeaderView::section {
    background-color: #e0e0e0;
    padding: 5px;
    border: 1px solid #cccccc;
    font-weight: bold;
}

/* List Widgets */
QListWidget {
    background-color: white;
    border: 1px solid #cccccc;
    border-radius: 3px;
}

QListWidget::item {
    padding: 8px;
    border-bottom: 1px solid #e0e0e0;
}

QListWidget::item:selected {
    background-color: #3daee9;
    color: white;
}

QListWidget::item:hover {
    background-color: #e8f4fd;
}

/* Text Edits */
QTextEdit {
    background-color: white;
    border: 1px solid #cccccc;
    border-radius: 3px;
    padding: 5px;
}

QTextEdit:focus {
    border: 2px solid #3daee9;
}

/* Line Edits */
QLineEdit {
    background-color: white;
    border: 1px solid #cccccc;
    border-radius: 3px;
    padding: 5px;
    min-height: 20px;
}

QLineEdit:focus {
    border: 2px solid #3daee9;
}

/* Combo Boxes */
QComboBox {
    background-color: white;
    border: 1px solid #cccccc;
    border-radius: 3px;
    padding: 5px;
    min-width: 100px;
}

QComboBox:focus {
    border: 2px solid #3daee9;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: none;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #666666;
    margin-right: 5px;
}

/* Buttons */
QPushButton {
    background-color: #e0e0e0;
    border: 1px solid #cccccc;
    border-radius: 3px;
    padding: 8px 16px;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #d0d0d0;
}

QPushButton:pressed {
    background-color: #c0c0c0;
}

QPushButton:focus {
    border: 2px solid #3daee9;
}

/* Progress Bar */
QProgressBar {
    border: 1px solid #cccccc;
    border-radius: 3px;
    text-align: center;
    background-color: #f0f0f0;
}

QProgressBar::chunk {
    background-color: #3daee9;
    border-radius: 2px;
}

/* Status Bar */
QStatusBar {
    background-color: #e0e0e0;
    border-top: 1px solid #cccccc;
}

/* Splitter */
QSplitter::handle {
    background-color: #cccccc;
}

QSplitter::handle:horizontal {
    width: 3px;
}

QSplitter::handle:vertical {
    height: 3px;
}

/* Scrollbars */
QScrollBar:vertical {
    background-color: #f0f0f0;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #cccccc;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #999999;
}

QScrollBar:horizontal {
    background-color: #f0f0f0;
    height: 12px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal {
    background-color: #cccccc;
    border-radius: 6px;
    min-width: 20px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #999999;
}

/* Menu Bar */
QMenuBar {
    background-color: #e0e0e0;
    border-bottom: 1px solid #cccccc;
}

QMenuBar::item {
    padding: 5px 10px;
}

QMenuBar::item:selected {
    background-color: #3daee9;
    color: white;
}

/* Menus */
QMenu {
    background-color: white;
    border: 1px solid #cccccc;
}

QMenu::item {
    padding: 5px 20px;
}

QMenu::item:selected {
    background-color: #3daee9;
    color: white;
}
