# HAIIR (Human-AI Interactive Review) Application Requirements
# Compatible with Python 3.11 and existing AI-chart-review conda environment

# GUI Framework - Use conda installation for best compatibility
# Install with: conda install pyqt=6.7.1
PyQt6>=6.7.1
PyQt6-sip>=13.9.1

# Data Processing and Analysis
pandas==2.1.4
numpy==1.24.3
pyarrow>=14.0.0  # For accelerated CSV reading

# File I/O and Compression
gzip  # Built-in Python module

# JSON and Data Validation
pydantic==2.5.2
typing-extensions==4.8.0

# Logging and Utilities
pathlib  # Built-in Python module
datetime  # Built-in Python module
json  # Built-in Python module

# Optional: For future AI integration (Phase 3)
# These are already in the main project requirements
# torch==2.6.0
# transformers>=4.21.0
# datasets>=2.0.0

# Development and Testing
pytest==7.4.3
pytest-qt==4.2.0

# Documentation
markdown==3.5.1

# Optional: For enhanced data visualization (future phases)
matplotlib==3.8.2
seaborn==0.13.0

# Optional: For advanced text processing (future phases)
nltk==3.8.1
spacy==3.7.2
