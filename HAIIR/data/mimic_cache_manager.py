"""
MIMIC Data Cache Manager for HAIIR Application
Handles caching of processed MIMIC data using pickle files for faster loading.
"""

import pickle
import logging
import os
import tempfile
from pathlib import Path
from typing import Dict, Tuple, Optional, Any
import pandas as pd

logger = logging.getLogger(__name__)


class MimicCacheManager:
    """Manages caching of MIMIC data using pickle files."""
    
    # Cache file names
    CACHE_FILES = {
        'patients': 'mimic_patients.pkl',
        'notes': 'mimic_notes.pkl',
        'icd9_dict': 'mimic_icd9_dict.pkl',
        'icd9_descriptions': 'mimic_icd9_descriptions.pkl',
        'patient_codes': 'mimic_patient_codes.pkl'
    }
    
    # Source file names for validation
    SOURCE_FILES = {
        'patients': 'PATIENTS.csv.gz',
        'notes': 'NOTEEVENTS.csv.gz',
        'icd9_dict': 'DIAGNOSES_ICD.csv.gz',
        'icd9_descriptions': 'D_ICD_DIAGNOSES.csv.gz',
        'patient_codes': 'DIAGNOSES_ICD.csv.gz'  # Same source as icd9_dict
    }
    
    def __init__(self, mimic_path: Path):
        """Initialize cache manager.
        
        Args:
            mimic_path: Path to the MIMIC data directory
        """
        self.mimic_path = Path(mimic_path)
        self.cache_dir = self.mimic_path  # Store cache files in same directory as source data
        
    def _get_cache_path(self, cache_key: str) -> Path:
        """Get the full path for a cache file."""
        return self.cache_dir / self.CACHE_FILES[cache_key]
    
    def _get_source_path(self, cache_key: str) -> Path:
        """Get the full path for a source file."""
        return self.mimic_path / self.SOURCE_FILES[cache_key]
    
    def _can_write_to_cache_dir(self) -> bool:
        """Check if we have write permissions to the cache directory."""
        try:
            # Try to create a temporary file in the cache directory
            with tempfile.NamedTemporaryFile(dir=self.cache_dir, delete=True):
                pass
            return True
        except (OSError, PermissionError):
            logger.warning(f"No write permissions to cache directory: {self.cache_dir}")
            return False
    
    def _all_cache_files_exist(self) -> bool:
        """Check if all required cache files exist."""
        for cache_key in self.CACHE_FILES:
            cache_path = self._get_cache_path(cache_key)
            if not cache_path.exists():
                logger.info(f"Cache file missing: {cache_path}")
                return False
        return True
    
    def _is_cache_valid(self) -> bool:
        """Check if the cache is valid by comparing modification times."""
        if not self._all_cache_files_exist():
            return False
        
        try:
            # Get the oldest cache file modification time
            oldest_cache_time = min(
                self._get_cache_path(cache_key).stat().st_mtime
                for cache_key in self.CACHE_FILES
            )
            
            # Check if any source file is newer than the oldest cache file
            for cache_key in self.CACHE_FILES:
                source_path = self._get_source_path(cache_key)
                if not source_path.exists():
                    logger.error(f"Source file missing: {source_path}")
                    return False
                
                source_time = source_path.stat().st_mtime
                if source_time > oldest_cache_time:
                    logger.info(f"Source file {source_path} is newer than cache, invalidating cache")
                    return False
            
            logger.info("Cache is valid - all cache files exist and are newer than source files")
            return True
            
        except (OSError, FileNotFoundError) as e:
            logger.warning(f"Error checking cache validity: {e}")
            return False
    
    def _save_pickle_atomic(self, data: Any, cache_path: Path) -> bool:
        """Save data to pickle file atomically to prevent corruption.
        
        Args:
            data: Data to save
            cache_path: Path where to save the pickle file
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Write to temporary file first
            temp_path = cache_path.with_suffix('.tmp')
            
            with open(temp_path, 'wb') as f:
                pickle.dump(data, f, protocol=pickle.HIGHEST_PROTOCOL)
            
            # Atomic rename
            temp_path.rename(cache_path)
            logger.info(f"Successfully saved cache file: {cache_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save cache file {cache_path}: {e}")
            # Clean up temporary file if it exists
            temp_path = cache_path.with_suffix('.tmp')
            if temp_path.exists():
                try:
                    temp_path.unlink()
                except:
                    pass
            return False
    
    def _load_pickle_safe(self, cache_path: Path) -> Optional[Any]:
        """Load data from pickle file safely.
        
        Args:
            cache_path: Path to the pickle file
            
        Returns:
            Loaded data or None if failed
        """
        try:
            with open(cache_path, 'rb') as f:
                data = pickle.load(f)
            logger.info(f"Successfully loaded cache file: {cache_path}")
            return data
            
        except Exception as e:
            logger.error(f"Failed to load cache file {cache_path}: {e}")
            return None
    
    def can_use_cache(self) -> bool:
        """Check if cache can be used for loading data.
        
        Returns:
            True if cache is valid and can be used
        """
        return self._is_cache_valid()
    
    def load_from_cache(self) -> Optional[Tuple[pd.DataFrame, pd.DataFrame, Dict, Dict, Dict]]:
        """Load all data from cache files.
        
        Returns:
            Tuple of (patients_df, notes_df, icd9_dict, icd9_descriptions_dict, patient_codes_dict)
            or None if any file fails to load
        """
        if not self.can_use_cache():
            return None
        
        logger.info("Loading data from cache files...")
        
        # Load all cache files
        patients_df = self._load_pickle_safe(self._get_cache_path('patients'))
        notes_df = self._load_pickle_safe(self._get_cache_path('notes'))
        icd9_dict = self._load_pickle_safe(self._get_cache_path('icd9_dict'))
        icd9_descriptions_dict = self._load_pickle_safe(self._get_cache_path('icd9_descriptions'))
        patient_codes_dict = self._load_pickle_safe(self._get_cache_path('patient_codes'))
        
        # Check if all files loaded successfully
        if all(data is not None for data in [patients_df, notes_df, icd9_dict, icd9_descriptions_dict, patient_codes_dict]):
            logger.info("Successfully loaded all data from cache")
            return patients_df, notes_df, icd9_dict, icd9_descriptions_dict, patient_codes_dict
        else:
            logger.error("Failed to load some cache files, falling back to raw data")
            return None
    
    def save_to_cache(self, patients_df: pd.DataFrame, notes_df: pd.DataFrame, 
                     icd9_dict: Dict, icd9_descriptions_dict: Dict, 
                     patient_codes_dict: Dict) -> bool:
        """Save all data to cache files.
        
        Args:
            patients_df: Patients DataFrame
            notes_df: Notes DataFrame
            icd9_dict: ICD9 diagnosis dictionary
            icd9_descriptions_dict: ICD9 descriptions dictionary
            patient_codes_dict: Patient codes dictionary
            
        Returns:
            True if all files saved successfully
        """
        if not self._can_write_to_cache_dir():
            logger.info("Cannot write to cache directory, skipping cache save")
            return False
        
        logger.info("Saving data to cache files...")
        
        # Data to save
        cache_data = {
            'patients': patients_df,
            'notes': notes_df,
            'icd9_dict': icd9_dict,
            'icd9_descriptions': icd9_descriptions_dict,
            'patient_codes': patient_codes_dict
        }
        
        # Save all files
        all_success = True
        for cache_key, data in cache_data.items():
            cache_path = self._get_cache_path(cache_key)
            success = self._save_pickle_atomic(data, cache_path)
            if not success:
                all_success = False
        
        if all_success:
            logger.info("Successfully saved all data to cache")
        else:
            logger.warning("Some cache files failed to save")
        
        return all_success
    
    def clear_cache(self) -> None:
        """Remove all cache files."""
        logger.info("Clearing cache files...")
        
        for cache_key in self.CACHE_FILES:
            cache_path = self._get_cache_path(cache_key)
            if cache_path.exists():
                try:
                    cache_path.unlink()
                    logger.info(f"Removed cache file: {cache_path}")
                except Exception as e:
                    logger.error(f"Failed to remove cache file {cache_path}: {e}")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """Get information about the cache status.
        
        Returns:
            Dictionary with cache information
        """
        info = {
            'cache_dir': str(self.cache_dir),
            'can_write': self._can_write_to_cache_dir(),
            'cache_valid': self._is_cache_valid(),
            'files_exist': {}
        }
        
        for cache_key in self.CACHE_FILES:
            cache_path = self._get_cache_path(cache_key)
            info['files_exist'][cache_key] = cache_path.exists()
            if cache_path.exists():
                try:
                    stat = cache_path.stat()
                    info[f'{cache_key}_size'] = stat.st_size
                    info[f'{cache_key}_mtime'] = stat.st_mtime
                except:
                    pass
        
        return info
