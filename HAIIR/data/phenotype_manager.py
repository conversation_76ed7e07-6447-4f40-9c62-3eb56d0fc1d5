"""
Phenotype Manager for HAIIR Application
Manages phenotype definitions and patient matching logic.
"""

import json
import logging
from pathlib import Path
from typing import Dict, Set, List, Tuple, Optional

logger = logging.getLogger(__name__)


class PhenotypeManager:
    """Manages phenotype definitions and patient matching."""
    
    def __init__(self, phenotypes_file: Optional[Path] = None):
        """Initialize the phenotype manager.
        
        Args:
            phenotypes_file: Path to the phenotypes JSON file. If None, uses default.
        """
        self.phenotypes_file = phenotypes_file or Path("phenotypes.json")
        
        # Dictionary mapping subject_id to set of ICD9 codes
        self.patient_codes_dict: Dict[int, Set[str]] = {}
        
        # Dictionary mapping phenotype name to set of ICD9 codes
        self.phenotypes_dict: Dict[str, Set[str]] = {}
        
        # Set of all unique ICD9 codes in the dataset
        self.all_icd9_codes: Set[str] = set()
        
        # Load existing phenotypes if file exists
        self.load_phenotypes()
    
    def set_patient_codes_dict(self, patient_codes_dict: Dict[int, Set[str]]):
        """Set the patient codes dictionary and update all ICD9 codes set.
        
        Args:
            patient_codes_dict: Dictionary mapping subject_id to set of ICD9 codes
        """
        self.patient_codes_dict = patient_codes_dict
        
        # Extract all unique ICD9 codes
        self.all_icd9_codes = set()
        for codes in patient_codes_dict.values():
            self.all_icd9_codes.update(codes)
        
        logger.info(f"Set patient codes for {len(patient_codes_dict)} patients")
        logger.info(f"Found {len(self.all_icd9_codes)} unique ICD9 codes")
    
    def get_all_icd9_codes(self) -> List[str]:
        """Get all unique ICD9 codes sorted."""
        return sorted(list(self.all_icd9_codes))
    
    def create_phenotype(self, name: str, icd9_codes: List[str]) -> bool:
        """Create a new phenotype.
        
        Args:
            name: Name of the phenotype
            icd9_codes: List of ICD9 codes for this phenotype
            
        Returns:
            True if created successfully, False if name already exists
        """
        if name in self.phenotypes_dict:
            logger.warning(f"Phenotype '{name}' already exists")
            return False
        
        self.phenotypes_dict[name] = set(icd9_codes)
        self.save_phenotypes()
        logger.info(f"Created phenotype '{name}' with {len(icd9_codes)} ICD9 codes")
        return True
    
    def update_phenotype(self, name: str, icd9_codes: List[str]) -> bool:
        """Update an existing phenotype.
        
        Args:
            name: Name of the phenotype
            icd9_codes: New list of ICD9 codes for this phenotype
            
        Returns:
            True if updated successfully, False if phenotype doesn't exist
        """
        if name not in self.phenotypes_dict:
            logger.warning(f"Phenotype '{name}' does not exist")
            return False
        
        self.phenotypes_dict[name] = set(icd9_codes)
        self.save_phenotypes()
        logger.info(f"Updated phenotype '{name}' with {len(icd9_codes)} ICD9 codes")
        return True
    
    def delete_phenotype(self, name: str) -> bool:
        """Delete a phenotype.
        
        Args:
            name: Name of the phenotype to delete
            
        Returns:
            True if deleted successfully, False if phenotype doesn't exist
        """
        if name not in self.phenotypes_dict:
            logger.warning(f"Phenotype '{name}' does not exist")
            return False
        
        del self.phenotypes_dict[name]
        self.save_phenotypes()
        logger.info(f"Deleted phenotype '{name}'")
        return True
    
    def get_phenotype_names(self) -> List[str]:
        """Get list of all phenotype names."""
        return sorted(list(self.phenotypes_dict.keys()))
    
    def get_phenotype_codes(self, name: str) -> Set[str]:
        """Get ICD9 codes for a specific phenotype.
        
        Args:
            name: Name of the phenotype
            
        Returns:
            Set of ICD9 codes, empty set if phenotype doesn't exist
        """
        return self.phenotypes_dict.get(name, set())
    
    def patient_matches_phenotype(self, subject_id: int, phenotype_name: str) -> bool:
        """Check if a patient matches a phenotype.
        
        Args:
            subject_id: Patient's subject ID
            phenotype_name: Name of the phenotype
            
        Returns:
            True if patient has any ICD9 codes that overlap with phenotype
        """
        if subject_id not in self.patient_codes_dict:
            return False
        
        if phenotype_name not in self.phenotypes_dict:
            return False
        
        patient_codes = self.patient_codes_dict[subject_id]
        phenotype_codes = self.phenotypes_dict[phenotype_name]
        
        # Check for intersection
        return bool(patient_codes & phenotype_codes)
    
    def get_matching_patients(self, phenotype_name: str) -> List[int]:
        """Get list of patient IDs that match a phenotype.
        
        Args:
            phenotype_name: Name of the phenotype
            
        Returns:
            List of subject IDs that match the phenotype
        """
        if phenotype_name not in self.phenotypes_dict:
            return []
        
        matching_patients = []
        for subject_id in self.patient_codes_dict:
            if self.patient_matches_phenotype(subject_id, phenotype_name):
                matching_patients.append(subject_id)
        
        return matching_patients
    
    def load_phenotypes(self):
        """Load phenotypes from JSON file."""
        if not self.phenotypes_file.exists():
            logger.info(f"Phenotypes file {self.phenotypes_file} does not exist, starting with empty phenotypes")
            return
        
        try:
            with open(self.phenotypes_file, 'r') as f:
                data = json.load(f)
            
            # Convert lists back to sets
            self.phenotypes_dict = {name: set(codes) for name, codes in data.items()}
            logger.info(f"Loaded {len(self.phenotypes_dict)} phenotypes from {self.phenotypes_file}")
            
        except Exception as e:
            logger.error(f"Error loading phenotypes from {self.phenotypes_file}: {e}")
            self.phenotypes_dict = {}
    
    def save_phenotypes(self):
        """Save phenotypes to JSON file."""
        try:
            # Convert sets to lists for JSON serialization
            data = {name: list(codes) for name, codes in self.phenotypes_dict.items()}
            
            with open(self.phenotypes_file, 'w') as f:
                json.dump(data, f, indent=2)
            
            logger.info(f"Saved {len(self.phenotypes_dict)} phenotypes to {self.phenotypes_file}")
            
        except Exception as e:
            logger.error(f"Error saving phenotypes to {self.phenotypes_file}: {e}")
