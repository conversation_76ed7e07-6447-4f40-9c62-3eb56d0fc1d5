"""
Annotation Manager for HAIIR Application
Handles human annotation data management and persistence.
"""

import json
import pandas as pd
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional
from PyQt6.QtCore import QObject, pyqtSignal

try:
    from ..utils.validators import validate_annotation_data
    from ..utils.config import ANNOTATION_TIMESTAMP_FORMAT
except ImportError:
    # Fallback for when running as script
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent))
    from utils.validators import validate_annotation_data
    from utils.config import ANNOTATION_TIMESTAMP_FORMAT


class AnnotationManager(QObject):
    """Manages human annotation data."""
    
    # Signals
    annotation_saved = pyqtSignal(dict)  # annotation_data
    annotation_loaded = pyqtSignal(list)  # list of annotations
    error_occurred = pyqtSignal(str)  # error_message
    
    def __init__(self):
        super().__init__()
        self.annotations = []
        self.current_annotator_id = "default_annotator"
        
    def set_annotator_id(self, annotator_id: str):
        """Set the current annotator ID."""
        self.current_annotator_id = annotator_id
        
    def create_annotation(self, note_id: int, criteria_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new annotation."""
        annotation = {
            "note_id": note_id,
            "annotator_id": self.current_annotator_id,
            "timestamp": datetime.now().strftime(ANNOTATION_TIMESTAMP_FORMAT),
            "criteria": criteria_data
        }
        
        if not validate_annotation_data(annotation):
            raise ValueError("Invalid annotation data structure")
            
        return annotation
        
    def save_annotation(self, note_id: int, criteria_data: Dict[str, Any]):
        """Save an annotation."""
        try:
            annotation = self.create_annotation(note_id, criteria_data)
            
            # Remove existing annotation for this note by this annotator
            self.annotations = [
                a for a in self.annotations 
                if not (a["note_id"] == note_id and a["annotator_id"] == self.current_annotator_id)
            ]
            
            # Add new annotation
            self.annotations.append(annotation)
            
            self.annotation_saved.emit(annotation)
            
        except Exception as e:
            self.error_occurred.emit(f"Failed to save annotation: {str(e)}")
            
    def get_annotation(self, note_id: int, annotator_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Get annotation for a specific note."""
        if annotator_id is None:
            annotator_id = self.current_annotator_id
            
        for annotation in self.annotations:
            if annotation["note_id"] == note_id and annotation["annotator_id"] == annotator_id:
                return annotation
                
        return None
        
    def get_all_annotations(self, annotator_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get all annotations, optionally filtered by annotator."""
        if annotator_id is None:
            return self.annotations.copy()
            
        return [a for a in self.annotations if a["annotator_id"] == annotator_id]
        
    def delete_annotation(self, note_id: int, annotator_id: Optional[str] = None):
        """Delete an annotation."""
        if annotator_id is None:
            annotator_id = self.current_annotator_id
            
        self.annotations = [
            a for a in self.annotations 
            if not (a["note_id"] == note_id and a["annotator_id"] == annotator_id)
        ]
        
    def save_to_file(self, file_path: Path):
        """Save annotations to JSON file."""
        try:
            with open(file_path, 'w') as f:
                json.dump(self.annotations, f, indent=2)
                
        except Exception as e:
            self.error_occurred.emit(f"Failed to save annotations to file: {str(e)}")
            
    def load_from_file(self, file_path: Path):
        """Load annotations from JSON file."""
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
                
            # Validate loaded data
            if not isinstance(data, list):
                raise ValueError("Invalid file format: expected list of annotations")
                
            valid_annotations = []
            for annotation in data:
                if validate_annotation_data(annotation):
                    valid_annotations.append(annotation)
                    
            self.annotations = valid_annotations
            self.annotation_loaded.emit(self.annotations)
            
        except Exception as e:
            self.error_occurred.emit(f"Failed to load annotations from file: {str(e)}")
            
    def export_for_preference_learning(self, file_path: Path):
        """Export annotations in format compatible with preference learning pipeline."""
        try:
            # Convert to format expected by utils/prefered_rejected.py
            export_data = []
            
            for annotation in self.annotations:
                export_item = {
                    "note_id": annotation["note_id"],
                    "annotator_id": annotation["annotator_id"],
                    "timestamp": annotation["timestamp"],
                    "human_annotation": annotation["criteria"]
                }
                export_data.append(export_item)
                
            with open(file_path, 'w') as f:
                json.dump(export_data, f, indent=2)
                
        except Exception as e:
            self.error_occurred.emit(f"Failed to export for preference learning: {str(e)}")
            
    def get_annotation_statistics(self) -> Dict[str, Any]:
        """Get statistics about current annotations."""
        if not self.annotations:
            return {"total": 0}
            
        stats = {
            "total": len(self.annotations),
            "annotators": len(set(a["annotator_id"] for a in self.annotations)),
            "criteria_stats": {}
        }
        
        # Calculate criteria statistics
        for criteria_type in ["diagnosis", "treatment", "self_harm"]:
            positive_count = sum(
                1 for a in self.annotations 
                if a["criteria"].get(criteria_type, {}).get("positive", False)
            )
            stats["criteria_stats"][criteria_type] = {
                "positive": positive_count,
                "negative": len(self.annotations) - positive_count,
                "positive_rate": positive_count / len(self.annotations) if self.annotations else 0
            }
            
        return stats
        
    def clear_annotations(self):
        """Clear all annotations."""
        self.annotations.clear()
