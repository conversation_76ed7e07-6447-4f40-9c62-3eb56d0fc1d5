"""
MIMIC Data Loader for HAIIR Application
Handles loading and processing of MIMIC dataset files.
"""

import gzip
import pandas as pd
from pathlib import Path
from PyQt6.QtCore import QObject, QThread, pyqtSignal
import logging
import os
from data.phenotype_manager import Phenotype<PERSON>anager
from data.mimic_cache_manager import MimicCache<PERSON>anager

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configure PyArrow for multi-core usage
def configure_pyarrow_multicore():
    """Configure PyArrow to use multiple cores."""
    try:
        import pyarrow as pa
        # Set PyArrow to use all available CPU cores
        cpu_count = os.cpu_count() or 1
        pa.set_cpu_count(cpu_count)
        logger.info(f"Configured PyArrow to use {cpu_count} CPU cores")
        return True
    except ImportError:
        logger.info("PyArrow not available for multi-core configuration")
        return False
    except Exception as e:
        logger.warning(f"Failed to configure PyArrow multi-core: {e}")
        return False


class MimicDataLoaderWorker(QThread):
    """Worker thread for loading MIMIC data."""

    # Signals
    progress = pyqtSignal(int, int)  # current, total (overall progress)
    file_progress = pyqtSignal(str, int, int)  # filename, current, total (file-specific progress)
    status_update = pyqtSignal(str)  # status message
    finished = pyqtSignal(pd.DataFrame, pd.DataFrame, dict, dict, dict)  # patients_df, notes_df, icd9_dict, icd9_descriptions_dict, patient_codes_dict
    error = pyqtSignal(str)  # error_message
    
    def __init__(self, mimic_path):
        super().__init__()
        self.mimic_path = Path(mimic_path)
        self.cache_manager = MimicCacheManager(mimic_path)
        
    def run(self):
        """Run the data loading process with caching support."""
        try:
            # Configure PyArrow for multi-core usage
            configure_pyarrow_multicore()

            # Check if we can use cached data
            self.status_update.emit("Checking cache validity...")
            if self.cache_manager.can_use_cache():
                self.load_from_cache()
            else:
                self.load_from_raw_files()

        except Exception as e:
            logger.error(f"Error loading MIMIC data: {e}")
            self.error.emit(str(e))

    def load_from_cache(self):
        """Load data from cache files."""
        logger.info("Loading data from cache...")
        self.status_update.emit("Loading data from cache...")

        # Load from cache
        self.progress.emit(1, 5)
        cache_result = self.cache_manager.load_from_cache()

        if cache_result is not None:
            patients_df, notes_df, icd9_dict, icd9_descriptions_dict, patient_codes_dict = cache_result

            # Emit progress updates to simulate loading steps
            self.progress.emit(2, 5)
            self.status_update.emit("Loaded patients from cache...")

            self.progress.emit(3, 5)
            self.status_update.emit("Loaded notes from cache...")

            self.progress.emit(4, 5)
            self.status_update.emit("Loaded ICD9 data from cache...")

            # Emit completion
            self.progress.emit(5, 5)
            self.status_update.emit("Data loading completed (from cache)")
            self.finished.emit(patients_df, notes_df, icd9_dict, icd9_descriptions_dict, patient_codes_dict)

            logger.info(f"Successfully loaded from cache: {len(patients_df)} patients, {len(notes_df)} notes")
        else:
            # Cache failed, fall back to raw files
            logger.warning("Cache loading failed, falling back to raw files")
            self.load_from_raw_files()

    def load_from_raw_files(self):
        """Load data from raw CSV.gz files and optionally save to cache."""
        logger.info("Loading data from raw CSV.gz files...")

        # Load patients data
        self.progress.emit(1, 6)
        self.status_update.emit("Loading patient data...")
        patients_df = self.load_patients()

        # Load notes data
        self.progress.emit(2, 6)
        self.status_update.emit("Loading notes data...")
        notes_df = self.load_notes()

        # Load diagnoses data
        self.progress.emit(3, 6)
        self.status_update.emit("Loading diagnoses data...")
        diagnoses_df = self.load_diagnoses()

        # Load ICD9 diagnosis descriptions
        self.progress.emit(4, 6)
        self.status_update.emit("Loading ICD9 descriptions...")
        icd9_descriptions_df = self.load_icd9_descriptions()

        # Create ICD9 diagnosis dictionary
        self.status_update.emit("Processing ICD9 diagnoses...")
        icd9_dict = self.create_icd9_dictionary(diagnoses_df)

        # Create ICD9 descriptions dictionary
        self.status_update.emit("Processing ICD9 descriptions...")
        icd9_descriptions_dict = self.create_icd9_descriptions_dictionary(icd9_descriptions_df)

        # Create patient codes dictionary for phenotype matching
        self.status_update.emit("Creating patient codes dictionary...")
        patient_codes_dict = self.create_patient_codes_dictionary(diagnoses_df)

        # Save to cache if possible
        self.progress.emit(5, 6)
        self.status_update.emit("Saving data to cache...")
        cache_saved = self.cache_manager.save_to_cache(
            patients_df, notes_df, icd9_dict, icd9_descriptions_dict, patient_codes_dict
        )

        if cache_saved:
            logger.info("Successfully saved data to cache for faster future loading")
        else:
            logger.info("Could not save to cache (no write permissions or other error)")

        # Emit completion
        self.progress.emit(6, 6)
        self.status_update.emit("Data loading completed")
        self.finished.emit(patients_df, notes_df, icd9_dict, icd9_descriptions_dict, patient_codes_dict)
            
    def load_patients(self):
        """Load patients data from PATIENTS.csv.gz."""
        patients_file = self.mimic_path / "PATIENTS.csv.gz"

        if not patients_file.exists():
            raise FileNotFoundError(f"PATIENTS.csv.gz not found at {patients_file}")

        logger.info(f"Loading patients data from {patients_file}")

        # Get file size for progress tracking
        file_size = patients_file.stat().st_size
        self.file_progress.emit("PATIENTS.csv.gz", 0, file_size)

        with gzip.open(patients_file, 'rt') as f:
            try:
                patients_df = pd.read_csv(f, engine='pyarrow')
                logger.info("Using PyArrow engine for PATIENTS.csv.gz")
            except ImportError:
                logger.warning("PyArrow not available, falling back to default engine")
                patients_df = pd.read_csv(f)
            except Exception as e:
                logger.warning(f"PyArrow engine failed ({e}), falling back to default engine")
                f.seek(0)  # Reset file position
                patients_df = pd.read_csv(f)

        # File loading complete
        self.file_progress.emit("PATIENTS.csv.gz", file_size, file_size)
        logger.info(f"Loaded {len(patients_df)} patients")
        return patients_df
        
    def load_notes(self):
        """Load notes data from NOTEEVENTS.csv.gz with real-time progress tracking."""
        notes_file = self.mimic_path / "NOTEEVENTS.csv.gz"

        if not notes_file.exists():
            raise FileNotFoundError(f"NOTEEVENTS.csv.gz not found at {notes_file}")

        logger.info(f"Loading notes data from {notes_file}")

        self.status_update.emit("Loading NOTEEVENTS.csv.gz...")
        self.file_progress.emit("NOTEEVENTS.csv.gz", 0, 100)  # Use percentage-based progress

        chunk_size = 10000
        chunks = []
        rows_loaded = 0

        # Statistical estimation variables
        estimated_total_rows = None
        estimated_total_chunks = None

        # Try PyArrow first for faster loading, fall back to chunked reading if needed
        try:
            # Use PyArrow's native CSV reader which handles quoted fields with newlines better
            import pyarrow.csv as pa_csv
            import pyarrow as pa

            with gzip.open(notes_file, 'rb') as f:  # Open in binary mode for PyArrow
                # Configure PyArrow CSV reader for medical text with embedded newlines
                parse_options = pa_csv.ParseOptions(
                    delimiter=',',
                    quote_char='"',
                    double_quote=True,
                    escape_char=None,
                    newlines_in_values=True  # This is key for handling embedded newlines
                )

                # Read with PyArrow and convert to pandas
                table = pa_csv.read_csv(f, parse_options=parse_options)
                notes_df = table.to_pandas()
                logger.info("Using PyArrow native CSV reader for NOTEEVENTS.csv.gz - loaded in single pass")
                self.file_progress.emit("NOTEEVENTS.csv.gz", 100, 100)

        except (ImportError, Exception) as e:
            if isinstance(e, ImportError):
                logger.warning("PyArrow not available, using chunked reading with default engine")
            else:
                logger.warning(f"PyArrow native CSV reader failed ({e}), trying pandas PyArrow engine")

                # Try pandas PyArrow engine as fallback
                try:
                    with gzip.open(notes_file, 'rt') as f:
                        notes_df = pd.read_csv(f, engine='pyarrow')
                        logger.info("Using pandas PyArrow engine for NOTEEVENTS.csv.gz - loaded in single pass")
                        self.file_progress.emit("NOTEEVENTS.csv.gz", 100, 100)
                except Exception as e2:
                    logger.warning(f"Pandas PyArrow engine also failed ({e2}), using chunked reading with default engine")

            # Fall back to chunked reading with default engine
            with gzip.open(notes_file, 'rt') as f:
                chunk_reader = pd.read_csv(f, chunksize=chunk_size)

                for chunk_num, chunk in enumerate(chunk_reader):
                    chunks.append(chunk)
                    rows_loaded += len(chunk)

                    # Statistical estimation after first few chunks
                    if chunk_num == 4:  # After 5 chunks (50k rows), make initial estimate
                        # Estimate based on typical NOTEEVENTS size (around 2M rows)
                        # Use a conservative estimate based on chunk processing rate
                        avg_rows_per_chunk = rows_loaded / (chunk_num + 1)
                        estimated_total_rows = int(avg_rows_per_chunk * 200)  # Estimate ~200 chunks total
                        estimated_total_chunks = estimated_total_rows // chunk_size
                        logger.info(f"Estimated {estimated_total_rows:,} total rows based on first {rows_loaded:,} rows")

                    # Update progress based on chunk processing
                    if estimated_total_chunks:
                        # Use chunk-based progress estimation
                        estimated_pct = min(95, int((chunk_num / estimated_total_chunks) * 100))
                        self.file_progress.emit("NOTEEVENTS.csv.gz", estimated_pct, 100)
                        self.status_update.emit(f"Loading NOTEEVENTS.csv.gz... {rows_loaded:,} rows (~{estimated_pct}% estimated)")
                    else:
                        # Before estimation kicks in, use a simple chunk-based progress
                        initial_pct = min(20, chunk_num * 4)  # Slow initial progress
                        self.file_progress.emit("NOTEEVENTS.csv.gz", initial_pct, 100)
                        self.status_update.emit(f"Loading NOTEEVENTS.csv.gz... {rows_loaded:,} rows loaded")

                    # Allow thread to be interrupted
                    if self.isInterruptionRequested():
                        return pd.DataFrame()

            # File loading complete - ensure we show 100%
            self.file_progress.emit("NOTEEVENTS.csv.gz", 100, 100)
            notes_df = pd.concat(chunks, ignore_index=True)

        logger.info(f"Loaded {len(notes_df)} notes (actual total)")
        return notes_df

    def load_diagnoses(self):
        """Load diagnoses data from DIAGNOSES_ICD.csv.gz."""
        diagnoses_file = self.mimic_path / "DIAGNOSES_ICD.csv.gz"

        if not diagnoses_file.exists():
            raise FileNotFoundError(f"DIAGNOSES_ICD.csv.gz not found at {diagnoses_file}")

        logger.info(f"Loading diagnoses data from {diagnoses_file}")

        # Get file size for progress tracking
        file_size = diagnoses_file.stat().st_size
        self.file_progress.emit("DIAGNOSES_ICD.csv.gz", 0, file_size)

        with gzip.open(diagnoses_file, 'rt') as f:
            try:
                diagnoses_df = pd.read_csv(f, engine='pyarrow')
                logger.info("Using PyArrow engine for DIAGNOSES_ICD.csv.gz")
            except ImportError:
                logger.warning("PyArrow not available, falling back to default engine")
                diagnoses_df = pd.read_csv(f)
            except Exception as e:
                logger.warning(f"PyArrow engine failed ({e}), falling back to default engine")
                f.seek(0)  # Reset file position
                diagnoses_df = pd.read_csv(f)

        # File loading complete
        self.file_progress.emit("DIAGNOSES_ICD.csv.gz", file_size, file_size)
        logger.info(f"Loaded {len(diagnoses_df)} diagnoses")
        return diagnoses_df

    def create_icd9_dictionary(self, diagnoses_df):
        """Create a dictionary keyed by (SUBJECT_ID, HADM_ID) with ICD9_CODE lists as values."""
        icd9_dict = {}

        if diagnoses_df.empty:
            return icd9_dict

        # Group by SUBJECT_ID and HADM_ID, then collect ICD9_CODE values
        for _, row in diagnoses_df.iterrows():
            subject_id = row.get('SUBJECT_ID')
            hadm_id = row.get('HADM_ID')
            icd9_code = row.get('ICD9_CODE')

            # Skip rows with missing key values
            if pd.isna(subject_id) or pd.isna(hadm_id):
                continue

            # Create key tuple
            key = (int(subject_id), int(hadm_id))

            # Initialize list if key doesn't exist
            if key not in icd9_dict:
                icd9_dict[key] = []

            # Add ICD9 code if it's not null
            if pd.notna(icd9_code) and str(icd9_code) != 'nan':
                icd9_dict[key].append(str(icd9_code))

        logger.info(f"Created ICD9 dictionary with {len(icd9_dict)} admission entries")
        return icd9_dict

    def load_icd9_descriptions(self):
        """Load ICD9 diagnosis descriptions from D_ICD_DIAGNOSES.csv.gz."""
        descriptions_file = self.mimic_path / "D_ICD_DIAGNOSES.csv.gz"

        if not descriptions_file.exists():
            raise FileNotFoundError(f"D_ICD_DIAGNOSES.csv.gz not found at {descriptions_file}")

        logger.info(f"Loading ICD9 descriptions from {descriptions_file}")

        # Get file size for progress tracking
        file_size = descriptions_file.stat().st_size
        self.file_progress.emit("D_ICD_DIAGNOSES.csv.gz", 0, file_size)

        with gzip.open(descriptions_file, 'rt') as f:
            try:
                descriptions_df = pd.read_csv(f, engine='pyarrow')
                logger.info("Using PyArrow engine for D_ICD_DIAGNOSES.csv.gz")
            except ImportError:
                logger.warning("PyArrow not available, falling back to default engine")
                descriptions_df = pd.read_csv(f)
            except Exception as e:
                logger.warning(f"PyArrow engine failed ({e}), falling back to default engine")
                f.seek(0)  # Reset file position
                descriptions_df = pd.read_csv(f)

        # File loading complete
        self.file_progress.emit("D_ICD_DIAGNOSES.csv.gz", file_size, file_size)
        logger.info(f"Loaded {len(descriptions_df)} ICD9 descriptions")
        return descriptions_df

    def create_icd9_descriptions_dictionary(self, descriptions_df):
        """Create a dictionary keyed by ICD9_CODE with SHORT_TITLE as values."""
        descriptions_dict = {}

        if descriptions_df.empty:
            return descriptions_dict

        # Create dictionary mapping ICD9_CODE to SHORT_TITLE
        for _, row in descriptions_df.iterrows():
            icd9_code = row.get('ICD9_CODE')
            short_title = row.get('SHORT_TITLE')

            # Skip rows with missing key values
            if pd.isna(icd9_code):
                continue

            # Add to dictionary
            if pd.notna(short_title) and str(short_title) != 'nan':
                descriptions_dict[str(icd9_code)] = str(short_title)
            else:
                descriptions_dict[str(icd9_code)] = "No description available"

        logger.info(f"Created ICD9 descriptions dictionary with {len(descriptions_dict)} entries")
        return descriptions_dict

    def create_patient_codes_dictionary(self, diagnoses_df):
        """Create a dictionary keyed by SUBJECT_ID with sets of ICD9_CODE values."""
        patient_codes_dict = {}

        if diagnoses_df.empty:
            return patient_codes_dict

        # Group by SUBJECT_ID and collect unique ICD9_CODE values
        for _, row in diagnoses_df.iterrows():
            subject_id = row.get('SUBJECT_ID')
            icd9_code = row.get('ICD9_CODE')

            # Skip rows with missing key values
            if pd.isna(subject_id):
                continue

            subject_id = int(subject_id)

            # Initialize set if key doesn't exist
            if subject_id not in patient_codes_dict:
                patient_codes_dict[subject_id] = set()

            # Add ICD9 code if it's not null
            if pd.notna(icd9_code) and str(icd9_code) != 'nan':
                patient_codes_dict[subject_id].add(str(icd9_code))

        logger.info(f"Created patient codes dictionary with {len(patient_codes_dict)} patients")
        return patient_codes_dict


class MimicDataLoader(QObject):
    """Main data loader for MIMIC dataset."""

    # Signals
    loading_started = pyqtSignal()
    loading_finished = pyqtSignal(pd.DataFrame, pd.DataFrame, dict, dict, object)  # patients_df, notes_df, icd9_dict, icd9_descriptions_dict, phenotype_manager
    loading_progress = pyqtSignal(int, int)  # current, total (overall progress)
    file_progress = pyqtSignal(str, int, int)  # filename, current, total (file-specific progress)
    status_update = pyqtSignal(str)  # status message
    loading_error = pyqtSignal(str)  # error_message

    def __init__(self):
        super().__init__()

        # Find MIMIC data path (relative to HAIIR folder)
        self.mimic_path = Path(__file__).parent.parent.parent / "mimic"

        # Data storage
        self.patients_df = None
        self.notes_df = None
        self.icd9_dict = None
        self.icd9_descriptions_dict = None

        # Phenotype manager
        self.phenotype_manager = PhenotypeManager()

        # Worker thread
        self.worker = None
        
    def load_data_async(self):
        """Load data asynchronously."""
        if self.worker is not None and self.worker.isRunning():
            logger.warning("Data loading already in progress")
            return
            
        # Check if MIMIC path exists
        if not self.mimic_path.exists():
            self.loading_error.emit(f"MIMIC data directory not found: {self.mimic_path}")
            return
            
        # Start loading
        self.loading_started.emit()
        
        # Create and start worker thread
        self.worker = MimicDataLoaderWorker(self.mimic_path)
        self.worker.progress.connect(self.loading_progress)
        self.worker.file_progress.connect(self.file_progress)
        self.worker.status_update.connect(self.status_update)
        self.worker.finished.connect(self.on_loading_finished)
        self.worker.error.connect(self.loading_error)
        self.worker.start()
        
    def on_loading_finished(self, patients_df, notes_df, icd9_dict, icd9_descriptions_dict, patient_codes_dict):
        """Handle loading completion."""
        self.patients_df = patients_df
        self.notes_df = notes_df
        self.icd9_dict = icd9_dict
        self.icd9_descriptions_dict = icd9_descriptions_dict

        # Set up phenotype manager with patient codes
        self.phenotype_manager.set_patient_codes_dict(patient_codes_dict)

        # Clean up worker
        if self.worker:
            self.worker.deleteLater()
            self.worker = None

        self.loading_finished.emit(patients_df, notes_df, icd9_dict, icd9_descriptions_dict, self.phenotype_manager)
        
    def get_patient_notes(self, subject_id):
        """Get all notes for a specific patient."""
        if self.notes_df is None:
            return pd.DataFrame()
            
        patient_notes = self.notes_df[self.notes_df['SUBJECT_ID'] == subject_id].copy()
        
        # Sort by chart date/time
        if 'CHARTDATE' in patient_notes.columns:
            patient_notes = patient_notes.sort_values('CHARTDATE', na_position='last')
            
        return patient_notes
        
    def get_patient_info(self, subject_id):
        """Get patient information."""
        if self.patients_df is None:
            return None
            
        patient_info = self.patients_df[self.patients_df['SUBJECT_ID'] == subject_id]
        if patient_info.empty:
            return None
            
        return patient_info.iloc[0].to_dict()
        
    def get_note_by_id(self, row_id):
        """Get a specific note by ROW_ID."""
        if self.notes_df is None:
            return None
            
        note = self.notes_df[self.notes_df['ROW_ID'] == row_id]
        if note.empty:
            return None
            
        return note.iloc[0].to_dict()
        
    def get_icd9_codes(self, subject_id, hadm_id):
        """Get ICD9 codes for a specific patient admission."""
        if self.icd9_dict is None:
            return []

        key = (int(subject_id), int(hadm_id))
        return self.icd9_dict.get(key, [])

    def get_icd9_description(self, icd9_code):
        """Get description for a specific ICD9 code."""
        if self.icd9_descriptions_dict is None:
            return "Descriptions not loaded"

        return self.icd9_descriptions_dict.get(str(icd9_code), "Description not found")

    def get_icd9_codes_with_descriptions(self, subject_id, hadm_id):
        """Get ICD9 codes with their descriptions for a specific patient admission."""
        codes = self.get_icd9_codes(subject_id, hadm_id)
        codes_with_descriptions = []

        for code in codes:
            description = self.get_icd9_description(code)
            codes_with_descriptions.append((code, description))

        return codes_with_descriptions

    def is_data_loaded(self):
        """Check if data is loaded."""
        return (self.patients_df is not None and
                self.notes_df is not None and
                self.icd9_dict is not None and
                self.icd9_descriptions_dict is not None)
        
    def get_data_summary(self):
        """Get summary of loaded data."""
        if not self.is_data_loaded():
            return "No data loaded"
            
        patients_count = len(self.patients_df)
        notes_count = len(self.notes_df)
        
        # Get unique categories
        categories = []
        if 'CATEGORY' in self.notes_df.columns:
            categories = self.notes_df['CATEGORY'].dropna().unique()
            
        return {
            'patients_count': patients_count,
            'notes_count': notes_count,
            'categories': list(categories)
        }

    def get_cache_info(self):
        """Get information about the cache status."""
        if hasattr(self, 'worker') and self.worker:
            return self.worker.cache_manager.get_cache_info()
        else:
            # Create temporary cache manager for info
            from data.mimic_cache_manager import MimicCacheManager
            temp_cache_manager = MimicCacheManager(self.mimic_path)
            return temp_cache_manager.get_cache_info()

    def clear_cache(self):
        """Clear all cache files."""
        if hasattr(self, 'worker') and self.worker:
            self.worker.cache_manager.clear_cache()
        else:
            # Create temporary cache manager for clearing
            from data.mimic_cache_manager import MimicCacheManager
            temp_cache_manager = MimicCacheManager(self.mimic_path)
            temp_cache_manager.clear_cache()
