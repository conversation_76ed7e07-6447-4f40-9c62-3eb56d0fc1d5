"""
AI Results Loader for HAIIR Application
Handles loading and parsing of AI-generated analysis results.
"""

import json
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Optional
from PyQt6.QtCore import QObject, pyqtSignal
import sys

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

try:
    from utils.prompt_schema import DiagnosisSchema, TreatmentSchema, SelfHarmSchema
    from utils.criteria import CriteriaType
except ImportError:
    # Fallback for when utils module is not available
    DiagnosisSchema = None
    TreatmentSchema = None
    SelfHarmSchema = None
    CriteriaType = None


class AIResultsLoader(QObject):
    """Loads and manages AI analysis results."""
    
    # Signals
    results_loaded = pyqtSignal(dict)  # results_data
    error_occurred = pyqtSignal(str)  # error_message
    
    def __init__(self):
        super().__init__()
        self.results_data = {}
        
    def load_results_from_pickle(self, file_path: Path):
        """Load AI results from pickle file (from criteria_combine workflow)."""
        try:
            df = pd.read_pickle(file_path)
            
            # Convert to internal format
            results = {}
            for _, row in df.iterrows():
                note_id = row.get('ROW_ID') or row.get('index', 0)
                
                results[note_id] = {
                    'note_text': row.get('note', ''),
                    'label': row.get('label', 0),
                    'diagnosis': self._parse_ai_result(row.get('diagnosis', [])),
                    'treatment': self._parse_ai_result(row.get('treatment', [])),
                    'self_harm': self._parse_ai_result(row.get('self_harm', []))
                }
                
            self.results_data = results
            self.results_loaded.emit(results)
            
        except Exception as e:
            self.error_occurred.emit(f"Failed to load AI results from pickle: {str(e)}")
            
    def load_results_from_json(self, file_path: Path):
        """Load AI results from JSON file."""
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
                
            # Validate and store results
            if isinstance(data, dict):
                self.results_data = data
            elif isinstance(data, list):
                # Convert list to dict with note_id as key
                self.results_data = {
                    item.get('note_id', i): item 
                    for i, item in enumerate(data)
                }
            else:
                raise ValueError("Invalid JSON format")
                
            self.results_loaded.emit(self.results_data)
            
        except Exception as e:
            self.error_occurred.emit(f"Failed to load AI results from JSON: {str(e)}")
            
    def _parse_ai_result(self, result_data):
        """Parse AI result data into standardized format."""
        if not result_data:
            return None
            
        # Handle list of results (multiple model outputs)
        if isinstance(result_data, list):
            if not result_data:
                return None
            # Take the first result for now
            result_data = result_data[0]
            
        # Handle Pydantic schema objects
        if hasattr(result_data, 'model_dump'):
            return result_data.model_dump()
        elif hasattr(result_data, 'dict'):
            return result_data.dict()
        elif isinstance(result_data, dict):
            return result_data
        else:
            # Try to parse as string representation
            try:
                return eval(str(result_data))
            except:
                return {"raw_data": str(result_data)}
                
    def get_ai_result(self, note_id: int) -> Optional[Dict[str, Any]]:
        """Get AI results for a specific note."""
        return self.results_data.get(note_id)
        
    def get_criteria_result(self, note_id: int, criteria_type: str) -> Optional[Dict[str, Any]]:
        """Get AI result for a specific criteria."""
        note_results = self.get_ai_result(note_id)
        if note_results:
            return note_results.get(criteria_type)
        return None
        
    def compare_with_human_annotation(self, note_id: int, human_annotation: Dict[str, Any]) -> Dict[str, Any]:
        """Compare AI results with human annotation."""
        ai_results = self.get_ai_result(note_id)
        if not ai_results:
            return {"error": "No AI results found for this note"}
            
        comparison = {
            "note_id": note_id,
            "criteria_comparison": {}
        }
        
        for criteria_type in ["diagnosis", "treatment", "self_harm"]:
            ai_result = ai_results.get(criteria_type, {})
            human_result = human_annotation.get("criteria", {}).get(criteria_type, {})
            
            # Extract key values for comparison
            ai_positive = self._extract_positive_determination(ai_result)
            ai_confidence = self._extract_confidence(ai_result)
            
            human_positive = human_result.get("positive", False)
            human_confidence = human_result.get("confidence", 0.0)
            
            comparison["criteria_comparison"][criteria_type] = {
                "ai": {
                    "positive": ai_positive,
                    "confidence": ai_confidence
                },
                "human": {
                    "positive": human_positive,
                    "confidence": human_confidence
                },
                "agreement": ai_positive == human_positive,
                "confidence_diff": abs(ai_confidence - human_confidence)
            }
            
        return comparison
        
    def _extract_positive_determination(self, ai_result: Dict[str, Any]) -> bool:
        """Extract positive determination from AI result."""
        if not ai_result:
            return False
            
        # Try different possible keys
        conclusion = ai_result.get("conclusion", {})
        if isinstance(conclusion, dict):
            # Check for various possible keys
            for key in ["has_depression", "has_treatment", "has_self_harm"]:
                if key in conclusion:
                    value = conclusion[key]
                    if isinstance(value, str):
                        return value.lower() in ["yes", "true", "positive"]
                    return bool(value)
                    
        return False
        
    def _extract_confidence(self, ai_result: Dict[str, Any]) -> float:
        """Extract confidence score from AI result."""
        if not ai_result:
            return 0.0
            
        conclusion = ai_result.get("conclusion", {})
        if isinstance(conclusion, dict):
            # Check for various possible confidence keys
            for key in ["confidence_depression", "confidence_treatment", "confidence_self_harm", "confidence"]:
                if key in conclusion:
                    try:
                        return float(conclusion[key])
                    except (ValueError, TypeError):
                        continue
                        
        return 0.0
        
    def get_results_summary(self) -> Dict[str, Any]:
        """Get summary of loaded AI results."""
        if not self.results_data:
            return {"total": 0}
            
        summary = {
            "total_notes": len(self.results_data),
            "criteria_stats": {}
        }
        
        for criteria_type in ["diagnosis", "treatment", "self_harm"]:
            positive_count = 0
            total_with_results = 0
            
            for note_id, results in self.results_data.items():
                ai_result = results.get(criteria_type)
                if ai_result:
                    total_with_results += 1
                    if self._extract_positive_determination(ai_result):
                        positive_count += 1
                        
            summary["criteria_stats"][criteria_type] = {
                "total_with_results": total_with_results,
                "positive": positive_count,
                "negative": total_with_results - positive_count,
                "positive_rate": positive_count / total_with_results if total_with_results > 0 else 0
            }
            
        return summary
        
    def clear_results(self):
        """Clear all loaded results."""
        self.results_data.clear()
