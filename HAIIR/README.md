# HAIIR (Human-AI Interactive Review)

A PyQt6 application for interactive medical chart review with AI-assisted annotation capabilities.

## Overview

HAIIR provides a graphical user interface for the AI-based chart review system, integrating with the existing codebase's three criteria analysis (diagnosis, treatment, self-harm) and supporting the current workflow using Ollama models with structured JSON outputs.

## Current Status

✅ **Phase 1 Complete**: Comprehensive medical chart review foundation
- Patient data viewer with sortable table and search functionality
- Advanced notes metadata table with ICD9 diagnosis codes
- Real-time ICD9 diagnosis descriptions for medical context
- Full text note viewing with category filtering
- PyArrow-accelerated data loading with multi-core utilization
- Three-panel responsive UI with professional styling
- Integration-ready architecture for future phases

🔄 **Phase 2 Planned**: Human annotation interface
🔄 **Phase 3 Planned**: AI integration and comparison tools

> For detailed project status, requirements, and technical documentation, see [PROJECT_STATUS.md](PROJECT_STATUS.md)

## Quick Start

### Prerequisites
- Python 3.11 (compatible with existing conda environment)
- MIMIC-III dataset files in `../mimic/` directory:
  - PATIENTS.csv.gz (patient demographics)
  - NOTEEVENTS.csv.gz (medical notes)
  - DIAGNOSES_ICD.csv.gz (ICD9 diagnosis codes)
  - D_ICD_DIAGNOSES.csv.gz (ICD9 code descriptions)

### Installation

#### Automated Installation (Recommended)
```bash
cd HAIIR
python install.py  # Handles all dependencies and verification
python main.py     # Launch application
```

#### Manual Installation
```bash
cd HAIIR
conda activate .conda  # Use existing environment
pip install -r requirements-minimal.txt  # Phase 1 dependencies only
python test_basic.py   # Verify installation
python main.py         # Launch application
```

**Note**: PyQt6 should be installed via conda for best compatibility:
```bash
conda install pyqt=6.7.1
```

## Usage

### Basic Workflow
1. **Launch**: `python main.py`
2. **Data Loading**: Automatic loading of 4 MIMIC files with PyArrow acceleration and progress tracking
3. **Browse Patients**: Search and select patients in the left panel
4. **View Notes Metadata**: Comprehensive notes table with ICD9 diagnosis codes appears in top panel
5. **Select Note**: Click on any note row to view content and ICD9 descriptions
6. **Review Content**: Note text appears in bottom-left, ICD9 descriptions in bottom-right
7. **Filter**: Use category dropdown to filter notes by type (Discharge, Nursing, etc.)

### Data Directory Structure
```
AI-chart-review/
├── mimic/
│   ├── PATIENTS.csv.gz         # Required: Patient demographics
│   ├── NOTEEVENTS.csv.gz       # Required: Medical notes
│   ├── DIAGNOSES_ICD.csv.gz    # Required: ICD9 diagnosis codes
│   └── D_ICD_DIAGNOSES.csv.gz  # Required: ICD9 code descriptions
└── HAIIR/
    ├── main.py
    └── ...
```

## Integration

HAIIR integrates with the existing AI chart review system:
- **Data Compatibility**: Reads MIMIC dataset files and train/test split formats
- **ICD9 Integration**: Provides real-time diagnosis code lookup and descriptions
- **Schema Integration**: Compatible with existing Pydantic schemas and criteria types
- **Performance**: PyArrow acceleration with multi-core utilization
- **Future AI Integration**: Ready for Phase 3 model inference and prompt management

## Troubleshooting

### Common Issues

**"MIMIC data directory not found"**
- Ensure MIMIC data files are in `../mimic/` relative to HAIIR folder
- Check that all 4 required files exist: PATIENTS.csv.gz, NOTEEVENTS.csv.gz, DIAGNOSES_ICD.csv.gz, D_ICD_DIAGNOSES.csv.gz

**"Failed to load MIMIC data"**
- Verify file permissions and available memory (NOTEEVENTS.csv.gz is large)
- Ensure files are not corrupted

**"Error importing UI components"**
- Verify PyQt6 is installed: `conda install pyqt=6.7.1`
- Check Python version compatibility (3.11 recommended)

### Performance Notes
- PyArrow acceleration significantly improves loading speed for large datasets
- Multi-core utilization (up to 48 cores detected) for CSV parsing
- Intelligent fallback to chunked reading if PyArrow encounters issues
- Use `python test_basic.py` to verify basic functionality

## Support

For detailed technical documentation, requirements, and development information, see [PROJECT_STATUS.md](PROJECT_STATUS.md).
