#!/usr/bin/env python3
"""
HAIIR (Human-AI Interactive Review) Application
Main entry point for the PyQt6 GUI application.
"""

import sys
import os
from pathlib import Path
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QIcon

# Add parent directory to path to import utils
sys.path.append(str(Path(__file__).parent.parent))

try:
    from ui.main_window import MainWindow
except ImportError as e:
    print(f"Error importing UI components: {e}")
    sys.exit(1)


class HAIIRApplication(QApplication):
    """Main HAIIR application class."""
    
    def __init__(self, argv):
        super().__init__(argv)
        
        # Set application properties
        self.setApplicationName("HAIIR")
        self.setApplicationDisplayName("Human-AI Interactive Review")
        self.setApplicationVersion("1.0.0")
        self.setOrganizationName("AI Chart Review")
        
        # High DPI scaling is enabled by default in PyQt6
        
        # Load application stylesheet
        self.load_stylesheet()
        
        # Initialize main window
        self.main_window = None
        
    def load_stylesheet(self):
        """Load the application stylesheet."""
        style_path = Path(__file__).parent / "resources" / "styles.qss"
        if style_path.exists():
            with open(style_path, 'r') as f:
                self.setStyleSheet(f.read())
    
    def initialize(self):
        """Initialize the application and show main window."""
        try:
            self.main_window = MainWindow()
            self.main_window.show()
            return True
        except Exception as e:
            QMessageBox.critical(
                None,
                "Initialization Error",
                f"Failed to initialize HAIIR application:\n{str(e)}"
            )
            return False


def main():
    """Main entry point."""
    # Create application
    app = HAIIRApplication(sys.argv)
    
    # Initialize and show main window
    if not app.initialize():
        sys.exit(1)
    
    # Run application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
