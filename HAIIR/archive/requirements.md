# HAIIR (Human-AI Interactive Review) Application

## Overview
HAIIR is a standalone PyQt6 application that provides a graphical user interface for the AI-based chart review system. It integrates with the existing codebase's three criteria analysis (diagnosis, treatment, self-harm) and supports the current workflow using Ollama models with structured JSON outputs.

## Core Requirements

### 1. Chart Display and Annotation Interface
- **Patient Chart Display**: Display individual patient medical charts from MIMIC dataset format with proper text formatting and scrolling
- **Human Annotation Interface**: Intuitive interface for marking charts as positive/negative for three criteria:
  - Diagnosis of depression (excluding bipolar depression)
  - Treatment for depression (medications, therapy, interventions)
  - Evidence of self-harm (suicidal ideation, self-harm behaviors)
- **Confidence Scoring**: Support 0.0-1.0 scale confidence scoring for human annotations
- **Data Persistence**: Save/load human annotation datasets in JSON format compatible with preference learning pipeline
- **Metadata Tracking**: Annotation timestamp tracking and annotator identification

### 2. AI Results Visualization
- **JSON Display**: Read and display JSON annotations generated by LLM using existing Pydantic schemas
- **Structured Output Visualization**: Display chain of thought reasoning, conclusions, confidence scores, and highlighted text extracts
- **Comparison Views**: Side-by-side human vs AI annotations with:
  - Blinded mode: Hide AI results during human annotation
  - Non-blinded mode: Show AI results alongside human annotations
- **Disagreement Analysis**: Visualization of disagreements between human and AI annotations

### 3. Prompt Management System
- **Prompt Editor**: Display and edit current prompts from `utils/prompt.py`
- **Configuration Management**: JSON-based prompt configuration integrating with `get_prompts()` function
- **Version Control**: Git-style prompt versioning with tracking and rollback capabilities
- **A/B Testing**: Interface for comparing prompt performance
- **Validation**: Validate prompt changes against existing Pydantic schemas
- **Export**: Export prompts in format expected by Ollama/criteria_combine workflow
- **Preview**: Show how prompts will appear to the model

### 4. Integration Requirements
- **Utils Module Integration**: Interface with existing utils module without modification:
  - Import `CriteriaType` enum from `utils/criteria.py`
  - Load and validate against schemas from `utils/prompt_schema.py`
  - Optionally trigger inference using `utils/model_response.py`
- **Dataset Support**: Load datasets from train/test split CSV format in `datasets/train_test_split/`
- **Model Pipeline**: Enable triggering existing model inference pipeline from GUI
- **Export Functionality**: Export preference learning datasets for `utils/prefered_rejected.py`
- **MIMIC Data**: Read compressed MIMIC data files from `mimic/` directory

## Technical Specifications

### Framework and Environment
- **GUI Framework**: PyQt6 with modern styling
- **Python Environment**: Compatible with existing Python 3.11 conda environment
- **Schema Compatibility**: Support JSON schema formats used by DiagnosisSchema, TreatmentSchema, SelfHarmSchema
- **Standalone Design**: Run independently while leveraging existing codebase through imports
- **Non-Invasive**: Do not modify any existing codebase files

### Dependencies
- **Data Processing**: pandas for data manipulation
- **File Handling**: gzip for reading compressed CSV files
- **Error Handling**: Proper error handling for file I/O and data validation
- **GUI Components**: PyQt6 widgets for modern interface design

## Implementation Phases

### Phase 1: Minimal Prototype
1. Create HAIIR folder structure with requirements.md
2. Build basic PyQt6 application with tabular view of patients from PATIENTS.csv.gz
3. Display ROW_ID, SUBJECT_ID, GENDER, DOB fields in sortable table
4. Implement patient selection and note viewing interface for NOTEEVENTS.csv.gz
5. Link patients to their notes via SUBJECT_ID with proper filtering
6. Add scrollable text display for individual medical notes with proper formatting

### Phase 2: Annotation Interface
1. Add human annotation interface for the three criteria
2. Implement confidence scoring widgets
3. Add save/load functionality for annotation datasets
4. Create blinded annotation mode

### Phase 3: AI Integration
1. Add AI results visualization
2. Implement side-by-side comparison views
3. Add prompt management system
4. Integrate with existing model inference pipeline

## File Structure
```
HAIIR/
├── requirements.md
├── main.py                 # Application entry point
├── ui/
│   ├── __init__.py
│   ├── main_window.py      # Main application window
│   ├── patient_table.py    # Patient list table widget
│   ├── chart_viewer.py     # Medical chart display widget
│   ├── annotation_panel.py # Human annotation interface
│   └── ai_results_panel.py # AI results visualization
├── data/
│   ├── __init__.py
│   ├── mimic_loader.py     # MIMIC dataset loading utilities
│   ├── annotation_manager.py # Human annotation data management
│   └── ai_results_loader.py # AI results loading and parsing
├── utils/
│   ├── __init__.py
│   ├── config.py           # Application configuration
│   └── validators.py       # Data validation utilities
└── resources/
    ├── styles.qss          # PyQt6 stylesheets
    └── icons/              # Application icons
```

## Data Models

### Patient Record
```python
{
    "row_id": int,
    "subject_id": int,
    "gender": str,
    "dob": str,
    "dod": str,
    "dod_hosp": str,
    "dod_ssn": str,
    "expire_flag": str
}
```

### Note Event
```python
{
    "row_id": int,
    "subject_id": int,
    "hadm_id": int,
    "chartdate": str,
    "charttime": str,
    "storetime": str,
    "category": str,
    "description": str,
    "cgid": int,
    "iserror": str,
    "text": str
}
```

### Human Annotation
```python
{
    "note_id": int,
    "annotator_id": str,
    "timestamp": str,
    "criteria": {
        "diagnosis": {
            "positive": bool,
            "confidence": float,
            "evidence": str,
            "notes": str
        },
        "treatment": {
            "positive": bool,
            "confidence": float,
            "evidence": str,
            "notes": str
        },
        "self_harm": {
            "positive": bool,
            "confidence": float,
            "evidence": str,
            "notes": str
        }
    }
}
```

## Deliverables

### Phase 1 Deliverables
1. HAIIR subfolder with proper project structure
2. Comprehensive requirements.md file with technical specifications
3. Minimal functioning prototype with:
   - Reading compressed MIMIC data files (PATIENTS.csv.gz, NOTEEVENTS.csv.gz)
   - Tabular patient view with scrollable chart text cells
   - Patient-to-notes relationship display
   - Basic navigation and data viewing functionality

The prototype demonstrates core data reading and display capabilities before adding annotation and AI integration features.
