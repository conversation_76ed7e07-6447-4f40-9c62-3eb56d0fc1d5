# HAIIR Project Status & Technical Documentation

## Project Overview

HAIIR (Human-AI Interactive Review) is a standalone PyQt6 application that provides a graphical user interface for the AI-based chart review system. It integrates with the existing codebase's three criteria analysis (diagnosis, treatment, self-harm) and supports the current workflow using Ollama models with structured JSON outputs.

## Current Implementation Status

### ✅ Phase 1: Minimal Prototype (COMPLETED)

**Core Functionality Delivered:**
- **MIMIC Data Integration**: Asynchronous loading of compressed PATIENTS.csv.gz, NOTEEVENTS.csv.gz, DIAGNOSES_ICD.csv.gz, and D_ICD_DIAGNOSES.csv.gz
- **Patient Management Interface**: Sortable table with search by patient ID and gender filtering
- **Medical Notes Viewer**: Comprehensive notes metadata table with ICD9 diagnosis codes and full text display
- **ICD9 Diagnosis Descriptions**: Real-time display of diagnosis code descriptions when notes are selected
- **Modern UI**: PyQt6 framework with professional three-panel layout and responsive design
- **Enhanced Progress Tracking**: Dual progress bars with real-time chunk-based progress reporting
- **Py<PERSON>rrow Acceleration**: Multi-core CSV loading with intelligent fallback mechanisms

**Technical Achievements:**
- Non-blocking data loading with comprehensive error handling and PyArrow acceleration
- Memory-efficient chunked processing for large datasets with multi-core utilization
- Advanced UI layout with three-panel design (metadata table, note content, ICD9 descriptions)
- Intelligent data type handling for proper numeric sorting in tables
- Real-time ICD9 diagnosis code lookup and description display
- Integration-ready architecture for future phases
- Comprehensive test coverage and documentation

### 🔄 Phase 2: Human Annotation Interface (PLANNED)

**Requirements:**
- Interactive annotation interface for three criteria (diagnosis, treatment, self-harm)
- Confidence scoring widgets (0.0-1.0 scale)
- Blinded annotation mode (hide AI results during human annotation)
- Annotation persistence in JSON format compatible with preference learning pipeline
- Metadata tracking (timestamp, annotator identification)

### 🔄 Phase 3: AI Integration (PLANNED)

**Requirements:**
- AI results visualization with structured output display
- Side-by-side human vs AI comparison views
- Prompt management system with version control
- Integration with existing model inference pipeline
- Disagreement analysis and export functionality

## Technical Specifications

### Framework and Dependencies
- **GUI Framework**: PyQt6 6.6.1+ with modern styling
- **Python Environment**: Compatible with Python 3.11 conda environment
- **Data Processing**: pandas 2.1.4 for data manipulation with PyArrow acceleration
- **File Handling**: gzip for reading compressed CSV files
- **Performance**: PyArrow engine for accelerated CSV loading (with fallback to default engine)
- **Schema Compatibility**: Support for DiagnosisSchema, TreatmentSchema, SelfHarmSchema

### Project Structure
```
HAIIR/
├── main.py                 # Application entry point
├── install.py              # Automated installation script
├── test_basic.py          # Basic functionality tests
├── requirements.txt       # Full dependencies
├── requirements-minimal.txt # Phase 1 dependencies only
├── PROJECT_STATUS.md      # This file
├── README.md              # User documentation
├── ui/                    # User interface components
│   ├── main_window.py     # Main application window
│   ├── patient_table.py   # Patient list with search/filter
│   ├── chart_viewer.py    # Medical notes display
│   ├── annotation_panel.py # Human annotation (Phase 2 ready)
│   └── ai_results_panel.py # AI results display (Phase 3 ready)
├── data/                  # Data management
│   ├── mimic_loader.py    # Asynchronous MIMIC data loading
│   ├── annotation_manager.py # Human annotation persistence
│   └── ai_results_loader.py # AI results integration
├── utils/                 # Utilities and configuration
│   ├── config.py          # Application settings
│   └── validators.py      # Data validation functions
└── resources/
    └── styles.qss         # Modern PyQt6 styling
```

### Integration Architecture

**Existing Codebase Integration:**
- Non-invasive design: No modifications to existing codebase required
- Import compatibility: Ready to import from `utils/` modules
- Schema compatibility: Prepared for existing Pydantic schemas
- Data format compatibility: Supports existing CSV and JSON formats

**Integration Points:**
- Import `CriteriaType` enum from `utils/criteria.py`
- Load and validate against schemas from `utils/prompt_schema.py`
- Interface with `utils/model_response.py` for inference (Phase 3)
- Export data compatible with `utils/prefered_rejected.py`

## Data Models

### Patient Record
```python
{
    "row_id": int,
    "subject_id": int,
    "gender": str,
    "dob": str,
    "dod": str,
    "expire_flag": str
}
```

### Note Event
```python
{
    "row_id": int,
    "subject_id": int,
    "hadm_id": int,
    "chartdate": str,
    "category": str,
    "description": str,
    "text": str,
    "icd9_diagnosis": str  # Comma-separated list of ICD9 codes for the admission
}
```

### ICD9 Diagnosis Code
```python
{
    "icd9_code": str,
    "short_title": str,
    "long_title": str
}
```

### Human Annotation (Phase 2)
```python
{
    "note_id": int,
    "annotator_id": str,
    "timestamp": str,
    "criteria": {
        "diagnosis": {
            "positive": bool,
            "confidence": float,
            "evidence": str,
            "notes": str
        },
        "treatment": {
            "positive": bool,
            "confidence": float,
            "evidence": str,
            "notes": str
        },
        "self_harm": {
            "positive": bool,
            "confidence": float,
            "evidence": str,
            "notes": str
        }
    }
}
```

## Enhanced Progress Tracking System

### Key Features
- **Dual Progress Display**: Overall progress (3-step process) + file-specific progress
- **Real-time Feedback**: Chunk-based progress reporting for large files
- **Hybrid Tracking**: Combines compressed stream position with statistical estimation
- **User Experience**: Eliminates "frozen" appearance during lengthy operations

### Technical Implementation
- **Single-Pass Approach**: No pre-analysis phase, immediate progress feedback
- **Compressed Stream Tracking**: Monitors compressed bytes read vs. file size
- **Statistical Estimation**: After 5 chunks, estimates total rows based on compression ratio
- **Progress Signals**: `file_progress(str, int, int)` and `status_update(str)` for real-time updates

### Performance Benefits
- **PyArrow Acceleration**: Significantly faster CSV loading for large files like NOTEEVENTS.csv.gz
- **Intelligent Fallback**: Automatic fallback to default pandas engine if PyArrow unavailable
- **Memory Efficient**: Maintains chunk-based loading approach when needed
- **Responsive UI**: Continuous visual feedback throughout loading
- **Thread Interruption**: Support for responsive cancellation
- **No Memory Overhead**: Progress tracking adds no additional memory overhead

## Development Guidelines

### Code Style
- Follow PEP 8 conventions
- Use type hints where appropriate
- Document classes and methods with docstrings
- Use PyQt6 signal/slot pattern for communication

### Testing
- Run `python test_basic.py` for basic functionality verification
- Add comprehensive tests for new features
- Test signal connection and emission capabilities
- Validate data structures and UI components

### Adding New Features
1. Follow the existing modular architecture
2. Add new UI components to `ui/` directory
3. Add data management to `data/` directory
4. Update tests and documentation
5. Maintain compatibility with existing codebase

## Installation and Setup

### Automated Installation (Recommended)
```bash
cd HAIIR
python install.py  # Handles dependencies, verification, and testing
python main.py     # Launch application
```

### Manual Installation
```bash
cd HAIIR
conda activate .conda
pip install -r requirements-minimal.txt  # Phase 1 only
# OR
pip install -r requirements.txt          # All phases
python test_basic.py                     # Verify installation
python main.py                           # Launch application
```

### Data Requirements
- MIMIC-III dataset files in `../mimic/` directory:
  - `PATIENTS.csv.gz` (patient demographics)
  - `NOTEEVENTS.csv.gz` (medical notes)
  - `DIAGNOSES_ICD.csv.gz` (ICD9 diagnosis codes per admission)
  - `D_ICD_DIAGNOSES.csv.gz` (ICD9 code descriptions)

## Success Metrics

### ✅ Functional Requirements Met
- Reads compressed MIMIC data files with PyArrow acceleration and progress tracking
- Displays patient table with proper numeric sorting, searching, and filtering
- Shows comprehensive notes metadata table with ICD9 diagnosis codes
- Displays full note text content with category filtering
- Provides real-time ICD9 diagnosis code descriptions for selected notes
- Links patients to their notes and diagnoses correctly via SUBJECT_ID/HADM_ID
- Provides responsive three-panel UI with professional styling

### ✅ Technical Requirements Met
- PyQt6 modern GUI framework implementation with advanced three-panel layout
- Standalone application design with integration readiness
- PyArrow multi-core acceleration with intelligent fallback mechanisms
- Comprehensive error handling and logging
- Memory-efficient data processing with proper data type handling
- Real-time ICD9 diagnosis lookup and description display
- Professional documentation and testing framework

### ✅ User Experience Goals Met
- Intuitive navigation workflow (patients → notes → content + ICD9 descriptions)
- Responsive three-panel interface with visual feedback
- Clear progress indication during multi-file data loading
- Real-time ICD9 diagnosis information for medical context
- Proper numeric sorting and unlimited row height for long diagnosis lists
- Robust error recovery and user-friendly error messages

## Next Development Steps

### Phase 2 Implementation Plan
1. Implement three-criteria annotation widgets in `annotation_panel.py`
2. Add confidence scoring sliders and evidence text fields
3. Create blinded annotation mode toggle
4. Implement annotation persistence with JSON export
5. Add metadata tracking (annotator ID, timestamps)

### Phase 3 Implementation Plan
1. Connect to existing model inference pipeline
2. Implement AI results visualization in `ai_results_panel.py`
3. Create side-by-side comparison views
4. Add prompt management interface with version control
5. Implement disagreement analysis and export tools

## Conclusion

The HAIIR Phase 1 prototype successfully delivers a complete foundation for the Human-AI Interactive Review system. The application provides immediate value for medical chart review while establishing a robust, well-documented architecture ready for future AI integration and annotation capabilities.
