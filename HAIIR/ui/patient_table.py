"""
Patient Table Widget for HAIIR Application
Displays patient data in a sortable table format.
"""

import pandas as pd
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
    QTableWidgetItem, QHeaderView, QLineEdit, QLabel,
    QPushButton, QComboBox
)
from PyQt6.QtCore import Qt, pyqtSignal, QSortFilterProxyModel
from PyQt6.QtGui import QFont
from typing import Optional


class PatientTableWidget(QWidget):
    """Widget for displaying and filtering patient data."""
    
    # Signals
    patient_selected = pyqtSignal(int)  # subject_id
    
    def __init__(self):
        super().__init__()

        self.patients_df = None
        self.phenotype_manager = None
        self.current_phenotype_filter = None
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the user interface."""
        layout = QVBoxLayout(self)
        
        # Create filter section
        filter_layout = QHBoxLayout()
        
        # Search filter
        filter_layout.addWidget(QLabel("Search:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Search patients...")
        self.search_edit.textChanged.connect(self.filter_patients)
        filter_layout.addWidget(self.search_edit)
        
        # Gender filter
        filter_layout.addWidget(QLabel("Gender:"))
        self.gender_combo = QComboBox()
        self.gender_combo.addItems(["All", "M", "F"])
        self.gender_combo.currentTextChanged.connect(self.filter_patients)
        filter_layout.addWidget(self.gender_combo)

        # Phenotype filter
        filter_layout.addWidget(QLabel("Phenotype:"))
        self.phenotype_combo = QComboBox()
        self.phenotype_combo.addItem("All")
        self.phenotype_combo.currentTextChanged.connect(self.filter_patients)
        filter_layout.addWidget(self.phenotype_combo)

        # Clear filter button
        clear_btn = QPushButton("Clear")
        clear_btn.clicked.connect(self.clear_filters)
        filter_layout.addWidget(clear_btn)
        
        layout.addLayout(filter_layout)
        
        # Create patient table
        self.table = QTableWidget()
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.table.setSortingEnabled(True)
        self.table.setAlternatingRowColors(True)
        
        # Connect selection signal
        self.table.itemSelectionChanged.connect(self.on_selection_changed)
        
        layout.addWidget(self.table)
        
        # Create info label
        self.info_label = QLabel("No data loaded")
        self.info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.info_label)
        
    def set_data(self, patients_df):
        """Set patient data."""
        self.patients_df = patients_df.copy()
        self.populate_table()

    def set_phenotype_manager(self, phenotype_manager):
        """Set the phenotype manager and update phenotype filter dropdown."""
        self.phenotype_manager = phenotype_manager
        self.update_phenotype_filter()

    def update_phenotype_filter(self):
        """Update the phenotype filter dropdown."""
        if not self.phenotype_manager:
            return

        # Clear and repopulate phenotype combo
        self.phenotype_combo.clear()
        self.phenotype_combo.addItem("All")

        phenotype_names = self.phenotype_manager.get_phenotype_names()
        self.phenotype_combo.addItems(phenotype_names)

    def set_phenotype_filter(self, phenotype_name):
        """Set the phenotype filter programmatically and apply filtering."""
        if phenotype_name in [self.phenotype_combo.itemText(i) for i in range(self.phenotype_combo.count())]:
            self.phenotype_combo.setCurrentText(phenotype_name)
        else:
            self.phenotype_combo.setCurrentText("All")

        # Trigger filtering immediately
        self.filter_patients()
        
    def populate_table(self):
        """Populate the table with patient data."""
        if self.patients_df is None or self.patients_df.empty:
            self.table.setRowCount(0)
            self.info_label.setText("No patient data available")
            return
            
        # Filter data based on current filters
        filtered_df = self.apply_filters()
        
        # Setup table
        self.table.setRowCount(len(filtered_df))
        self.table.setColumnCount(6)
        
        # Set headers
        headers = ["ROW_ID", "SUBJECT_ID", "GENDER", "DOB", "DOD", "EXPIRE_FLAG"]
        self.table.setHorizontalHeaderLabels(headers)
        
        # Populate data with proper data type handling
        for row_idx, (_, row) in enumerate(filtered_df.iterrows()):
            for col_idx, header in enumerate(headers):
                value = row.get(header, "")

                # Handle NaN values
                if pd.isna(value) or str(value) == 'nan' or str(value) == "None":
                    display_value = ""
                else:
                    display_value = str(value)

                item = QTableWidgetItem(display_value)

                # Set proper data types for sorting
                if header in ["ROW_ID", "SUBJECT_ID"]:
                    # Numeric fields - store as integer for proper sorting
                    try:
                        if display_value and display_value != "":
                            numeric_value = int(float(value))
                            item.setData(Qt.ItemDataRole.DisplayRole, numeric_value)
                    except (ValueError, TypeError):
                        item.setData(Qt.ItemDataRole.DisplayRole, display_value)
                else:
                    # Text fields - store as string
                    item.setData(Qt.ItemDataRole.DisplayRole, display_value)

                # Store SUBJECT_ID for selection handling
                item.setData(Qt.ItemDataRole.UserRole, row["SUBJECT_ID"])
                self.table.setItem(row_idx, col_idx, item)
        
        # Resize columns
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # ROW_ID
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # SUBJECT_ID
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # GENDER
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)           # DOB
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Stretch)           # DOD
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # EXPIRE_FLAG
        
        # Update info label
        total_patients = len(self.patients_df)
        filtered_patients = len(filtered_df)
        if filtered_patients == total_patients:
            self.info_label.setText(f"Showing {total_patients} patients")
        else:
            self.info_label.setText(f"Showing {filtered_patients} of {total_patients} patients")
            
    def apply_filters(self):
        """Apply current filters to the data."""
        if self.patients_df is None:
            return pd.DataFrame()
            
        filtered_df = self.patients_df.copy()
        
        # Apply search filter
        search_text = self.search_edit.text().strip().lower()
        if search_text:
            # Search in SUBJECT_ID and ROW_ID
            mask = (
                filtered_df["SUBJECT_ID"].astype(str).str.lower().str.contains(search_text, na=False) |
                filtered_df["ROW_ID"].astype(str).str.lower().str.contains(search_text, na=False)
            )
            filtered_df = filtered_df[mask]
        
        # Apply gender filter
        gender_filter = self.gender_combo.currentText()
        if gender_filter != "All":
            filtered_df = filtered_df[filtered_df["GENDER"] == gender_filter]

        # Apply phenotype filter
        phenotype_filter = self.phenotype_combo.currentText()
        if phenotype_filter != "All" and self.phenotype_manager:
            # Get patients that match the selected phenotype
            matching_patients = self.phenotype_manager.get_matching_patients(phenotype_filter)
            if matching_patients:
                filtered_df = filtered_df[filtered_df["SUBJECT_ID"].isin(matching_patients)]
            else:
                # No patients match this phenotype, return empty dataframe
                filtered_df = filtered_df.iloc[0:0]  # Empty dataframe with same columns

        return filtered_df
        
    def filter_patients(self):
        """Filter patients based on current filter settings."""
        self.populate_table()
        
    def clear_filters(self):
        """Clear all filters."""
        self.search_edit.clear()
        self.gender_combo.setCurrentText("All")
        self.phenotype_combo.setCurrentText("All")
        
    def on_selection_changed(self):
        """Handle table selection change."""
        selected_items = self.table.selectedItems()
        if selected_items:
            # Get subject_id from the first selected item
            subject_id = selected_items[0].data(Qt.ItemDataRole.UserRole)
            if subject_id is not None:
                self.patient_selected.emit(int(subject_id))
                
    def refresh(self):
        """Refresh the table display."""
        self.populate_table()
        
    def get_selected_patient_id(self):
        """Get the currently selected patient ID."""
        selected_items = self.table.selectedItems()
        if selected_items:
            return selected_items[0].data(Qt.ItemDataRole.UserRole)
        return None
