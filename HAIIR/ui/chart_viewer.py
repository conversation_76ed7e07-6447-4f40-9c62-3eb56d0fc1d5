"""
Chart Viewer Widget for HAIIR Application
Displays medical notes and charts with proper formatting.
"""

import pandas as pd
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTextEdit,
    QTableWidget, QTableWidgetItem, QSplitter, QLabel,
    QComboBox, QPushButton, QGroupBox, QHeaderView
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont


class ChartViewerWidget(QWidget):
    """Widget for displaying medical charts and notes."""
    
    # Signals
    note_selected = pyqtSignal(int)  # row_id
    
    def __init__(self):
        super().__init__()

        self.notes_df = None
        self.current_notes = []
        self.icd9_dict = None
        self.icd9_descriptions_dict = None
        self.data_loader = None
        self.phenotype_manager = None
        self.current_subject_id = None
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the user interface."""
        layout = QVBoxLayout(self)

        # Create header with patient info
        header_layout = QHBoxLayout()
        self.patient_label = QLabel("No patient selected")
        self.patient_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        header_layout.addWidget(self.patient_label)
        header_layout.addStretch()

        # Note count label
        self.note_count_label = QLabel("")
        header_layout.addWidget(self.note_count_label)

        layout.addLayout(header_layout)

        # Create vertical splitter for notes metadata table (top) and content area (bottom)
        main_splitter = QSplitter(Qt.Orientation.Vertical)
        layout.addWidget(main_splitter)

        # Create notes metadata table panel (top)
        metadata_panel = self.create_notes_metadata_panel()
        main_splitter.addWidget(metadata_panel)

        # Create horizontal splitter for text content (left) and ICD9 descriptions (right)
        content_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_splitter.addWidget(content_splitter)

        # Create text content panel (left side of bottom area)
        text_panel = self.create_text_content_panel()
        content_splitter.addWidget(text_panel)

        # Create ICD9 descriptions panel (right side of bottom area)
        icd9_panel = self.create_icd9_descriptions_panel()
        content_splitter.addWidget(icd9_panel)

        # Set splitter proportions
        main_splitter.setSizes([300, 500])  # 37.5% metadata, 62.5% content area
        content_splitter.setSizes([600, 200])  # 75% text content, 25% ICD9 descriptions
        
    def create_notes_metadata_panel(self):
        """Create the notes metadata table panel with category filter."""
        panel = QGroupBox("Notes Metadata")
        layout = QVBoxLayout(panel)

        # Create filter controls
        filter_layout = QHBoxLayout()

        # Category filter
        filter_layout.addWidget(QLabel("Category:"))
        self.category_combo = QComboBox()
        self.category_combo.currentTextChanged.connect(self.filter_notes)
        filter_layout.addWidget(self.category_combo)

        # Phenotype filter
        filter_layout.addWidget(QLabel("Phenotype:"))
        self.phenotype_combo = QComboBox()
        self.phenotype_combo.addItem("All")
        self.phenotype_combo.currentTextChanged.connect(self.filter_notes)
        filter_layout.addWidget(self.phenotype_combo)

        # Clear filter button
        clear_btn = QPushButton("Clear")
        clear_btn.clicked.connect(self.clear_note_filters)
        filter_layout.addWidget(clear_btn)

        filter_layout.addStretch()  # Push filter controls to the left

        layout.addLayout(filter_layout)

        # Create notes metadata table
        self.notes_table = QTableWidget()
        self.notes_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.notes_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.notes_table.setSortingEnabled(True)
        self.notes_table.setAlternatingRowColors(True)

        # Enable text wrapping and disable text elision
        self.notes_table.setWordWrap(True)
        self.notes_table.setTextElideMode(Qt.TextElideMode.ElideNone)

        # Allow unlimited row height expansion
        vertical_header = self.notes_table.verticalHeader()
        vertical_header.setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        vertical_header.setDefaultSectionSize(50)  # Minimum row height

        # Connect selection signal
        self.notes_table.itemSelectionChanged.connect(self.on_notes_table_selection_changed)

        layout.addWidget(self.notes_table)

        return panel

    def create_text_content_panel(self):
        """Create the text content panel (bottom of right side)."""
        panel = QGroupBox("Note Content")
        layout = QVBoxLayout(panel)

        # Create note info header
        info_layout = QHBoxLayout()
        self.note_info_label = QLabel("Select a note to view content")
        self.note_info_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        info_layout.addWidget(self.note_info_label)
        info_layout.addStretch()

        layout.addLayout(info_layout)

        # Create text display
        self.chart_text = QTextEdit()
        self.chart_text.setReadOnly(True)
        self.chart_text.setFont(QFont("Courier New", 10))
        layout.addWidget(self.chart_text)

        return panel

    def create_icd9_descriptions_panel(self):
        """Create the ICD9 descriptions panel (right side of bottom area)."""
        panel = QGroupBox("ICD9 Diagnosis Descriptions")
        layout = QVBoxLayout(panel)

        # Create text display for ICD9 descriptions
        self.icd9_text = QTextEdit()
        self.icd9_text.setReadOnly(True)
        self.icd9_text.setFont(QFont("Arial", 10))
        self.icd9_text.setPlaceholderText("Select a note to view ICD9 diagnosis descriptions")
        layout.addWidget(self.icd9_text)

        return panel

    def set_icd9_dict(self, icd9_dict):
        """Set the ICD9 diagnosis dictionary."""
        self.icd9_dict = icd9_dict

    def set_icd9_descriptions_dict(self, icd9_descriptions_dict):
        """Set the ICD9 descriptions dictionary."""
        self.icd9_descriptions_dict = icd9_descriptions_dict

    def set_data_loader(self, data_loader):
        """Set the data loader reference for accessing ICD9 methods."""
        self.data_loader = data_loader

    def set_phenotype_manager(self, phenotype_manager):
        """Set the phenotype manager and update phenotype filter."""
        self.phenotype_manager = phenotype_manager
        self.update_phenotype_filter()

    def update_phenotype_filter(self):
        """Update the phenotype filter dropdown."""
        if not self.phenotype_manager:
            return

        # Clear and repopulate phenotype combo
        self.phenotype_combo.clear()
        self.phenotype_combo.addItem("All")

        phenotype_names = self.phenotype_manager.get_phenotype_names()
        self.phenotype_combo.addItems(phenotype_names)

    def display_notes(self, notes_df):
        """Display notes for a patient."""
        self.notes_df = notes_df.copy() if notes_df is not None else pd.DataFrame()
        self.current_notes = self.notes_df.to_dict('records') if not self.notes_df.empty else []
        
        # Update patient label
        if not self.notes_df.empty:
            subject_id = self.notes_df.iloc[0]['SUBJECT_ID']
            self.current_subject_id = subject_id
            self.patient_label.setText(f"Patient ID: {subject_id}")
            self.note_count_label.setText(f"{len(self.notes_df)} notes")
        else:
            self.current_subject_id = None
            self.patient_label.setText("No patient selected")
            self.note_count_label.setText("")
            
        # Update category filter
        self.update_category_filter()

        # Populate notes metadata table
        self.populate_notes_table()

        # Clear chart display and ICD9 descriptions panel
        self.chart_text.clear()
        self.note_info_label.setText("Select a note to view content")
        self.icd9_text.setPlainText("Select a note to view ICD9 diagnosis descriptions")
        
    def update_category_filter(self):
        """Update the category filter dropdown, preserving current selection if possible."""
        # Remember the current selection
        current_selection = self.category_combo.currentText()

        # Clear and repopulate
        self.category_combo.clear()
        self.category_combo.addItem("All")

        if (self.notes_df is not None and not self.notes_df.empty and
            'CATEGORY' in self.notes_df.columns):
            categories = sorted(self.notes_df['CATEGORY'].dropna().unique())
            self.category_combo.addItems(categories)

            # Restore previous selection if it exists in the new categories
            if current_selection and current_selection in ["All"] + categories:
                self.category_combo.setCurrentText(current_selection)
            else:
                # Fall back to "All" if previous selection doesn't exist
                self.category_combo.setCurrentText("All")
            


    def populate_notes_table(self):
        """Populate the notes metadata table."""
        self.notes_table.clear()

        if self.notes_df is None or self.notes_df.empty:
            return

        # Apply category filter
        filtered_notes = self.apply_note_filters()

        # Define table columns (all NOTEEVENTS fields except TEXT, plus ICD9_Diagnosis)
        columns = ["ROW_ID", "SUBJECT_ID", "HADM_ID", "CHARTDATE", "CHARTTIME",
                  "STORETIME", "CATEGORY", "DESCRIPTION", "CGID", "ISERROR", "ICD9_Diagnosis"]

        # Setup table
        self.notes_table.setRowCount(len(filtered_notes))
        self.notes_table.setColumnCount(len(columns))
        self.notes_table.setHorizontalHeaderLabels(columns)

        # Populate data with proper data type handling
        for row_idx, (_, note) in enumerate(filtered_notes.iterrows()):
            for col_idx, column in enumerate(columns):
                if column == "ICD9_Diagnosis":
                    # Handle ICD9 diagnosis codes
                    subject_id = note.get('SUBJECT_ID')
                    hadm_id = note.get('HADM_ID')

                    if (self.icd9_dict and
                        pd.notna(subject_id) and pd.notna(hadm_id)):
                        key = (int(subject_id), int(hadm_id))
                        icd9_codes = self.icd9_dict.get(key, [])
                        if icd9_codes:
                            # Format codes for better readability - use line breaks for many codes
                            if len(icd9_codes) > 5:
                                # For many codes, use line breaks every 5 codes
                                formatted_codes = []
                                for i in range(0, len(icd9_codes), 5):
                                    chunk = icd9_codes[i:i+5]
                                    formatted_codes.append(", ".join(chunk))
                                display_value = "\n".join(formatted_codes)
                            else:
                                # For few codes, use comma separation
                                display_value = ", ".join(icd9_codes)
                        else:
                            display_value = ""
                    else:
                        display_value = ""
                else:
                    # Handle regular NOTEEVENTS fields
                    value = note.get(column, "")

                    # Handle NaN values
                    if pd.isna(value) or str(value) == 'nan':
                        display_value = ""
                    else:
                        display_value = str(value)

                item = QTableWidgetItem(display_value)

                # Enable text wrapping for cells, especially for ICD9_Diagnosis
                item.setFlags(item.flags() | Qt.ItemFlag.ItemIsEnabled)

                # For ICD9_Diagnosis column, ensure no text truncation
                if column == "ICD9_Diagnosis":
                    # Set text alignment to allow wrapping
                    item.setTextAlignment(Qt.AlignmentFlag.AlignTop | Qt.AlignmentFlag.AlignLeft)

                # Set proper data types for sorting
                if column in ["ROW_ID", "SUBJECT_ID", "HADM_ID", "CGID"]:
                    # Numeric fields - store as integer for proper sorting
                    try:
                        if display_value and display_value != "":
                            if column == "ICD9_Diagnosis":
                                # ICD9_Diagnosis is text, not numeric
                                item.setData(Qt.ItemDataRole.DisplayRole, display_value)
                            else:
                                numeric_value = int(float(note.get(column, 0)))
                                item.setData(Qt.ItemDataRole.DisplayRole, numeric_value)
                    except (ValueError, TypeError):
                        item.setData(Qt.ItemDataRole.DisplayRole, display_value)
                else:
                    # Text fields - store as string
                    item.setData(Qt.ItemDataRole.DisplayRole, display_value)

                # Store ROW_ID for selection handling
                item.setData(Qt.ItemDataRole.UserRole, note['ROW_ID'])
                self.notes_table.setItem(row_idx, col_idx, item)

        # Resize rows to fit content
        self.notes_table.resizeRowsToContents()

        # Configure column widths
        header = self.notes_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # ROW_ID
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # SUBJECT_ID
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # HADM_ID
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # CHARTDATE
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # CHARTTIME
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # STORETIME
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)  # CATEGORY
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.Stretch)           # DESCRIPTION
        header.setSectionResizeMode(8, QHeaderView.ResizeMode.ResizeToContents)  # CGID
        header.setSectionResizeMode(9, QHeaderView.ResizeMode.ResizeToContents)  # ISERROR
        header.setSectionResizeMode(10, QHeaderView.ResizeMode.Stretch)          # ICD9_Diagnosis

    def apply_note_filters(self):
        """Apply filters to the notes."""
        if self.notes_df is None or self.notes_df.empty:
            return pd.DataFrame() if self.notes_df is None else self.notes_df

        filtered_df = self.notes_df.copy()

        # Apply category filter
        category_filter = self.category_combo.currentText()
        if category_filter != "All":
            filtered_df = filtered_df[filtered_df['CATEGORY'] == category_filter]

        # Apply phenotype filter
        phenotype_filter = self.phenotype_combo.currentText()
        if (phenotype_filter != "All" and self.phenotype_manager and
            self.icd9_dict is not None):

            # Get the phenotype's ICD9 codes
            phenotype_codes = self.phenotype_manager.get_phenotype_codes(phenotype_filter)

            if phenotype_codes:
                # Filter notes to only show those whose ICD9 codes intersect with phenotype codes
                matching_notes = []

                for _, note in filtered_df.iterrows():
                    # Get ICD9 codes for this specific note
                    note_icd9_codes = self.get_note_icd9_codes(note)

                    # Check if this note's codes intersect with phenotype codes
                    if note_icd9_codes & phenotype_codes:  # Set intersection
                        matching_notes.append(note)

                if matching_notes:
                    # Create dataframe from matching notes
                    filtered_df = pd.DataFrame(matching_notes).reset_index(drop=True)
                else:
                    # No notes match this phenotype
                    filtered_df = filtered_df.iloc[0:0]  # Empty dataframe with same columns
            else:
                # No codes in phenotype, show no notes
                filtered_df = filtered_df.iloc[0:0]

        return filtered_df

    def get_note_icd9_codes(self, note):
        """Get ICD9 codes for a specific note."""
        subject_id = note.get('SUBJECT_ID')
        hadm_id = note.get('HADM_ID')

        # Return empty set if no ICD9 data or missing required fields
        if (not self.icd9_dict or pd.isna(subject_id) or pd.isna(hadm_id)):
            return set()

        # Get ICD9 codes for this note's admission
        key = (int(subject_id), int(hadm_id))
        icd9_codes = self.icd9_dict.get(key, [])

        # Return as set for easy intersection operations
        return set(icd9_codes)

    def filter_notes(self):
        """Filter notes based on current settings."""
        self.populate_notes_table()

    def clear_note_filters(self):
        """Clear note filters."""
        self.category_combo.setCurrentText("All")
        self.phenotype_combo.setCurrentText("All")

    def on_notes_table_selection_changed(self):
        """Handle note selection change from notes metadata table."""
        selected_items = self.notes_table.selectedItems()
        if selected_items:
            row_id = selected_items[0].data(Qt.ItemDataRole.UserRole)
            self.display_note_content(row_id)
            self.display_icd9_descriptions(row_id)
            self.note_selected.emit(row_id)
            
    def display_note_content(self, row_id):
        """Display the content of a specific note."""
        if self.notes_df.empty:
            return
            
        # Find the note
        note_row = self.notes_df[self.notes_df['ROW_ID'] == row_id]
        if note_row.empty:
            return
            
        note = note_row.iloc[0]
        
        # Update info label
        category = note.get('CATEGORY', 'Unknown')
        description = note.get('DESCRIPTION', 'No description')
        chartdate = note.get('CHARTDATE', 'No date')
        self.note_info_label.setText(f"{category} - {description} ({chartdate})")
        
        # Display note text
        note_text = note.get('TEXT', 'No content available')
        if pd.isna(note_text) or str(note_text) == 'nan':
            note_text = 'No content available'
            
        self.chart_text.setPlainText(str(note_text))

    def display_icd9_descriptions(self, row_id):
        """Display ICD9 diagnosis descriptions for a specific note."""
        if self.notes_df.empty or self.data_loader is None:
            self.icd9_text.setPlainText("Select a note to view ICD9 diagnosis descriptions")
            return

        # Find the note
        note = self.notes_df[self.notes_df['ROW_ID'] == row_id]
        if note.empty:
            self.icd9_text.setPlainText("Select a note to view ICD9 diagnosis descriptions")
            return

        note_data = note.iloc[0]
        subject_id = note_data.get('SUBJECT_ID')
        hadm_id = note_data.get('HADM_ID')

        if pd.isna(subject_id) or pd.isna(hadm_id):
            self.icd9_text.setPlainText("No admission ID available for this note")
            return

        # Get ICD9 codes with descriptions
        codes_with_descriptions = self.data_loader.get_icd9_codes_with_descriptions(
            int(subject_id), int(hadm_id)
        )

        if not codes_with_descriptions:
            self.icd9_text.setPlainText("No ICD9 diagnosis codes found for this admission")
            return

        # Format the descriptions for display
        description_lines = []
        description_lines.append(f"ICD9 Diagnosis Codes for Admission {int(hadm_id)}:")
        description_lines.append("=" * 50)
        description_lines.append("")

        for code, description in codes_with_descriptions:
            description_lines.append(f"• {code}: {description}")

        description_lines.append("")
        description_lines.append(f"Total: {len(codes_with_descriptions)} diagnosis codes")

        self.icd9_text.setPlainText("\n".join(description_lines))

    def clear(self):
        """Clear all displays."""
        self.notes_df = None
        self.current_notes = []
        self.icd9_dict = None
        self.icd9_descriptions_dict = None
        self.data_loader = None
        self.phenotype_manager = None
        self.current_subject_id = None
        self.patient_label.setText("No patient selected")
        self.note_count_label.setText("")
        self.notes_table.clear()
        self.chart_text.clear()
        self.icd9_text.setPlainText("Select a note to view ICD9 diagnosis descriptions")
        self.note_info_label.setText("Select a note to view content")
        self.category_combo.clear()
        self.category_combo.addItem("All")
        self.phenotype_combo.clear()
        self.phenotype_combo.addItem("All")
