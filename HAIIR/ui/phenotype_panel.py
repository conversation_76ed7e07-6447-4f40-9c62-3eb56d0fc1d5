"""
Phenotype Panel Widget for HAIIR Application
Provides interface for creating and managing phenotypes.
"""

import logging
from typing import List, Set, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLineEdit, QLabel,
    QPushButton, QListWidget, QListWidgetItem, QGroupBox,
    QMessageBox, QAbstractItemView, QSplitter
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from data.phenotype_manager import PhenotypeManager

logger = logging.getLogger(__name__)


class ICD9MultiSelectWidget(QWidget):
    """Multi-select widget for ICD9 codes with search functionality."""

    def __init__(self):
        super().__init__()
        self.all_codes = []  # Store all codes
        self.setup_ui()

    def setup_ui(self):
        """Setup the UI with search and list."""
        layout = QVBoxLayout(self)

        # Search box
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Search ICD9 codes...")
        self.search_edit.textChanged.connect(self.filter_codes)
        layout.addWidget(self.search_edit)

        # List widget
        self.list_widget = QListWidget()
        self.list_widget.setSelectionMode(QAbstractItemView.SelectionMode.MultiSelection)
        self.list_widget.setMaximumHeight(120)  # Limit height to save space
        layout.addWidget(self.list_widget)

    def set_icd9_codes(self, codes: List[str]):
        """Set the available ICD9 codes."""
        self.all_codes = codes
        self.populate_list(codes)

    def populate_list(self, codes: List[str]):
        """Populate the list with given codes."""
        self.list_widget.clear()
        for code in codes:
            item = QListWidgetItem(code)
            self.list_widget.addItem(item)

    def filter_codes(self):
        """Filter codes based on search text."""
        search_text = self.search_edit.text().lower()
        if not search_text:
            filtered_codes = self.all_codes
        else:
            filtered_codes = [code for code in self.all_codes if search_text in code.lower()]

        self.populate_list(filtered_codes)

    def get_selected_codes(self) -> List[str]:
        """Get the currently selected ICD9 codes."""
        selected_codes = []
        for i in range(self.list_widget.count()):
            item = self.list_widget.item(i)
            if item.isSelected():
                selected_codes.append(item.text())
        return selected_codes

    def set_selected_codes(self, codes: List[str]):
        """Set the selected ICD9 codes."""
        self.clearSelection()
        for i in range(self.list_widget.count()):
            item = self.list_widget.item(i)
            if item.text() in codes:
                item.setSelected(True)

    def clearSelection(self):
        """Clear all selections."""
        self.list_widget.clearSelection()


class PhenotypePanelWidget(QWidget):
    """Widget for managing phenotypes."""
    
    # Signals
    phenotype_created = pyqtSignal(str)  # phenotype_name
    phenotype_updated = pyqtSignal(str)  # phenotype_name
    phenotype_deleted = pyqtSignal(str)  # phenotype_name
    phenotype_selected = pyqtSignal(str)  # phenotype_name for filtering
    
    def __init__(self):
        super().__init__()
        
        self.phenotype_manager: Optional[PhenotypeManager] = None
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the user interface."""
        layout = QVBoxLayout(self)
        
        # Create main group box
        group_box = QGroupBox("Phenotype Manager")
        group_layout = QVBoxLayout(group_box)
        layout.addWidget(group_box)
        
        # Create splitter for top and bottom sections
        splitter = QSplitter(Qt.Orientation.Vertical)
        group_layout.addWidget(splitter)
        
        # Top section: Phenotype creation/editing
        creation_widget = self.create_phenotype_creation_widget()
        splitter.addWidget(creation_widget)
        
        # Bottom section: Phenotype list
        list_widget = self.create_phenotype_list_widget()
        splitter.addWidget(list_widget)
        
        # Set splitter proportions (60% creation, 40% list)
        splitter.setSizes([180, 120])
        
    def create_phenotype_creation_widget(self):
        """Create the phenotype creation/editing widget."""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Phenotype name input
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel("Name:"))
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("e.g., Major Depressive Disorder")
        name_layout.addWidget(self.name_edit)
        layout.addLayout(name_layout)
        
        # ICD9 codes multi-select
        layout.addWidget(QLabel("ICD9 Codes (select from list):"))
        self.icd9_select = ICD9MultiSelectWidget()
        layout.addWidget(self.icd9_select)

        # Text input for comma-separated codes
        layout.addWidget(QLabel("Or enter comma-separated codes:"))
        self.codes_text_edit = QLineEdit()
        self.codes_text_edit.setPlaceholderText("e.g., 29620, 29621, 29622")
        self.codes_text_edit.textChanged.connect(self.on_codes_text_changed)
        layout.addWidget(self.codes_text_edit)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("Save Phenotype")
        self.save_btn.clicked.connect(self.save_phenotype)
        button_layout.addWidget(self.save_btn)
        
        self.clear_btn = QPushButton("Clear")
        self.clear_btn.clicked.connect(self.clear_form)
        button_layout.addWidget(self.clear_btn)
        
        layout.addLayout(button_layout)
        
        return widget
        
    def create_phenotype_list_widget(self):
        """Create the phenotype list widget."""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Header with filter button
        header_layout = QHBoxLayout()
        header_layout.addWidget(QLabel("Defined Phenotypes:"))
        
        self.filter_btn = QPushButton("Filter Patients")
        self.filter_btn.clicked.connect(self.filter_by_selected_phenotype)
        self.filter_btn.setEnabled(False)
        header_layout.addWidget(self.filter_btn)
        
        layout.addLayout(header_layout)
        
        # Phenotype list
        self.phenotype_list = QListWidget()
        self.phenotype_list.setMaximumHeight(80)  # Compact list
        self.phenotype_list.itemSelectionChanged.connect(self.on_phenotype_selection_changed)
        self.phenotype_list.itemDoubleClicked.connect(self.edit_phenotype)
        layout.addWidget(self.phenotype_list)
        
        # Selected phenotype codes display
        layout.addWidget(QLabel("Selected phenotype codes:"))
        self.selected_codes_edit = QLineEdit()
        self.selected_codes_edit.setReadOnly(True)
        self.selected_codes_edit.setPlaceholderText("Select a phenotype to view its codes")
        layout.addWidget(self.selected_codes_edit)

        # Action buttons
        button_layout = QHBoxLayout()

        self.edit_btn = QPushButton("Edit Selected")
        self.edit_btn.clicked.connect(self.edit_phenotype_from_button)
        self.edit_btn.setEnabled(False)
        button_layout.addWidget(self.edit_btn)

        self.delete_btn = QPushButton("Delete Selected")
        self.delete_btn.clicked.connect(self.delete_phenotype)
        self.delete_btn.setEnabled(False)
        button_layout.addWidget(self.delete_btn)

        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        return widget
    
    def set_phenotype_manager(self, phenotype_manager: PhenotypeManager):
        """Set the phenotype manager."""
        self.phenotype_manager = phenotype_manager
        
        # Populate ICD9 codes
        all_codes = phenotype_manager.get_all_icd9_codes()
        self.icd9_select.set_icd9_codes(all_codes)
        
        # Refresh phenotype list
        self.refresh_phenotype_list()
        
    def refresh_phenotype_list(self):
        """Refresh the phenotype list."""
        if not self.phenotype_manager:
            return
            
        self.phenotype_list.clear()
        phenotype_names = self.phenotype_manager.get_phenotype_names()
        
        for name in phenotype_names:
            codes = self.phenotype_manager.get_phenotype_codes(name)
            item_text = f"{name} ({len(codes)} codes)"
            item = QListWidgetItem(item_text)
            item.setData(Qt.ItemDataRole.UserRole, name)  # Store actual name
            self.phenotype_list.addItem(item)
    
    def save_phenotype(self):
        """Save the current phenotype."""
        if not self.phenotype_manager:
            QMessageBox.warning(self, "Error", "Phenotype manager not initialized")
            return

        name = self.name_edit.text().strip()
        if not name:
            QMessageBox.warning(self, "Error", "Please enter a phenotype name")
            return

        # Get codes from both sources
        selected_codes = self.icd9_select.get_selected_codes()
        text_codes = self.get_codes_from_text()

        # Combine and deduplicate codes
        all_codes = list(set(selected_codes + text_codes))

        if not all_codes:
            QMessageBox.warning(self, "Error", "Please select at least one ICD9 code or enter codes in the text field")
            return
        
        # Check if updating existing phenotype
        if name in self.phenotype_manager.get_phenotype_names():
            reply = QMessageBox.question(
                self, "Update Phenotype",
                f"Phenotype '{name}' already exists with {len(self.phenotype_manager.get_phenotype_codes(name))} codes.\n"
                f"Replace it with {len(all_codes)} codes?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            if reply == QMessageBox.StandardButton.Yes:
                success = self.phenotype_manager.update_phenotype(name, all_codes)
                if success:
                    self.phenotype_updated.emit(name)
                    self.clear_form()
                    self.refresh_phenotype_list()
                    QMessageBox.information(self, "Success", f"Updated phenotype '{name}' with {len(all_codes)} codes")
        else:
            # Create new phenotype
            success = self.phenotype_manager.create_phenotype(name, all_codes)
            if success:
                self.phenotype_created.emit(name)
                self.clear_form()
                self.refresh_phenotype_list()
                QMessageBox.information(self, "Success", f"Created phenotype '{name}' with {len(all_codes)} codes")
    
    def clear_form(self):
        """Clear the form."""
        self.name_edit.clear()
        self.icd9_select.clearSelection()
        self.codes_text_edit.clear()

    def get_codes_from_text(self) -> List[str]:
        """Get ICD9 codes from the text input field."""
        text = self.codes_text_edit.text().strip()
        if not text:
            return []

        # Split by comma and clean up
        codes = [code.strip() for code in text.split(',')]
        codes = [code for code in codes if code]  # Remove empty strings

        # Validate codes against available codes (optional - could warn about invalid codes)
        if self.phenotype_manager:
            all_available_codes = set(self.phenotype_manager.get_all_icd9_codes())
            valid_codes = [code for code in codes if code in all_available_codes]
            invalid_codes = [code for code in codes if code not in all_available_codes]

            if invalid_codes:
                # For now, just log invalid codes - could show warning to user
                logger.warning(f"Invalid ICD9 codes entered: {invalid_codes}")

            return valid_codes

        return codes

    def on_codes_text_changed(self):
        """Handle changes in the codes text field."""
        # This could be used for validation or real-time feedback
        pass

    def edit_phenotype_from_button(self):
        """Edit phenotype when Edit button is clicked."""
        current_item = self.phenotype_list.currentItem()
        if current_item:
            self.edit_phenotype(current_item)
    
    def edit_phenotype(self, item: QListWidgetItem):
        """Edit the selected phenotype."""
        if not self.phenotype_manager:
            return

        phenotype_name = item.data(Qt.ItemDataRole.UserRole)
        codes = self.phenotype_manager.get_phenotype_codes(phenotype_name)
        codes_list = sorted(list(codes))

        # Populate form
        self.name_edit.setText(phenotype_name)
        self.icd9_select.set_selected_codes(codes_list)

        # Also populate the text field with comma-separated codes
        self.codes_text_edit.setText(", ".join(codes_list))
    
    def delete_phenotype(self):
        """Delete the selected phenotype."""
        if not self.phenotype_manager:
            return
            
        current_item = self.phenotype_list.currentItem()
        if not current_item:
            return
            
        phenotype_name = current_item.data(Qt.ItemDataRole.UserRole)
        
        reply = QMessageBox.question(
            self, "Delete Phenotype",
            f"Are you sure you want to delete phenotype '{phenotype_name}'?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            success = self.phenotype_manager.delete_phenotype(phenotype_name)
            if success:
                self.phenotype_deleted.emit(phenotype_name)
                self.refresh_phenotype_list()
                self.clear_form()
                QMessageBox.information(self, "Success", f"Deleted phenotype '{phenotype_name}'")
    
    def filter_by_selected_phenotype(self):
        """Filter patients by the selected phenotype."""
        current_item = self.phenotype_list.currentItem()
        if current_item:
            phenotype_name = current_item.data(Qt.ItemDataRole.UserRole)
            self.phenotype_selected.emit(phenotype_name)
    
    def on_phenotype_selection_changed(self):
        """Handle phenotype selection change."""
        current_item = self.phenotype_list.currentItem()
        has_selection = bool(current_item)

        # Enable/disable buttons
        self.delete_btn.setEnabled(has_selection)
        self.filter_btn.setEnabled(has_selection)
        self.edit_btn.setEnabled(has_selection)

        # Show selected phenotype's codes
        if has_selection and self.phenotype_manager:
            phenotype_name = current_item.data(Qt.ItemDataRole.UserRole)
            codes = self.phenotype_manager.get_phenotype_codes(phenotype_name)
            codes_text = ", ".join(sorted(list(codes)))
            self.selected_codes_edit.setText(codes_text)
        else:
            self.selected_codes_edit.clear()
