"""
Main Window for HAIIR Application
"""

import sys
from pathlib import Path
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QSplitter, QTabWidget, QMenuBar, QStatusBar,
    QMessageBox, QProgressBar, QLabel
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QAction, QKeySequence

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from .patient_table import PatientTableWidget
from .chart_viewer import ChartViewerWidget
from .phenotype_panel import Phenotype<PERSON>anelWidget
from data.mimic_loader import MimicDataLoader


class MainWindow(QMainWindow):
    """Main application window for HAIIR."""
    
    # Signals
    patient_selected = pyqtSignal(int)  # subject_id
    
    def __init__(self):
        super().__init__()
        
        # Initialize data loader
        self.data_loader = MimicDataLoader()
        
        # Setup UI
        self.setup_ui()
        self.setup_menus()
        self.setup_status_bar()
        self.setup_connections()
        
        # Load initial data
        self.load_data()
        
    def setup_ui(self):
        """Setup the main user interface."""
        self.setWindowTitle("HAIIR - Human-AI Interactive Review")
        self.setGeometry(100, 100, 1400, 900)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create main layout
        main_layout = QHBoxLayout(central_widget)
        
        # Create main splitter
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(main_splitter)

        # Create left panel with vertical splitter for patient table and phenotype panel
        left_splitter = QSplitter(Qt.Orientation.Vertical)
        main_splitter.addWidget(left_splitter)

        # Create patient table widget (upper 2/3 of left panel)
        self.patient_table = PatientTableWidget()
        left_splitter.addWidget(self.patient_table)

        # Create phenotype panel widget (lower 1/3 of left panel)
        self.phenotype_panel = PhenotypePanelWidget()
        left_splitter.addWidget(self.phenotype_panel)

        # Set left splitter proportions (2/3 patient table, 1/3 phenotype panel)
        left_splitter.setSizes([280, 140])

        # Create chart viewer widget
        self.chart_viewer = ChartViewerWidget()
        main_splitter.addWidget(self.chart_viewer)

        # Set main splitter proportions (30% for left panel, 70% for chart viewer)
        main_splitter.setSizes([420, 980])
        
    def setup_menus(self):
        """Setup application menus."""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu("&File")
        
        # Load data action
        load_action = QAction("&Load Data", self)
        load_action.setShortcut(QKeySequence.StandardKey.Open)
        load_action.triggered.connect(self.load_data)
        file_menu.addAction(load_action)

        file_menu.addSeparator()

        # Cache management submenu
        cache_menu = file_menu.addMenu("Cache Management")

        # Cache info action
        cache_info_action = QAction("View Cache Info", self)
        cache_info_action.triggered.connect(self.show_cache_info)
        cache_menu.addAction(cache_info_action)

        # Clear cache action
        clear_cache_action = QAction("Clear Cache", self)
        clear_cache_action.triggered.connect(self.clear_cache)
        cache_menu.addAction(clear_cache_action)

        file_menu.addSeparator()

        # Exit action
        exit_action = QAction("E&xit", self)
        exit_action.setShortcut(QKeySequence.StandardKey.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # View menu
        view_menu = menubar.addMenu("&View")
        
        # Refresh action
        refresh_action = QAction("&Refresh", self)
        refresh_action.setShortcut(QKeySequence.StandardKey.Refresh)
        refresh_action.triggered.connect(self.refresh_data)
        view_menu.addAction(refresh_action)
        
        # Help menu
        help_menu = menubar.addMenu("&Help")
        
        # About action
        about_action = QAction("&About", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
        
    def setup_status_bar(self):
        """Setup status bar."""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # Create progress widgets container
        progress_widget = QWidget()
        progress_layout = QVBoxLayout(progress_widget)
        progress_layout.setContentsMargins(0, 0, 0, 0)
        progress_layout.setSpacing(2)

        # Overall progress bar
        self.overall_progress_bar = QProgressBar()
        self.overall_progress_bar.setVisible(False)
        self.overall_progress_bar.setMaximumHeight(12)
        self.overall_progress_bar.setFormat("Overall: %v/%m")
        progress_layout.addWidget(self.overall_progress_bar)

        # File-specific progress bar
        self.file_progress_bar = QProgressBar()
        self.file_progress_bar.setVisible(False)
        self.file_progress_bar.setMaximumHeight(12)
        self.file_progress_bar.setFormat("File: %p%")
        progress_layout.addWidget(self.file_progress_bar)

        # Add progress widget to status bar
        self.status_bar.addPermanentWidget(progress_widget)

        # Set initial status
        self.status_bar.showMessage("Ready")
        
    def setup_connections(self):
        """Setup signal connections."""
        # Connect patient selection to chart viewer
        self.patient_table.patient_selected.connect(self.on_patient_selected)

        # Connect phenotype panel signals
        self.phenotype_panel.phenotype_selected.connect(self.on_phenotype_filter_selected)
        self.phenotype_panel.phenotype_created.connect(self.on_phenotype_changed)
        self.phenotype_panel.phenotype_updated.connect(self.on_phenotype_changed)
        self.phenotype_panel.phenotype_deleted.connect(self.on_phenotype_changed)

        # Connect data loader signals
        self.data_loader.loading_started.connect(self.on_loading_started)
        self.data_loader.loading_finished.connect(self.on_loading_finished)
        self.data_loader.loading_progress.connect(self.on_loading_progress)
        self.data_loader.file_progress.connect(self.on_file_progress)
        self.data_loader.status_update.connect(self.on_status_update)
        self.data_loader.loading_error.connect(self.on_loading_error)
        
    def load_data(self):
        """Load MIMIC data."""
        self.status_bar.showMessage("Loading MIMIC data...")
        self.data_loader.load_data_async()
        
    def refresh_data(self):
        """Refresh data display."""
        self.patient_table.refresh()
        self.chart_viewer.clear()
        self.status_bar.showMessage("Data refreshed")
        
    def on_patient_selected(self, subject_id):
        """Handle patient selection."""
        self.status_bar.showMessage(f"Loading notes for patient {subject_id}...")
        
        # Load notes for selected patient
        notes = self.data_loader.get_patient_notes(subject_id)
        self.chart_viewer.display_notes(notes)
        
        self.status_bar.showMessage(f"Displaying {len(notes)} notes for patient {subject_id}")

    def on_phenotype_filter_selected(self, phenotype_name):
        """Handle phenotype filter selection."""
        # Apply phenotype filter to patient table
        self.patient_table.set_phenotype_filter(phenotype_name)
        self.status_bar.showMessage(f"Filtering patients by phenotype: {phenotype_name}")

    def on_phenotype_changed(self, phenotype_name):
        """Handle phenotype creation, update, or deletion."""
        # Refresh phenotype dropdowns in both patient table and chart viewer
        self.patient_table.update_phenotype_filter()
        self.chart_viewer.update_phenotype_filter()
        self.status_bar.showMessage(f"Phenotype '{phenotype_name}' updated. Dropdowns refreshed.")

    def on_loading_started(self):
        """Handle loading started."""
        self.overall_progress_bar.setVisible(True)
        self.file_progress_bar.setVisible(True)
        self.overall_progress_bar.setRange(0, 6)  # 6 steps: cache check, patients, notes, diagnoses, descriptions, cache save, completion
        self.file_progress_bar.setRange(0, 100)  # Percentage for file progress
        
    def on_loading_finished(self, patients_df, notes_df, icd9_dict, icd9_descriptions_dict, phenotype_manager):
        """Handle loading finished."""
        self.overall_progress_bar.setVisible(False)
        self.file_progress_bar.setVisible(False)

        # Update patient table
        self.patient_table.set_data(patients_df)

        # Set up phenotype manager in patient table for filtering
        self.patient_table.set_phenotype_manager(phenotype_manager)

        # Store ICD9 dictionaries in chart viewer for use in notes display
        self.chart_viewer.set_icd9_dict(icd9_dict)
        self.chart_viewer.set_icd9_descriptions_dict(icd9_descriptions_dict)

        # Store data loader reference for ICD9 descriptions access
        self.chart_viewer.set_data_loader(self.data_loader)

        # Set up phenotype manager in chart viewer for filtering
        self.chart_viewer.set_phenotype_manager(phenotype_manager)

        # Set up phenotype panel
        self.phenotype_panel.set_phenotype_manager(phenotype_manager)

        self.status_bar.showMessage(f"Loaded {len(patients_df)} patients, {len(notes_df)} notes, {len(icd9_dict)} admission diagnoses, and {len(icd9_descriptions_dict)} ICD9 descriptions")
        
    def on_loading_progress(self, value, maximum):
        """Handle overall loading progress."""
        self.overall_progress_bar.setRange(0, maximum)
        self.overall_progress_bar.setValue(value)
        
    def on_file_progress(self, filename, current, total):
        """Handle file-specific loading progress."""
        if total > 0:
            percentage = int((current / total) * 100)
            self.file_progress_bar.setValue(percentage)

            # Determine if we're dealing with file sizes, row counts, or percentages
            if total == 100:  # Percentage-based progress (new single-pass approach)
                self.file_progress_bar.setFormat(f"{filename}: {percentage}%")
            elif total > 1000000:  # Likely row count (NOTEEVENTS has millions of rows)
                # Format as row count
                self.file_progress_bar.setFormat(f"{filename}: {current:,} / {total:,} rows ({percentage}%)")
            else:
                # Format as file size
                def format_size(size_bytes):
                    if size_bytes < 1024:
                        return f"{size_bytes} B"
                    elif size_bytes < 1024 * 1024:
                        return f"{size_bytes / 1024:.1f} KB"
                    else:
                        return f"{size_bytes / (1024 * 1024):.1f} MB"

                self.file_progress_bar.setFormat(f"{filename}: {format_size(current)} / {format_size(total)} ({percentage}%)")
        else:
            self.file_progress_bar.setValue(0)
            self.file_progress_bar.setFormat(f"{filename}: Starting...")

    def on_status_update(self, message):
        """Handle status updates."""
        self.status_bar.showMessage(message)

    def on_loading_error(self, error_message):
        """Handle loading error."""
        self.overall_progress_bar.setVisible(False)
        self.file_progress_bar.setVisible(False)
        self.status_bar.showMessage("Error loading data")

        QMessageBox.critical(
            self,
            "Data Loading Error",
            f"Failed to load MIMIC data:\n{error_message}"
        )
        
    def show_cache_info(self):
        """Show cache information dialog."""
        try:
            cache_info = self.data_loader.get_cache_info()

            # Format cache information
            info_lines = [
                f"Cache Directory: {cache_info['cache_dir']}",
                f"Can Write to Cache: {'Yes' if cache_info['can_write'] else 'No'}",
                f"Cache Valid: {'Yes' if cache_info['cache_valid'] else 'No'}",
                "",
                "Cache Files Status:"
            ]

            for file_key, exists in cache_info['files_exist'].items():
                status = "✓ Exists" if exists else "✗ Missing"
                info_lines.append(f"  {file_key}: {status}")

                if exists and f'{file_key}_size' in cache_info:
                    size_mb = cache_info[f'{file_key}_size'] / (1024 * 1024)
                    info_lines.append(f"    Size: {size_mb:.1f} MB")

            QMessageBox.information(
                self,
                "Cache Information",
                "\n".join(info_lines)
            )

        except Exception as e:
            QMessageBox.warning(
                self,
                "Cache Info Error",
                f"Failed to get cache information:\n{str(e)}"
            )

    def clear_cache(self):
        """Clear cache files after confirmation."""
        reply = QMessageBox.question(
            self,
            "Clear Cache",
            "Are you sure you want to clear all cache files?\n\n"
            "This will force the application to reload data from CSV files on the next startup, "
            "which may take significantly longer.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                self.data_loader.clear_cache()
                QMessageBox.information(
                    self,
                    "Cache Cleared",
                    "Cache files have been successfully cleared.\n\n"
                    "The next data load will rebuild the cache from CSV files."
                )
                self.status_bar.showMessage("Cache cleared successfully")

            except Exception as e:
                QMessageBox.warning(
                    self,
                    "Clear Cache Error",
                    f"Failed to clear cache:\n{str(e)}"
                )

    def show_about(self):
        """Show about dialog."""
        QMessageBox.about(
            self,
            "About HAIIR",
            "HAIIR (Human-AI Interactive Review)\n"
            "Version 1.0.0\n\n"
            "A PyQt6 application for interactive medical chart review\n"
            "with AI-assisted annotation capabilities."
        )
        
    def closeEvent(self, event):
        """Handle application close event."""
        # Add any cleanup code here
        event.accept()
