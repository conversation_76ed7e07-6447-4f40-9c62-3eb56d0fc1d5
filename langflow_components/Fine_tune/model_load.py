from langflow.field_typing import Data
import torch
import os
import gc
from langflow.custom import Component
from langflow.io import Output, MultilineInput
from langflow.schema import Message
from unsloth import FastLanguageModel


class ModelLoad(Component):
    """
    A component to load a language model, apply LoRA fine-tuning, and save it to a specified directory.
    """

    display_name = "Model Load"
    description: str = (
        "Takes the name of the model and saves it to the models directory."
    )
    icon = "code"
    name = "model_loading"

    inputs = [
        MultilineInput(
            name="base_model",
            value="unsloth/Meta-Llama-3.1-8B-Instruct",
            display_name="Base Model",
        )
    ]

    outputs = [Output(display_name="path", name="path", method="build_model")]

    def build_model(self) -> Message:
        """
        Loads the model, applies LoRA fine-tuning, and saves it to the specified directory.
        Returns the path where the model is saved.
        """
        model_path = "./models/" + self.base_model

        # Check if the model directory already exists
        if not os.path.exists(model_path):
            os.makedirs(model_path)
        else:
            return Message(text=model_path)

        try:
            # Clear GPU cache to free up memory
            torch.cuda.empty_cache()

            # Load the base model and tokenizer
            model, tokenizer = FastLanguageModel.from_pretrained(
                model_name=self.base_model,
                dtype=None,
                load_in_4bit=True,
                device_map="cuda",
            )

            # Apply LoRA fine-tuning to the model
            model = FastLanguageModel.get_peft_model(
                model,
                r=128,  # LoRA rank (suggested values: 8, 16, 32, 64, 128)
                target_modules=[
                    "q_proj",
                    "k_proj",
                    "v_proj",
                    "o_proj",
                    "gate_proj",
                    "up_proj",
                    "down_proj",
                ],
                lora_alpha=16,
                lora_dropout=0,
                bias="none",
                use_gradient_checkpointing="unsloth",
                random_state=3407,
                use_rslora=False,
                loftq_config=None,
            )

            # Define a custom chat template for the tokenizer
            llama3_template = """
            {% set loop_messages = messages %}
            {% for message in loop_messages %}
                {% if message['role'] == 'user' %}
                    {{ '<|start_header_id|>user<|end_header_id|>\n\n' + message['content'] + '<|eot_id|>' }}
                {% elif message['role'] == 'assistant' %}
                    {{ '<|start_header_id|>assistant<|end_header_id|>\n\n' + message['content'] + '<|eot_id|>' }}
                {% endif %}
            {% endfor %}
            {% if add_generation_prompt %}{{ '<|start_header_id|>assistant<|end_header_id|>\n\n' }}{% endif %}
            """
            tokenizer.chat_template = llama3_template

            # Save the fine-tuned model and tokenizer
            model.save_pretrained(model_path)
            tokenizer.save_pretrained(model_path)

            # Clean up memory
            del model
            del tokenizer
            gc.collect()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

            return Message(text=model_path)
        except Exception as e:
            # Return the model path even if an error occurs
            print(f"An error occurred: {e}")
            return Message(text=model_path)
