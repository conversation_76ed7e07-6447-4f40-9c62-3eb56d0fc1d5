from langflow.custom import Component
from langflow.io import DataFrameInput, MultilineInput, MessageTextInput, Output
from langflow.schema import DataFrame
from tqdm import tqdm
from unsloth import FastLanguageModel
import pandas as pd
import gc
import torch


class ModelInference(Component):
    """
    A component to perform inference using a fine-tuned language model.
    This component takes patient notes, formats them into prompts, and generates model predictions.
    """

    display_name = "Model Inference"  # Display name for the component in the UI
    description = """
    Perform inference using a fine-tuned language model.
    This component formats patient notes into prompts, generates predictions, and returns the results.
    """
    documentation: str = (
        "http://docs.langflow.org/components/custom"  # Link to documentation
    )
    icon = "code"  # Icon representing the component in the UI
    name = "ModelInference"  # Internal name of the component

    # Define input fields
    inputs = [
        DataFrameInput(
            name="patient_notes",  # Internal name for the patient notes input
            display_name="Patient Notes",  # Display name for the patient notes input
            required=True,  # This input is mandatory
            info="A DataFrame containing patient notes.",
        ),
        MultilineInput(
            name="input_prompt_template",  # Internal name for the input prompt template
            display_name="Input Prompt Template",  # Display name for the input prompt template
            info="Template for the input prompt. Use `{}` as a placeholder for the patient note.",
        ),
        MessageTextInput(
            name="model_path",  # Internal name for the model path input
            display_name="Model Path",  # Display name for the model path input
            info="Path to the fine-tuned model.",  # Help text for the input
            value="finetuned_model",  # Default value for the model path
        ),
    ]

    # Define output fields
    outputs = [
        Output(
            display_name="Model Predictions",  # Display name for the output
            name="model_predictions",  # Internal name for the output
            method="generate_predictions",  # Method to generate the output
        ),
    ]

    def _generate_predictions(self, model, tokenizer, notes: list, device: str) -> list:
        """
        Generate predictions for the given patient notes using the fine-tuned model.

        Args:
            model: The fine-tuned language model.
            tokenizer: The tokenizer for the model.
            notes (list): A list of patient notes.
            device (str): The device to run the model on (e.g., "cuda" or "cpu").

        Returns:
            list: A list of model predictions.
        """
        predictions = []
        for note in tqdm(notes, desc="Generating predictions"):
            # Format the input prompt using the patient note
            prompt = self.input_prompt_template.format(note)
            chat = [{"role": "user", "content": prompt}]
            formatted_input = tokenizer.apply_chat_template(chat, tokenize=False)

            # Tokenize the input and generate predictions
            tokenized_input = tokenizer(
                formatted_input, padding=True, truncation=True, return_tensors="pt"
            ).to(device)
            input_length = tokenized_input["input_ids"].shape[1]
            output = model.generate(**tokenized_input, max_new_tokens=200)
            generated_tokens = output[0, input_length:]
            output_text = tokenizer.decode(generated_tokens, skip_special_tokens=True)

            # Convert the output text to a label and add to predictions
            predictions.append(output_text)
        return predictions

    def generate_predictions(self) -> DataFrame:
        """
        Perform inference using the fine-tuned model and return the predictions.

        Returns:
            DataFrame: A DataFrame containing the model's predictions.
        """
        # Set the device (GPU if available, otherwise CPU)
        device = "cuda" if torch.cuda.is_available() else "cpu"

        # Clear GPU memory if a model is already loaded
        if "model" in globals():
            del model
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        # Load the fine-tuned model and tokenizer
        model, tokenizer = FastLanguageModel.from_pretrained(
            self.model_path,
            load_in_4bit=True,
            device_map="auto",
        )
        model = FastLanguageModel.for_inference(model)

        # Extract patient notes and generate predictions
        patient_notes = self.patient_notes["note"]
        predictions = self._generate_predictions(
            model, tokenizer, patient_notes, device
        )

        # Clean up resources
        del model
        del tokenizer
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        # Return the predictions as a DataFrame
        return DataFrame(pd.DataFrame(predictions, columns=["model_output"]))
