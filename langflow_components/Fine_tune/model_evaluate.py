from langflow.custom import Component
from langflow.io import DataFrameInput, Output
from langflow.schema import Data
import matplotlib.pyplot as plt
from sklearn.metrics import (
    accuracy_score,
    precision_score,
    recall_score,
    f1_score,
    confusion_matrix,
    ConfusionMatrixDisplay,
    classification_report,
)


class EvaluateModel(Component):
    """
    A component to evaluate the performance of a classification model.
    This component computes metrics such as accuracy, precision, recall, F1-score,
    and generates a confusion matrix and classification report.
    """

    display_name = "Model Evaluation"  # Display name for the component in the UI
    description = """
    Evaluate the performance of a model by comparing true labels with model predictions.
    This component computes metrics such as accuracy, precision, recall, F1-score, and generates a confusion matrix.
    """
    documentation: str = (
        "http://docs.langflow.org/components/custom"  # Link to documentation
    )
    icon = "code"  # Icon representing the component in the UI
    name = "EvaluateModel"  # Internal name of the component

    # Define input fields
    inputs = [
        DataFrameInput(
            name="true_labels",  # Internal name for the true labels input
            display_name="True Labels",  # Display name for the true labels input
            required=True,  # This input is mandatory
            info="A DataFrame containing the true labels.",
        ),
        DataFrameInput(
            name="model_predictions",  # Internal name for the model predictions input
            display_name="Model Predictions",  # Display name for the model predictions input
            required=True,  # This input is mandatory
            info="A DataFrame containing the model's predictions.",
        ),
    ]

    # Define output fields
    outputs = [
        Output(
            display_name="Evaluation Results",  # Display name for the output
            name="evaluation_results",  # Internal name for the output
            method="evaluate_model",  # Method to generate the output
        ),
    ]

    def _convert_predictions(self, predictions: list) -> list:
        """
        Convert model predictions into a consistent format (0 or 1).
        Predictions that cannot be converted are treated as unsure.

        Args:
            predictions (list): A list of model predictions.

        Returns:
            list: A list of converted predictions (0 or 1).
        """
        unsure = 0
        converted = []
        for pred in predictions:
            try:
                # Extract the last part of the prediction string and convert to integer
                converted.append(int(pred.split(":")[-1].strip()))
            except (ValueError, AttributeError):
                # If conversion fails, treat as unsure (0)
                unsure += 1
                converted.append(0)
        return converted, unsure

    def evaluate_model(self) -> Data:
        """
        Evaluate the model's performance by comparing true labels with predictions.

        Returns:
            Data: A Data object containing evaluation metrics, classification report,
                  confusion matrix, and unsure predictions.
        """
        # Extract true labels and model predictions
        true_labels = list(self.true_labels["label"])
        model_predictions = list(self.model_predictions["model_output"])

        # Convert predictions to a consistent format
        converted_predictions, unsure = self._convert_predictions(model_predictions)

        # Compute evaluation metrics
        accuracy = accuracy_score(true_labels, converted_predictions)
        precision = precision_score(true_labels, converted_predictions)
        recall = recall_score(true_labels, converted_predictions)
        f1 = f1_score(true_labels, converted_predictions)
        conf_matrix = confusion_matrix(true_labels, converted_predictions)
        report = classification_report(true_labels, converted_predictions)

        # Generate and save the confusion matrix plot
        cm_display = ConfusionMatrixDisplay(
            confusion_matrix=conf_matrix, display_labels=[0, 1]
        )
        cm_display.plot(cmap="Blues")
        plt.title(f"Confusion Matrix\nAccuracy: {accuracy:.2f}")
        plt.savefig("confusion_matrix.png", bbox_inches="tight", pad_inches=0.5)
        plt.show()

        # Prepare the evaluation results
        evaluation_results = {
            "accuracy": accuracy,
            "precision": precision,
            "recall": recall,
            "f1": f1,
            "classification_report": report,
            "confusion_matrix": conf_matrix.tolist(),  # Convert numpy array to list for serialization
            "true_labels": true_labels,
            "predictions": converted_predictions,
            "unsure": unsure,
        }

        # Return the results as a Data object
        return Data(data=evaluation_results)
