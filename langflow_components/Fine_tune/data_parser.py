from langflow.custom import Component
from langflow.io import MultilineInput, Output
from langflow.schema import DataFrame
import pandas as pd
import os


class DataExtractor(Component):
    """
    A component to extract and process patient notes (X) and depression labels (Y) from CSV files.
    This component reads the input files, extracts the relevant columns, and outputs them as DataFrames.
    """

    display_name = "Data Parser"  # Display name for the component in the UI
    description = """
    Extract patient notes (X) and depression labels (Y) from CSV files.
    The component reads the input files, processes them, and outputs the extracted data as DataFrames.
    """
    documentation: str = (
        "http://docs.langflow.org/components/custom"  # Link to documentation
    )
    icon = "code"  # Icon representing the component in the UI
    name = "DataExtractor"  # Internal name of the component

    # Define input fields
    inputs = [
        MultilineInput(
            name="patient_notes_path",  # Internal name for the patient notes file path
            display_name="Patient Notes Path",  # Display name for the patient notes file path
            required=True,  # This input is mandatory
            info="Path to the CSV file containing patient notes.",
        ),
        MultilineInput(
            name="depression_labels_path",  # Internal name for the depression labels file path
            display_name="Depression Labels Path",  # Display name for the depression labels file path
            required=True,  # This input is mandatory
            info="Path to the CSV file containing depression labels.",
        ),
    ]

    # Define output fields
    outputs = [
        Output(
            display_name="Patient Notes",  # Display name for the patient notes output
            name="patient_notes",  # Internal name for the patient notes output
            method="extract_patient_notes",  # Method to generate the output
        ),
        Output(
            display_name="Depression Labels",  # Display name for the depression labels output
            name="depression_labels",  # Internal name for the depression labels output
            method="extract_depression_labels",  # Method to generate the output
        ),
    ]

    def _read_csv_file(self, file_path: str) -> pd.DataFrame:
        """
        Read a CSV file and return its contents as a DataFrame.

        Args:
            file_path (str): Path to the CSV file.

        Returns:
            pd.DataFrame: DataFrame containing the file data.

        Raises:
            ValueError: If the file cannot be read.
        """
        try:
            return pd.read_csv(file_path)
        except Exception as e:
            raise ValueError(f"Failed to read the file at {file_path}: {e}")

    def extract_patient_notes(self) -> DataFrame:
        """
        Extract patient notes from the input CSV file.

        Returns:
            DataFrame: A DataFrame containing the patient notes.
        """
        df = self._read_csv_file(self.patient_notes_path)
        return DataFrame({"note": df["note"]})  # Extract the "note" column

    def extract_depression_labels(self) -> DataFrame:
        """
        Extract depression labels from the input CSV file.

        Returns:
            DataFrame: A DataFrame containing the depression labels.
        """
        df = self._read_csv_file(self.depression_labels_path)
        return DataFrame({"label": df["label"]})  # Extract the "label" column
