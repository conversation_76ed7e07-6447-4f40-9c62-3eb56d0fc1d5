from langflow.custom import Component
from langflow.io import Output, MultilineInput, DataFrameInput, MessageTextInput
from langflow.schema import DataFrame
import pandas as pd
from transformers import AutoTokenizer
import gc
import torch


class PromptBuilder(Component):
    """
    A component to build prompts for training or inference using a template.
    This component takes a note and label, formats them into a prompt using a template,
    and applies a tokenizer to generate the final prompt structure.
    """

    display_name = "Prompt Builder"  # Display name for the component in the UI
    description = """
    Build prompts for training or inference by providing a template with placeholders for notes and labels.
    The component formats the input data into a structured prompt and applies a tokenizer to generate the final output.
    """
    icon = "code"  # Icon representing the component in the UI
    name = "PromptBuilder"  # Internal name of the component

    # Define input fields
    inputs = [
        DataFrameInput(
            name="note_data",  # Internal name for the note input
            display_name="Note Data",  # Display name for the note input
            required=True,  # This input is mandatory
        ),
        DataFrameInput(
            name="label_data",  # Internal name for the label input
            display_name="Label Data",  # Display name for the label input
            required=True,  # This input is mandatory
        ),
        MessageTextInput(
            name="tokenizer_path",  # Internal name for the tokenizer input
            display_name="Tokenizer Path",  # Display name for the tokenizer input
            info="Path to the tokenizer to be used for prompt formatting.",  # Help text for the input
            tool_mode=True,  # Indicates this input is used in tool mode
        ),
        MultilineInput(
            name="input_template",  # Internal name for the input prompt template
            display_name="Input Prompt Template",  # Display name for the input prompt template
            info="Template for the user input prompt. Use `{}` as a placeholder for the note.",
        ),
        MultilineInput(
            name="output_template",  # Internal name for the output prompt template
            display_name="Output Prompt Template",  # Display name for the output prompt template
            value="**The final answer is: {}**",  # Default value for the output template
            info="Template for the model's output prompt. Use `{}` as a placeholder for the label.",
        ),
    ]

    # Define output fields
    outputs = [
        Output(
            display_name="Formatted Prompts",  # Display name for the output
            name="formatted_prompts",  # Internal name for the output
            method="build_prompts",  # Method to generate the output
        )
    ]

    def format_chat_prompt(self, note: str, label: str, tokenizer) -> str:
        """
        Format the note and label into a structured chat prompt using the provided templates.

        Args:
            note (str): The note to be included in the prompt.
            label (str): The label to be included in the prompt.
            tokenizer: The tokenizer to apply the chat template.

        Returns:
            str: The formatted chat prompt.
        """
        user_content = self.input_template.format(
            note
        )  # Format the user input template
        assistant_content = self.output_template.format(
            label
        )  # Format the assistant output template
        chat = [
            {"role": "user", "content": user_content},
            {"role": "assistant", "content": assistant_content},
        ]
        return tokenizer.apply_chat_template(
            chat, tokenize=False
        )  # Apply the tokenizer's chat template

    def build_prompts(self) -> DataFrame:
        """
        Build and format prompts for all note-label pairs in the input data.

        Returns:
            DataFrame: A DataFrame containing the formatted prompts.
        """
        notes = self.note_data["note"]  # Extract notes from the input DataFrame
        labels = self.label_data["label"]  # Extract labels from the input DataFrame
        tokenizer = AutoTokenizer.from_pretrained(
            self.tokenizer_path
        )  # Load the tokenizer

        formatted_prompts = []
        for note, label in zip(notes, labels):
            prompt = self.format_chat_prompt(
                note, label, tokenizer
            )  # Format each note-label pair
            formatted_prompts.append(prompt)

        # Clean up resources
        del tokenizer
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        # Return the formatted prompts as a DataFrame
        return DataFrame(pd.DataFrame(formatted_prompts, columns=["prompt"]))
