from langflow.custom import Component
from langflow.io import MessageTextInput, Output, DataFrameInput
from langflow.schema import Message
from trl import SFTTrainer, SFTConfig
from datasets import Dataset
from unsloth import FastLanguageModel, is_bfloat16_supported
import gc
import torch
import os
import random
import shutil


class ModelTrainer(Component):
    """
    A component for fine-tuning a language model using the provided prompts and configuration.
    This component handles model loading, training, and saving the fine-tuned model to a specified path.
    """

    display_name = "Model Fine-Tuning"  # Display name for the component in the UI
    description = """
    Fine-tune a pre-trained language model using the provided prompts and training configuration.
    The component supports LoRA (Low-Rank Adaptation) for efficient fine-tuning and saves the fine-tuned model to the specified path.
    """
    documentation: str = (
        "http://docs.langflow.org/components/custom"  # Link to documentation
    )
    icon = "code"  # Icon representing the component in the UI
    name = "ModelFineTuning"  # Internal name of the component

    # Define input fields
    inputs = [
        DataFrameInput(
            name="prompts",  # Internal name for the prompts input
            display_name="Prompts",  # Display name for the prompts input
            required=True,  # This input is mandatory
        ),
        MessageTextInput(
            name="model_path",  # Internal name for the model path input
            display_name="Model Path",  # Display name for the model path input
            required=True,  # This input is mandatory
        ),
        MessageTextInput(
            name="save_path",  # Internal name for the save path input
            display_name="Save Path",  # Display name for the save path input
            info="Path to save the fine-tuned model.",  # Help text for the input
            value="finetuned_model",  # Default value for the save path
            tool_mode=True,  # Indicates this input is used in tool mode
        ),
        MessageTextInput(
            name="training_config",  # Internal name for the training config input
            display_name="Training Config",  # Display name for the training config input
            info="Configuration for fine-tuning parameters.",  # Help text for the input
            value="""{
                'learning_rate': 0.0003068487651615263,
                'gradient_accumulation_steps': 16,
                'epochs': 1,
                'weight_decay': 0.02857038520567239,
                'warmup_ratio': 0.1757736057365846
            }""",  # Default configuration
            tool_mode=True,  # Indicates this input is used in tool mode
        ),
    ]

    # Define output fields
    outputs = [
        Output(
            display_name="Fine-Tuned Model Path",  # Display name for the output
            name="fine_tuned_model_path",  # Internal name for the output
            method="fine_tune_model",  # Method to generate the output
        )
    ]

    def train_model(self) -> str:
        """
        Fine-tune the model using the provided prompts and configuration.

        Returns:
            str: The path where the fine-tuned model is saved.
        """
        # Load the training configuration
        config = eval(self.training_config)

        # Load the pre-trained model and tokenizer
        model, tokenizer = FastLanguageModel.from_pretrained(model_name=self.model_path)

        # Prepare the training dataset
        data = Dataset.from_dict({"text": self.prompts["prompt"]})

        # Define the save path for the fine-tuned model
        ft_pth = f"./{self.save_path}/{self.model_path}"

        if os.path.exists(ft_pth):
            # Remove the directory and its contents
            shutil.rmtree(ft_pth)

        # Recreate the directory
        os.makedirs(ft_pth)

        # Initialize the SFTTrainer
        self._training(model, tokenizer, data, ft_pth, config)

        # Clean up resources
        del model
        del tokenizer
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        return ft_pth

    def _training(self, model, tokenizer, data, ft_pth, config):
        """
        Fine-tune the model using the provided training data and configuration.
        """
        trainer = SFTTrainer(
            model=model,
            tokenizer=tokenizer,
            train_dataset=data,
            args=SFTConfig(
                packing=False,
                dataset_num_proc=5,
                dataset_text_field="text",
                learning_rate=config["learning_rate"],
                per_device_train_batch_size=8,
                gradient_accumulation_steps=config["gradient_accumulation_steps"],
                warmup_steps=4,
                num_train_epochs=config["epochs"],
                fp16=not is_bfloat16_supported(),
                bf16=is_bfloat16_supported(),
                logging_steps=1,
                optim="adamw_8bit",
                weight_decay=config["weight_decay"],
                lr_scheduler_type="linear",
                output_dir="checkpoints",
                report_to="none",
                logging_dir="logs",
            ),
        )
        print(f"Trainer: {trainer}")

        # Train the model
        trained = trainer.train()
        print(trained)
        trainer.save_model(ft_pth)
        tokenizer.save_pretrained(ft_pth)

    def fine_tune_model(self) -> Message:
        """
        Execute the fine-tuning process and return the path to the fine-tuned model.

        Returns:
            Message: A message containing the path to the fine-tuned model.
        """
        save_path = self.train_model()
        return Message(text=save_path)
