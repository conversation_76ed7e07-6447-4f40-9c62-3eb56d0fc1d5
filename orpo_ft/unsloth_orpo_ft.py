# %%
import os
import sys
import json
import argparse

import pandas as pd
import torch
from datasets import Dataset
from unsloth import FastLanguageModel
from trl import ORPOConfig, ORPOTrainer

from utils.prefered_rejected import *
from utils.prompt import *
from utils.criteria import CriteriaType

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Train ORPO model with specified criteria and model using Unsloth')
    
    # Model and criteria arguments
    parser.add_argument(
        '--model_name', 
        type=str, 
        default="unsloth/Llama-3.1-8B-Instruct",
        help='Model name to use for training (default: unsloth/Llama-3.1-8B-Instruct)'
    )
    
    parser.add_argument(
        '--criteria', 
        type=str, 
        choices=[c.value for c in CriteriaType],
        required=True,
        help='Criteria type to use for training (choices: diagnosis, treatment, self_harm)'
    )
    
    # Data arguments
    parser.add_argument(
        '--pickle_path',
        type=str,
        required=True,
        help='Path to the pickle file containing the data'
    )
    
    # Model configuration arguments
    parser.add_argument(
        '--max_seq_length',
        type=int,
        default=2048,
        help='Maximum sequence length (default: 2048)'
    )
    
    parser.add_argument(
        '--load_in_4bit',
        action='store_true',
        default=True,
        help='Load model in 4-bit quantization (default: 8-bit)'
    )
    
    return parser.parse_args()

def main():
    # Parse command line arguments
    args = parse_arguments()
    
    # Convert string criteria to CriteriaType enum
    try:
        criteria_type = CriteriaType(args.criteria.lower())
    except ValueError:
        print(f"Invalid criteria: {args.criteria}")
        print(f"Valid options: {[c.value.lower() for c in CriteriaType]}")
        sys.exit(1)
    
    model_name = args.model_name
    
    print(f"Using model: {model_name}")
    print(f"Using criteria: {criteria_type.value}")
    
    # Get GPU ID from environment variable
    gpu_id = os.environ.get("CUDA_VISIBLE_DEVICES", "0")
    print(f"Using GPU ID: {gpu_id}")
    
    hf_token = os.getenv("HF_TOKEN")
    
    criteria = criteria_type.value
    criteria_chosen = f"{criteria}_chosen"
    criteria_rejected = f"{criteria}_rejected"
    
    # Load and prepare data
    df = pd.read_pickle(args.pickle_path)
    print(f"Loaded {len(df)} samples from {args.pickle_path}")
    if f"{criteria}_chosen" not in df.columns:
        df = extract_min_max(df, criteria)
        df.to_pickle(args.pickle_path)
    df.rename(columns={"note": "chart"}, inplace=True)
    preference_df = df[["chart", f"{criteria}_chosen", f"{criteria}_rejected"]].copy()
    preference_df = preference_df.dropna().reset_index(drop=True)
    print(f"Using {len(preference_df)} samples for training after cleaning")
    
    # Debug: Print sample data info
    if len(preference_df) > 0:
        row = 0
        chart = df["chart"][row]
        diagnosis = df[criteria][row][-1]
        print(f"Sample diagnosis score: {diagnosis.score(chart)}")
        
        # Print JSON dump of criteria data
        a = [x.model_dump_json() for x in df[criteria][row]]
        print(f"Sample criteria data: [{','.join(a)}]")
    
    # Load model and tokenizer using Unsloth
    print("Loading model with Unsloth...")
    model, tokenizer = FastLanguageModel.from_pretrained(
        model_name=model_name,
        max_seq_length=args.max_seq_length,
        dtype=None,  # None for auto detection. Float16 for Tesla T4, V100, Bfloat16 for Ampere+
        load_in_4bit=args.load_in_4bit,
        load_in_8bit=not args.load_in_4bit,  # Default to 8-bit if not 4-bit
        token=hf_token,
    )
    
    # Apply LoRA using Unsloth
    print("Applying LoRA with Unsloth...")
    model = FastLanguageModel.get_peft_model(
        model,
        r=16,  # Choose any number > 0 ! Suggested 8, 16, 32, 64, 128
        target_modules=["q_proj", "k_proj", "v_proj", "o_proj",
                        "gate_proj", "up_proj", "down_proj",],
        lora_alpha=16,
        lora_dropout=0,  # Supports any, but = 0 is optimized
        bias="none",    # Supports any, but = "none" is optimized
        # [NEW] "unsloth" uses 30% less VRAM, fits 2x larger batch sizes!
        use_gradient_checkpointing="unsloth",  # True or "unsloth" for very long context
        random_state=3407,
        use_rslora=False,  # We support rank stabilized LoRA
        loftq_config=None, # And LoftQ
    )
    
    # Set padding token
    tokenizer.pad_token = tokenizer.eos_token
    tokenizer.padding_side = "right"
    
    # Load preference data
    dataset = load_preference_data(preference_df, tokenizer, "chart", criteria_chosen, criteria_rejected, *get_prompts(criteria_type))
    print(f"Dataset size: {len(dataset)}")
    
    # Configure ORPO training arguments
    training_args = ORPOConfig(
        num_train_epochs=3,
        per_device_train_batch_size=2,  # Increased due to Unsloth efficiency
        gradient_accumulation_steps=4,
        beta=0.1,  # The beta parameter for ORPO loss
        logging_steps=10,
        max_grad_norm=0.3,
        optim="adamw_8bit",
        lr_scheduler_type="linear",
        output_dir="./orpo-checkpoints",
        learning_rate=2e-4,  # Higher learning rate with Unsloth
        warmup_ratio=0.03,
        save_strategy="epoch",
        fp16=not torch.cuda.is_bf16_supported(),
        bf16=torch.cuda.is_bf16_supported(),
        report_to="tensorboard",
        dataloader_num_workers=2,
        remove_unused_columns=False,
    )
    
    # Create ORPO Trainer
    orpo_trainer = ORPOTrainer(
        model=model,
        processing_class=tokenizer,
        train_dataset=dataset,
        args=training_args,
    )
    
    # Train the model
    print("Starting ORPO training with Unsloth...")
    orpo_trainer.train()
    print("ORPO training completed!")
    
    # Save the trained model
    os.makedirs("./model/orpo_model/", exist_ok=True)
    output_dir = f"./model/orpo_model/{model_name.replace('/', '_')}-{criteria}"
    
    print(f"Saving model to {output_dir}")
    
    # Save in HuggingFace format
    FastLanguageModel.for_inference(model)
    model.save_pretrained(output_dir)
    tokenizer.save_pretrained(output_dir)
    

    # Save in GGUF format for Ollama
    print("Saving model in GGUF format...")
    
    # Save only q4_0 quantization as a single .gguf file
    try:
        print("Saving with quantization: q4_0")
        gguf_filename = f"{output_dir.replace('/', '_')}-q4_0.gguf"
        model, tokenizer = FastLanguageModel.from_pretrained(model_name = output_dir, load_in_4bit = True)
        FastLanguageModel.for_inference(model)
        model.save_pretrained_gguf(
            gguf_filename,
            tokenizer,
            quantization_method="q4_0",
        )
        print(f"GGUF model saved to {gguf_filename}")
        
    except Exception as e:
        print(f"Failed to save GGUF model: {str(e)}")
    
    print("Model training and saving completed!")
    print(f"Models saved in multiple formats:")
    print(f"  - HuggingFace format: {output_dir}")
    print(f"  - GGUF file: {output_dir.replace('/', '_')}-q4_0.gguf")

if __name__ == "__main__":
    main()