# %%
import os
import sys
import json
import argparse

import pandas as pd
import torch
from datasets import Dataset
from transformers import (
    AutoModelForCausalLM,
    AutoTokenizer,
    BitsAndBytesConfig,
)
from peft import LoraConfig, get_peft_model, prepare_model_for_kbit_training
from trl import ORPOConfig, ORPOTrainer

from utils.prefered_rejected import *
from utils.prompt import *
from utils.criteria import CriteriaType
from utils.model_convert import convert_hf_to_ollama

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Train ORPO model with specified criteria and model')
    
    # Model and criteria arguments
    parser.add_argument(
        '--model_name', 
        type=str, 
        default="meta-llama/Llama-3.1-8B-Instruct",
        help='Model name to use for training (default: meta-llama/Llama-3.1-8B-Instruct)'
    )
    
    parser.add_argument(
        '--criteria', 
        type=str, 
        choices=[c.value for c in CriteriaType],
        required=True,
        help='Criteria type to use for training (choices: diagnosis, treatment, self_harm)'
    )
    
    # Data arguments
    parser.add_argument(
        '--pickle_path',
        type=str,
        required=True,
        help='Path to the pickle file containing the data'
    )
    
    return parser.parse_args()

def main():
    # Parse command line arguments
    args = parse_arguments()
    
    # Convert string criteria to CriteriaType enum
    try:
        criteria_type = CriteriaType(args.criteria.lower())
    except ValueError:
        print(f"Invalid criteria: {args.criteria}")
        print(f"Valid options: {[c.value.lower() for c in CriteriaType]}")
        sys.exit(1)
    
    model_name = args.model_name
    
    print(f"Using model: {model_name}")
    print(f"Using criteria: {criteria_type.value}")
    
    # Get GPU ID from environment variable
    
    gpu_id = os.environ["CUDA_VISIBLE_DEVICES"]
    print(f"Using GPU ID: {gpu_id}")
    
    hf_token = os.getenv("HF_TOKEN")
    
    criteria = criteria_type.value
    criteria_chosen = f"{criteria}_chosen"
    criteria_rejected = f"{criteria}_rejected"
    
    # Load and prepare data (use all data)
    df = pd.read_pickle(args.pickle_path)
    print(f"Loaded {len(df)} samples from {args.pickle_path}")
    if f"{criteria}_chosen" not in df.columns:
        df = extract_min_max(df, criteria)
        df.to_pickle(args.pickle_path)
    df.rename(columns={"note": "chart"}, inplace=True)
    preference_df = df[["chart", f"{criteria}_chosen", f"{criteria}_rejected"]].copy()
    preference_df = preference_df.dropna().reset_index(drop=True)
    print(f"Using {len(preference_df)} samples for training after cleaning")
    
    # Debug: Print sample data info
    if len(preference_df) > 0:
        row = 0
        chart = df["chart"][row]
        diagnosis = df[criteria][row][-1]
        print(f"Sample diagnosis score: {diagnosis.score(chart)}")
        
        # Print JSON dump of criteria data
        a = [x.model_dump_json() for x in df[criteria][row]]
        print(f"Sample criteria data: [{','.join(a)}]")
    
    # Load tokenizer and model
    tokenizer = AutoTokenizer.from_pretrained(model_name, token=hf_token)
    
    quantization_config = BitsAndBytesConfig(
    load_in_8bit=True,
    llm_int8_threshold=6.0,
    llm_int8_has_fp16_weight=False)
    
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        device_map="auto",
        quantization_config=quantization_config,
        token=hf_token
    )
    
    tokenizer.pad_token = tokenizer.eos_token  # Set padding token
    tokenizer.padding_side = "right"
    
    # Set custom chat template
    custom_template = """{% set loop_messages = messages %}{% for message in loop_messages %}{% set content = '<|start_header_id|>' + message['role'] + '<|end_header_id|>' + message['content'] + '<|eot_id|>' %}{% if loop.index0 == 0 %}{% set content = '<|begin_of_text|>' + content %}{% endif %}{{ content }}{% endfor %}{% if add_generation_prompt %}{{ '<|start_header_id|>assistant<|end_header_id|>' }}{% endif %}"""
    tokenizer.chat_template = custom_template
    
    model.gradient_checkpointing_enable()
    
    # Apply LoRA for parameter-efficient fine-tuning (hardcoded values)
    lora_config = LoraConfig(
        r=5,                   # Rank dimension
        lora_alpha=16,         # LoRA scaling factor
        target_modules=["q_proj", "v_proj", "k_proj", "o_proj"],  # Target attention modules
        lora_dropout=0.05,     # Dropout probability
        bias="none",           # Bias configuration
        task_type="CAUSAL_LM"  # Task type
    )
    model = prepare_model_for_kbit_training(model)
    model = get_peft_model(model, lora_config)
    model.print_trainable_parameters()  # Print trainable parameters
    model.train()
    model.config.use_cache = False
    
    # Load preference data
    dataset = load_preference_data(preference_df, tokenizer, "chart", criteria_chosen, criteria_rejected, *get_prompts(criteria_type))
    print(f"Dataset size: {len(dataset)}")
    
    # Configure ORPO training arguments (hardcoded values)
    training_args = ORPOConfig(
        num_train_epochs=3,
        per_device_train_batch_size=1,
        gradient_accumulation_steps=2,
        beta=0.1,  # The beta parameter for ORPO loss
        logging_steps=1,
        max_grad_norm=0.5,
        optim="adamw_8bit",
        lr_scheduler_type="linear",
        output_dir="./orpo-checkpoints",
        learning_rate=1e-6,
        warmup_ratio=0.1,
        save_strategy="epoch",
        fp16=False,
        bf16=True,
        report_to="tensorboard",
    )
    
    # Create ORPO Trainer
    orpo_trainer = ORPOTrainer(
        model=model,
        processing_class=tokenizer,
        train_dataset=dataset,
        args=training_args,
    )
    
    # Train the model
    print("Starting ORPO training...")
    orpo_trainer.train()
    print("ORPO training completed!")
    
    # Save the trained model
    os.makedirs("./model/orpo_model/", exist_ok=True)
    output_dir = f"./model/orpo_model/{model_name}-{criteria}"
    trained_model = orpo_trainer.model
    trained_model = trained_model.merge_and_unload()
    trained_model.save_pretrained(output_dir, safe_serialization=True)
    tokenizer.save_pretrained(output_dir)
    print(f"Merged model saved to {output_dir}")
    
    # Convert to Ollama format
    print("Converting to Ollama format...")
    convert_hf_to_ollama(f"{model_name}-{criteria}")
    print("Ollama conversion completed!")

if __name__ == "__main__":
    main()