import pandas as pd

def compare_score_counts_detailed(untuned_path, tuned_path, n_rows=100, columns=None):
    """
    Compare value counts for three score columns between untuned and tuned model responses.
    Analyzes each column individually and combined.
    
    Parameters:
    untuned_path (str): Path to untuned model pickle file
    tuned_path (str): Path to tuned model pickle file
    n_rows (int): Number of rows to analyze (default 100)
    columns (list): Column names to analyze. If None, uses default score columns.
    
    Returns:
    dict: Dictionary containing DataFrames for each column and combined analysis
    """
    if columns is None:
        columns = ["diagnosis_score", "treatment_score", "self_harm_score"]
    
    def get_individual_counts(file_path, column):
        """Get value counts for a single column"""
        df = pd.read_pickle(file_path)[:n_rows]
        counts = df[column].explode().value_counts().sort_index()
        return counts
    
    def get_combined_counts(file_path):
        """Get combined value counts for all columns"""
        df = pd.read_pickle(file_path)[:n_rows]
        combined_counts = pd.concat([df[col].explode() for col in columns]).value_counts().sort_index()
        return combined_counts
    
    def create_comparison_df(counts_untuned, counts_tuned, analysis_name):
        """Create comparison DataFrame with counts and fractions"""
        # Get all unique scores from both files
        all_scores = sorted(set(counts_untuned.index) | set(counts_tuned.index))
        
        # Create result DataFrame
        result_df = pd.DataFrame({
            'score': all_scores,
            'count_untuned': [counts_untuned.get(score, 0) for score in all_scores],
            'count_finetuned': [counts_tuned.get(score, 0) for score in all_scores]
        })
        
        # Calculate fractions
        total_untuned = result_df['count_untuned'].sum()
        total_finetuned = result_df['count_finetuned'].sum()
        
        if total_untuned > 0:
            result_df['frac_untuned'] = result_df['count_untuned'] / total_untuned
        else:
            result_df['frac_untuned'] = 0
            
        if total_finetuned > 0:
            result_df['frac_finetuned'] = result_df['count_finetuned'] / total_finetuned
        else:
            result_df['frac_finetuned'] = 0
        
        return result_df
    
    # Dictionary to store all results
    results = {}
    
    # Analyze each column individually
    for column in columns:
        counts_untuned = get_individual_counts(untuned_path, column)
        counts_tuned = get_individual_counts(tuned_path, column)
        results[column] = create_comparison_df(counts_untuned, counts_tuned, column)
    
    # Analyze combined columns
    counts_untuned_combined = get_combined_counts(untuned_path)
    counts_tuned_combined = get_combined_counts(tuned_path)
    results['combined'] = create_comparison_df(counts_untuned_combined, counts_tuned_combined, 'combined')
    
    return results

def print_analysis_summary(results):
    """Print a summary of the analysis results"""
    print("="*80)
    print("SCORE COMPARISON ANALYSIS SUMMARY")
    print("="*80)
    
    for analysis_name, df in results.items():
        print(f"\n{analysis_name.upper()} ANALYSIS:")
        print("-" * 50)
        print(df.round(4))
        
            


def save_results_to_excel(results, output_path="score_comparison_results.xlsx"):
    """Save all results to an Excel file with separate sheets"""
    with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
        for sheet_name, df in results.items():
            df.to_excel(writer, sheet_name=sheet_name, index=False)
    print(f"Results saved to {output_path}")

# Example usage:
if __name__ == "__main__":
    # Run the detailed analysis
    results = compare_score_counts_detailed(
        "result/train_20250603_162707.pkl",  # untuned
        "result/test_ft_20250608_220539.pkl"  # tuned
        # "agents/result/llama3.3:70b_20250528_134432.pickle",  # untuned
    )
    
    # Print comprehensive summary
    print_analysis_summary(results)
    
    # Save to Excel file
    save_results_to_excel(results)
    
    combined_results = results['combined']
    print("\nCombined Analysis:")
    print(combined_results)

