# ---
# jupyter:
#   jupytext:
#     text_representation:
#       extension: .py
#       format_name: percent
#       format_version: '1.3'
#       jupytext_version: 1.17.1
#   kernelspec:
#     display_name: .conda
#     language: python
#     name: python3
# ---

# %%
import os
import sys
os.chdir("../")
sys.path.append(os.getcwd())
import numpy as np
import pandas as pd
from utils import *

# %%
result_path= "agents/result/qwen2:7b_20250512_142040.csv"

df = process_csv_file(result_path)
criteria="diagnosis"
df = extract_min_max(df, criteria)
before_scores = list(df["diagnosis_prefered_score"])[:20]

result_path= "agents/result/qwen2:7b_20250512_164534.csv"
df2 = process_csv_file(result_path)
df2 = extract_min_max(df2, criteria)
after_scores = list(df2["diagnosis_prefered_score"])[:20]


# %%
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

def visualize_ft_scores(before, after):
    # Validate input
    assert len(before) == len(after), "Before and after must have the same number of values"
    
    # Create a DataFrame
    df = pd.DataFrame({'before': before, 'after': after})
    
    # Compute and print summary statistics
    print(f"Mean score before FT: {df['before'].mean():.2f}")
    print(f"Mean score after FT: {df['after'].mean():.2f}")
    print(f"Mean difference (after - before): {(df['after'] - df['before']).mean():.2f}")
    print(f"Number of improvements: {(df['after'] > df['before']).sum()}")
    
    # Create the visualization using JointGrid
    g = sns.JointGrid(x='before', y='after', data=df)
    
    # Scatter plot with transparency
    g.plot_joint(plt.scatter, alpha=0.5)
    
    # Marginal histograms
    g.plot_marginals(sns.histplot, kde=False)
    
    # Add y = x line
    g.ax_joint.plot([0, 4], [0, 4], color='red', linestyle='--')
    
    # Set labels and title
    g.ax_joint.set_xlabel('Score before FT')
    g.ax_joint.set_ylabel('Score after FT')
    g.fig.suptitle('Comparison of scores before and after fine-tuning')
    
    # Set axis limits
    g.ax_joint.set_xlim(0, 4)
    g.ax_joint.set_ylim(0, 4)
    
    # Show the plot
    plt.show()

# Example usage (uncomment and replace with your data)
# before_scores = [1.2, 2.3, ..., 3.4]  # 100 scores before FT
# after_scores = [1.5, 2.5, ..., 3.7]   # 100 scores after FT
visualize_ft_scores(before_scores, after_scores)

# %%
