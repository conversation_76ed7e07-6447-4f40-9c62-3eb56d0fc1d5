#!/bin/bash

# Define the directory to check
TARGET_DIR="./datasets/train_test_split"

# Create a dir to save langflow db.
mkdir -p .langflow

# Check if the directory exists
if [ -d "$TARGET_DIR" ]; then
    echo "Directory $TARGET_DIR already exists. Skipping script execution."
else
    echo "Directory $TARGET_DIR does not exist. Running scripts..."

    # Run the pre-processing script
    echo "Running pre-processing script..."
    python pre-processing.py

    # Run the train-test split script
    echo "Running train-test split script..."
    python train_test_split.py

    echo "All scripts executed successfully."
fi

cp ./sample_env .env

# Check if .langflow/.langflow.db exists
if [ -f ".langflow/.langflow.db" ]; then
    echo ".langflow/.langflow.db found. Commenting out LANGFLOW_LOAD_FLOWS_PATH in .env file..."

    # Removing the LANGFLOW_LOAD_FLOWS_PATH line in the .env file
    sed -i '/LANGFLOW_LOAD_FLOWS_PATH=".\/.ai_workflows"/d' .env

fi

# Run the langflow
echo "Running Langflow..."
python -m langflow run --env-file .env

