#!/bin/bash

# Check if the username and password are provided as command-line arguments
if [ -z "$1" ] || [ -z "$2" ]; then
  echo "Usage: $0 <username> <password>"
  exit 1
fi

# Assign the username and password to variables
USERNAME=$1
PASSWORD=$2

# Step 1: Create the datasets directory
echo "Creating datasets directory..."
mkdir -p datasets
mkdir -p datasets/1.4
mkdir -p datasets/1.20.03

# Step 2: Download the phenotype annotations dataset
echo "Downloading phenotype annotations dataset..."
wget -r -N -c -np --user $USERNAME --password $PASSWORD https://physionet.org/files/phenotype-annotations-mimic/1.20.03/

# Step 3: Copy the phenotype annotations dataset to the datasets directory
echo "Copying phenotype annotations files to datasets directory..."
mv physionet.org/files/phenotype-annotations-mimic/1.20.03/* ./datasets/1.20.03/

# Step 4: Clean up the downloaded phenotype dataset
echo "Cleaning up phenotype dataset..."
rm -rf physionet.org

# Step 5: Download the MIMIC-III dataset
echo "Downloading MIMIC-III dataset..."
wget -N -c --user $USERNAME --password $PASSWORD https://physionet.org/files/mimiciii/1.4/NOTEEVENTS.csv.gz

# Step 6: Unzip the .csv.gz files
echo "Unzipping .csv.gz files..."
gunzip NOTEEVENTS.csv.gz

# Step 7: Copy the relevant files to the datasets directory
echo "Copying MIMIC-III files to datasets directory..."
mv ./NOTEEVENTS.csv ./datasets/1.4/

# Step 8: Clean up the file downloaded
echo "Cleaning up NOTEEVENTS.csv.gz ..."
rm -rf NOTEEVENTS.csv.gz

echo "All datasets have been downloaded and processed."