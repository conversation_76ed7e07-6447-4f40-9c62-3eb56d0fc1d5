# +
import torch
import numpy as np
import pandas as pd
from trl import SFTTrainer, SFTConfig
from datasets import Dataset
from tqdm import tqdm
from unsloth import FastLanguageModel, is_bfloat16_supported

import matplotlib.pyplot as plt
from sklearn.metrics import (
    accuracy_score,
    precision_score,
    recall_score,
    f1_score,
    confusion_matrix,
    ConfusionMatrixDisplay,
    classification_report,
    matthews_corrcoef,
)

import os
import pprint

os.environ["UNSLOTH_RETURN_LOGITS"] = "1"
device = "cuda" if torch.cuda.is_available else "cpu"
print(f"Using device: {device}")
np.random.seed(48)


# +
# print(os.environ.pop("CUDA_VISIBLE_DEVICES", None))

# Check the number of available GPUs
# num_gpus = torch.cuda.device_count()
# print(f"Number of available GPUs: {num_gpus}")

# # List all GPU names
# for i in range(num_gpus):
#     print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
# -

def load_model(base_model):
    """
    Load the base model and tokenizer, and apply LoRA fine-tuning configuration.
    """
    model, tokenizer = FastLanguageModel.from_pretrained(
        model_name=base_model, dtype=None, load_in_4bit=True, device_map="auto"
    )

    # Apply LoRA fine-tuning configuration
    model = FastLanguageModel.get_peft_model(
        model,
        r=128,  # LoRA rank
        target_modules=[
            "q_proj",
            "k_proj",
            "v_proj",
            "o_proj",
            "gate_proj",
            "up_proj",
            "down_proj",
        ],
        lora_alpha=16,
        lora_dropout=0.2,
        bias="none",
        use_rslora=False,
        loftq_config=None,
    )

    # Define the chat template for Llama 3
    llama3_template = """
    {% set loop_messages = messages %}
    {% for message in loop_messages %}
        {% if message['role'] == 'user' %}
            {{ '<|start_header_id|>user<|end_header_id|>\n\n' + message['content'] + '<|eot_id|>' }}
        {% elif message['role'] == 'assistant' %}
            {{ '<|start_header_id|>assistant<|end_header_id|>\n\n' + message['content'] + '<|eot_id|>' }}
        {% endif %}
    {% endfor %}
    {% if add_generation_prompt %}{{ '<|start_header_id|>assistant<|end_header_id|>\n\n' }}{% endif %}
    """
    tokenizer.chat_template = llama3_template

    return model, tokenizer


# +
def data_preprocessing(dir_name):
    """
    Load and preprocess the training and testing data from the specified directory.
    """
    x_train = pd.read_csv(f"{dir_name}/x_train.csv")["note"]
    y_train = pd.read_csv(f"{dir_name}/y_train.csv")["label"]
    x_test = pd.read_csv(f"{dir_name}/x_test.csv")["note"]
    y_test = pd.read_csv(f"{dir_name}/y_test.csv")["label"]
    return x_train, y_train, x_test, y_test


def chat_train(tokenizer, note, label, prompt):
    """
    Format the training data into a chat-like structure for the model.
    """
    user_content = prompt.format(note)
    chat = [
        {"role": "user", "content": user_content},
        {"role": "assistant", "content": f"**The final answer is: {label}**"},
    ]
    return tokenizer.apply_chat_template(chat, tokenize=False)


def train_tokenize(tokenizer, notes, labels, prompt):
    """
    Tokenize the training data and convert it into a Dataset object.
    """
    tokenized_data = [
        chat_train(tokenizer, notes[i], labels[i], prompt) for i in range(len(notes))
    ]
    print(tokenized_data[0])
    return Dataset.from_dict({"text": tokenized_data})


def training(model, tokenizer, data, ft_pth, config):
    """
    Fine-tune the model using the provided training data and configuration.
    """
    trainer = SFTTrainer(
        model=model,
        tokenizer=tokenizer,
        train_dataset=data,
        args=SFTConfig(
            packing=False,
            dataset_num_proc=2,
            dataset_text_field="text",
            learning_rate=config["learning_rate"],
            per_device_train_batch_size=16,
            gradient_accumulation_steps=config["gradient_accumulation_steps"],
            warmup_steps=4,
            num_train_epochs=config["epochs"],
            fp16=not is_bfloat16_supported(),
            bf16=is_bfloat16_supported(),
            logging_steps=1,
            optim="adamw_8bit",
            weight_decay=config["weight_decay"],
            lr_scheduler_type="linear",
            seed=3407,
            output_dir="checkpoints",
            report_to="none",
            logging_dir="logs",
        ),
    )

    # Train the model
    trainer.train()
    trainer.save_model(ft_pth)
    tokenizer.save_pretrained(ft_pth)


def convert_labels(input_str):
    """
    Extract the final label (0 or 1) from the model's output string.
    """
    c = input_str.split(":")[-1].strip()
    for char in c:
        if char in {"0", "1"}:
            return int(char)
    return input_str


def model_inference(model, tokenizer, df_test, df_test_label, prompt, device):
    """
    Perform inference on the test dataset using the fine-tuned model.
    """
    model = FastLanguageModel.for_inference(model)
    outputs = []
    for i in tqdm(range(len(df_test))):
        note = df_test[i]
        user_content = prompt.format(note)
        chat = [{"role": "user", "content": user_content}]
        tokenized_input = tokenizer.apply_chat_template(chat, tokenize=False)
        tokenized_input = tokenizer(
            tokenized_input, padding=True, truncation=True, return_tensors="pt"
        ).to(device)

        input_length = tokenized_input["input_ids"].shape[1]
        output = model.generate(**tokenized_input, max_new_tokens=200)
        generated_tokens = output[0, input_length:]
        output_text = tokenizer.decode(generated_tokens, skip_special_tokens=True)
        c_l = convert_labels(output_text)
        outputs.append(c_l)
    return outputs


def sure_unsure(outputs):
    """
    Separate model outputs into sure (0 or 1) and unsure predictions.
    """
    sure = []
    unsure = {}
    for i, output in enumerate(outputs):
        try:
            sure.append(int(output))
        except ValueError:
            sure.append(0)
            unsure[i] = output
    return sure, unsure


def evaluate(y_true, y_pred):
    """
    Evaluate the model's performance using various metrics.
    """
    accuracy = accuracy_score(y_true, y_pred)
    precision = precision_score(y_true, y_pred)
    recall = recall_score(y_true, y_pred)
    f1 = f1_score(y_true, y_pred)
    conf_matrix = confusion_matrix(y_true, y_pred)
    report = classification_report(y_true, y_pred)
    mcc = matthews_corrcoef(y_true, y_pred)

    # Plot confusion matrix
    cm_display = ConfusionMatrixDisplay(
        confusion_matrix=conf_matrix, display_labels=[0, 1]
    )
    cm_display.plot(cmap="Blues")
    plt.title(f"Confusion Matrix\nAccuracy: {accuracy:0.2f}")
    plt.savefig("confusion_matrix.png", bbox_inches="tight", pad_inches=0.5)
    plt.show()

    return {
        "accuracy": accuracy,
        "precision": precision,
        "recall": recall,
        "F1_score": f1,
        "confusion_matrix": conf_matrix,
        "classification_report": report,
        "mcc": mcc,
    }


# +
base_model = "unsloth/Meta-Llama-3.1-8B-Instruct-bnb-4bit"

# Define the prompt for the task
prompt = """Your task is to determine whether the patient described in the note has depression or not.
**Output Instructions:** Provide your final answer (only the number "1" or "0") in this format **The final answer is: {{ 0 or 1 }}**.

---
**Patient Note Summary:**
{}
Output the result (1 for depression present, or 0 otherwise) in this format **The final answer is: **.
"""

# Define the training configuration
config = {
    "learning_rate": 4.6984521886770686e-05,
    "gradient_accumulation_steps": 4,
    "epochs": 2,
    "weight_decay": 0.023938599209150646,
}


# +
def run():
    """
    Main function to load data, fine-tune the model, and evaluate its performance.
    """
    model, tokenizer = load_model(base_model)
    df_train, df_train_label, df_test, df_test_label = data_preprocessing(
        "./datasets/train_test_split"
    )

    # Define the path to save the fine-tuned model
    ft_pth = f"./finetuned_model/{base_model}"
    if not os.path.exists(ft_pth):
        os.makedirs(ft_pth)

    # Tokenize the training data
    data = train_tokenize(tokenizer, df_train, df_train_label, prompt)

    # Fine-tune the model
    training(model, tokenizer, data, ft_pth, config)

    # Perform inference on the test data
    outputs = model_inference(model, tokenizer, df_test, df_test_label, prompt, device)

    # Separate sure and unsure predictions
    y_pred, unsure = sure_unsure(outputs)
    print(f"Model unsure values vs total testing: {len(unsure)}/{len(y_pred)}")

    # Evaluate the model's performance
    metrics = evaluate(df_test_label, y_pred)
    pprint.pprint(metrics, indent=2)


# Run the main function
run()
