This repository contains scripts to download and preprocess datasets, and run a Langflow application. Below are the instructions to set up the environment and run the scripts.

## Prerequisites
- **Conda**: Ensure you have Conda installed on your system. You can download it from [here](https://docs.conda.io/en/latest/miniconda.html).

## Setting Up the Environment
### Create a Conda Environment:
Open a terminal and run the following commands to create a new Conda environment with the required dependencies:

```bash
conda create -n .conda python=3.11 matplotlib scikit-learn langflow==1.1.3
```

Due to a dependency conflict between Lang<PERSON> and Unsloth, install Unsloth separately after activating the environment:

```bash
conda activate .conda
pip install unsloth==2025.3.10
```

## Downloading and Preprocessing Datasets
### Download Datasets:
Run the `script/download_datasets.sh` script to download the required datasets. You need to provide your PhysioNet username and password as command-line arguments.

```bash
bash script/download_datasets.sh <username> <password>
```

Replace `<username>` and `<password>` with your actual PhysioNet credentials.

### Preprocess Datasets:
After downloading the datasets, run the `script/run.sh` script to preprocess the data and split it into training and testing sets.

```bash
bash script/run.sh
```

## Running Langflow
The `run.sh` script also handles running the Langflow application. If the `.langflow/.langflow.db` file exists, it will comment out the `LANGFLOW_LOAD_FLOWS_PATH` line in the `.env` file before running Langflow.

## Requirements
The following packages are required to run the scripts:

- matplotlib
- scikit-learn
- langflow==1.1.3
- unsloth==2025.3.10
- torch==2.6.0
- python=3.11

These dependencies are automatically installed when you create the Conda environment as described above, with Unsloth installed separately to avoid conflicts.

## Notes
- Ensure you have the necessary permissions to access the PhysioNet datasets.

## Troubleshooting
- If you encounter any issues with the scripts, ensure that all dependencies are correctly installed and that you have the necessary permissions to access the datasets.

