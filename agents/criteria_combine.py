#!/usr/bin/env python3
# medical_text_analysis_pipeline.py
import os
import sys
import csv
import json
import argparse
import signal
from datetime import datetime

sys.path.append(os.path.abspath(".."))
# Set CUDA_VISIBLE_DEVICES to use GPU 1
os.environ["CUDA_VISIBLE_DEVICES"] = "0,1"
os.environ["OLLAMA_USE_GPU"] = "true"

import ollama
import pandas as pd
from tqdm import tqdm
from transformers import AutoTokenizer

from utils.criteria import CriteriaType
from utils.prompt_schema import *
from utils.prompt import *
from utils.model_response import *

# Documentation for chat template(LLAMA and huggingface): 
#     - https://www.llama.com/docs/model-cards-and-prompt-formats/meta-llama-3/
#     - https://huggingface.co/docs/transformers/main/en/chat_templating

# Global flag for graceful shutdown
shutdown_requested = False

def signal_handler(signum, frame):
    """Handle first Ctrl+C gracefully by setting shutdown flag"""
    global shutdown_requested
    print("\n" + "="*50)
    print("INTERRUPT RECEIVED - Attempting graceful shutdown...")
    print("Press Ctrl+C again to force quit")
    print("="*50)
    shutdown_requested = True
    
def force_signal_handler(signum, frame):
    """Handle second Ctrl+C by forcing immediate exit"""
    print("\nForce quit requested. Exiting immediately...")
    os._exit(1)

# Set up signal handlers
signal.signal(signal.SIGINT, signal_handler)

def create_model_combinations(model_diagnosis="llama3.3:70b", model_treatment="llama3.3:70b", model_self_harm="llama3.3:70b"):
    """Create list of model-criteria combinations for processing
    
    Args:
        model_diagnosis: Model name for diagnosis criteria
        model_treatment: Model name for treatment criteria  
        model_self_harm: Model name for self-harm criteria
        
    Returns:
        List of dictionaries with criteria and model configurations
    """
    return [
        {
            "criteria": CriteriaType.DIAGNOSIS,
            "model": model_diagnosis,
        },
        {
            "criteria": CriteriaType.TREATMENT,
            "model": model_treatment,
        },
        {
            "criteria": CriteriaType.SELF_HARM,
            "model": model_self_harm
        },
    ]

def prompt_creation(tokenizer, dataset_dir, split, nrows=None, text_column="note"):
    """Load dataset and generate prompts for all criteria types
    
    Args:
        tokenizer: HuggingFace tokenizer for prompt formatting
        dataset_dir: Directory containing the dataset files
        split: Dataset split ('train', 'test', 'val')
        nrows: Number of rows to load (None for all)
        text_column: Column name containing the text data
        
    Returns:
        DataFrame with original data plus generated prompts for each criteria
    """
    # Load dataset and labels
    df = pd.read_csv(f"{dataset_dir}/x_{split}.csv", nrows=nrows)
    label = pd.read_csv(f"{dataset_dir}/y_{split}.csv", nrows=nrows)
    df["label"] = label["label"]
    
    # Configure custom chat template for Llama
    custom_template = """{% set loop_messages = messages %}{% for message in loop_messages %}{% set content = '<|start_header_id|>' + message['role'] + '<|end_header_id|>' + message['content'] + '<|eot_id|>' %}{% if loop.index0 == 0 %}{% set content = '<|begin_of_text|>' + content %}{% endif %}{{ content }}{% endfor %}{% if add_generation_prompt %}{{ '<|start_header_id|>assistant<|end_header_id|>' }}{% endif %}"""
    tokenizer.chat_template = custom_template
    
    # Generate prompts for each criteria type
    for criteria in CriteriaType:
        prompts = []
        system_prompt, json_prompt, user_prompt = get_prompts(criteria)
        conversation_template = [
            {"role": "system", "content": system_prompt}, 
            {"role": "JSON", "content": json_prompt}, 
            {"role": "user"}
        ]
        
        # Create prompts for each text sample
        for text_content in df[text_column]:
            conversation_template[2]["content"] = user_prompt.render(chart=text_content)
            formatted_prompt = tokenizer.apply_chat_template(
                conversation_template, 
                add_generation_prompt=True, 
                tokenize=False
            )
            prompts.append(formatted_prompt)
            
        df[f"{criteria.value}_prompt"] = prompts
        
    return df

def pipeline(model_combinations, output_path, dataframe, num_responses=8):
    """Execute the complete inference pipeline for all model-criteria combinations
    
    Args:
        model_combinations: List of model-criteria configuration dictionaries
        output_path: Path where results will be saved
        dataframe: Input DataFrame with prompts
        num_responses: Number of responses to generate per sample
        
    Returns:
        DataFrame with inference results added
    """
    global shutdown_requested
    results_df = dataframe.copy()
    ollama_client = ollama.Client(host="http://127.0.0.1:11434")
    
    # Process each model-criteria combination
    for combination in model_combinations:
        if shutdown_requested:
            print("Shutdown requested during processing. Saving current progress...")
            save_results(results_df, output_path)
            print(f"Partial results saved to {output_path}")
            sys.exit(0)
        criteria_name = combination['criteria'].value
        model_name = combination['model']
        print(f"Running inference for criteria: {criteria_name} using model: {model_name}")
        
        try:
            # Run inference for current combination
            inference_results = process_pipeline(
                results_df, 
                ollama_client, 
                combination["model"], 
                combination['criteria'], 
                n=num_responses
            )
            results_df[criteria_name] = inference_results
            
            # Save intermediate results
            save_results(results_df, output_path)
            
        except KeyboardInterrupt:
            print("\nProcessing interrupted. Saving current progress...")
            save_results(results_df, output_path)
            print(f"Partial results saved to {output_path}")
            raise

    print(f"All combinations processed and results saved in {output_path}")
    return results_df

def save_results(dataframe, output_path):
    """Save results in both pickle and CSV formats
    
    Args:
        dataframe: DataFrame to save
        output_path: Base path for saving (should end with .pickle)
    """
    dataframe.to_pickle(output_path)
    csv_path = output_path.replace('.pkl', '.csv')
    dataframe.to_csv(csv_path, quoting=csv.QUOTE_ALL, encoding='utf-8', index=False)

def parse_command_line_arguments():
    """Parse and validate command line arguments
    
    Returns:
        Parsed arguments namespace
    """
    parser = argparse.ArgumentParser(
        description="Run medical text analysis pipeline with Ollama inference",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python medical_text_analysis_pipeline.py                                    # Use all defaults
  python medical_text_analysis_pipeline.py --split test --nrows 100          # Override specific options
  python medical_text_analysis_pipeline.py --model_diagnosis "llama3.1:8b"   # Use different model
  python medical_text_analysis_pipeline.py --split train                      # Process training set

Configuration Options:
  --split: Dataset split (train or test)
  --nrows: Number of rows to process (None for all)
  --num_responses: Number of responses per sample
  --model_diagnosis: Model for diagnosis criteria
  --model_treatment: Model for treatment criteria  
  --model_self_harm: Model for self-harm criteria

Defaults:
  split=train, nrows=None (all rows), num_responses=8
  All models default to llama3.3:70b
        """
    )
    parser.add_argument("--split", type=str, default="train", 
                       help="Dataset split to use (default: train)")
    parser.add_argument("--nrows", type=int, default=None,
                       help="Number of rows to process (default: None - all rows)")
    parser.add_argument("--num_responses", type=int, default=8,
                       help="Number of responses to generate per sample (default: 8)")
    parser.add_argument("--dataset_dir", type=str, default="./datasets/train_test_split",
                       help="Path to dataset directory (default: ./datasets/train_test_split)")
    parser.add_argument("--output_dir", type=str, default="./result",
                       help="Output directory for results (default: ./result)")
    parser.add_argument("--model_diagnosis", type=str, default="llama3.3:70b",
                       help="Model to use for diagnosis criteria (default: llama3.3:70b)")
    parser.add_argument("--model_treatment", type=str, default="llama3.3:70b",
                       help="Model to use for treatment criteria (default: llama3.3:70b)")
    parser.add_argument("--model_self_harm", type=str, default="llama3.3:70b",
                       help="Model to use for self harm criteria (default: llama3.3:70b)")
    parser.add_argument("--ft", type=bool, default=False, help="finetune model or not(default: False)" )
    
    return parser.parse_args()

def initialize_pipeline_environment():
    """Initialize the pipeline environment and validate dependencies
    
    Returns:
        Tuple of (tokenizer, success_flag)
    """
    try:
        print("Initializing tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained("meta-llama/Llama-3.3-70B-Instruct")
        return tokenizer, True
    except Exception as e:
        print(f"Failed to initialize tokenizer: {e}")
        return None, False

def main():
    """Main pipeline execution function"""
    global shutdown_requested
    
    try:
        # Parse command line arguments
        args = parse_command_line_arguments()
        
        # Display configuration
        print("="*60)
        print("MEDICAL TEXT ANALYSIS PIPELINE WITH OLLAMA")
        print("="*60)
        print(f"Configuration:")
        print(f"  Split: {args.split}")
        print(f"  Rows: {'All' if args.nrows is None else args.nrows}")
        print(f"  Responses per sample: {args.num_responses}")
        print(f"  Dataset directory: {args.dataset_dir}")
        print(f"  Output directory: {args.output_dir}")
        print(f"  Models:")
        print(f"    Diagnosis: {args.model_diagnosis}")
        print(f"    Treatment: {args.model_treatment}")
        print(f"    Self-harm: {args.model_self_harm}")
        print("="*60)
        print("Press Ctrl+C to stop the pipeline gracefully")
        print("="*60)
        
        # Check for early shutdown
        if shutdown_requested:
            print("Shutdown requested. Exiting...")
            sys.exit(0)
        
        # Initialize environment
        tokenizer, init_success = initialize_pipeline_environment()
        if not init_success:
            raise RuntimeError("Failed to initialize pipeline environment")
        
        if shutdown_requested:
            print("Shutdown requested. Exiting...")
            sys.exit(0)
        
        # Prepare dataset and prompts
        print(f"Loading dataset and generating prompts...")
        dataset_with_prompts = prompt_creation(
            tokenizer, 
            dataset_dir=args.dataset_dir, 
            split=args.split, 
            nrows=args.nrows
        )
        print(f"Loaded {len(dataset_with_prompts)} rows")
        print("Sample prompt preview:")
        print("-" * 40)
        sample_prompt = dataset_with_prompts["diagnosis_prompt"][0]
        preview = sample_prompt[:3500] + "..." if len(sample_prompt) > 3500 else sample_prompt
        print(preview)
        print("-" * 40)
        print()
        
        if shutdown_requested:
            print("Shutdown requested. Exiting...")
            sys.exit(0)
        
        # Prepare output path
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        os.makedirs(args.output_dir, exist_ok=True)
        if args.ft:
            output_path = f"{args.output_dir}/{args.split}_ft_{timestamp}.pkl"
        else:
            output_path = f"{args.output_dir}/{args.split}_{timestamp}.pkl"
        print(f"Results will be saved to: {output_path}")
        print()
        
        # Create model combinations
        model_combinations = create_model_combinations(
            model_diagnosis=args.model_diagnosis,
            model_treatment=args.model_treatment,
            model_self_harm=args.model_self_harm
        )
        
        # Change signal handler to force quit on second Ctrl+C
        signal.signal(signal.SIGINT, force_signal_handler)
        
        # Execute the inference pipeline
        print(f"Starting inference pipeline execution...")
        print("Note: Press Ctrl+C again to force quit")
        save_results(dataset_with_prompts, output_path)
        final_results = pipeline(
            model_combinations, 
            output_path, 
            dataset_with_prompts, 
            num_responses=args.num_responses
        )
        
        print("="*60)
        print("PIPELINE COMPLETED SUCCESSFULLY!")
        print(f"Results saved to: {output_path}")
        print(f"CSV version: {output_path.replace('.pkl', '.csv')}")
        print("="*60)
        
    except KeyboardInterrupt:
        print("\n" + "="*50)
        print("PIPELINE INTERRUPTED BY USER")
        print("="*50)
        sys.exit(0)
    except Exception as e:
        print(f"\nError occurred: {str(e)}")
        print("\nFor help, run: python medical_text_analysis_pipeline.py --help")
        print("\nRequired dependencies:")
        print("- Dataset directory with x_split.csv and y_split.csv files")
        print("- Utils modules: criteria, prompt_schema, prompt, model_response")
        print("- Ollama server running on http://127.0.0.1:11434")
        print("- HuggingFace transformers library")
        sys.exit(1)

if __name__ == "__main__":
    main()