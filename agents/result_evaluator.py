#!/usr/bin/env python3
"""
Model evaluation script for processing pickle results and computing metrics.
"""

import argparse
import os
import sys
from pathlib import Path
import pandas as pd
from utils import CriteriaType, count_yes_occurrences, ModelEvaluator, extract_min_max


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Evaluate model results from pickle file"
    )
    parser.add_argument(
        "--pickle_path",
        type=str,
        required=True,
        help="Path to the pickle file containing model results"
    )
    return parser.parse_args()


def load_and_process_data(pickle_path):
    """Load pickle file and process the data."""
    if not os.path.exists(pickle_path):
        raise FileNotFoundError(f"Pickle file not found: {pickle_path}")
    
    print(f"Loading data from: {pickle_path}")
    df = pd.read_pickle(pickle_path)
    
    # Process criteria columns - extract first element from lists
    for criteria in CriteriaType:
        c = criteria.value 
        if c in df.columns: 
            if f"{c}_chosen" not in df.columns:
                df2 = extract_min_max(df2, c)
                df2.to_pickle("score_test.pkl")
    for criteria in CriteriaType:
        c = criteria.value 
        df2[c] = df2[f"{c}_chosen"]
        
    return df2


def print_sample_diagnosis(df):
    """Print sample diagnosis for inspection."""
    if "diagnosis" in df.columns and len(df) > 1:
        try:
            sample_diagnosis = df["diagnosis"].iloc[1]
            if hasattr(sample_diagnosis, 'model_dump_json'):
                print("Sample diagnosis:")
                print(sample_diagnosis.model_dump_json())
        except (IndexError, AttributeError) as e:
            print(f"Could not display sample diagnosis: {e}")


def evaluate_model(df, pickle_path):
    """Evaluate model performance and print metrics."""
    # Process the dataframe for evaluation
    df = count_yes_occurrences(df, count=1)
    
    # Extract true labels and predictions
    if "label" not in df.columns or "prediction" not in df.columns:
        raise ValueError("DataFrame must contain 'label' and 'prediction' columns")
    
    y_true = df["label"]
    y_pred = df["prediction"]
    
    # Use the pickle file path as the title
    title = Path(pickle_path).stem
    
    # Evaluate and print metrics
    evaluator = ModelEvaluator(save_dir="report")
    metrics = evaluator.evaluate(y_true, y_pred, title=title)
    evaluator.print_metrics(metrics)
    
    return metrics


def main():
    """Main execution function."""
    # try:
    args = parse_arguments()
    
    # Load and process data
    df = load_and_process_data(args.pickle_path)
    print(f"Loaded dataframe with shape: {df.shape}")
    
    # Print sample diagnosis
    print_sample_diagnosis(df)
    
    # Evaluate model
    metrics = evaluate_model(df, args.pickle_path)
    
    print("\nEvaluation completed successfully!")
        
    # except Exception as e:
    #     print(f"Error: {e}", file=sys.stderr)
    #     sys.exit(1)


if __name__ == "__main__":
    main()