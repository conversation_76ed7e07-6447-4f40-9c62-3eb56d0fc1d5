import json
from tqdm import tqdm
from .prompt_schema import *


def process_pipeline(df, ollama_client, model, criteria, n=8):
    """
    Process a batch of data using the model, generating 8 different responses for each note
    Args:
        df: DataFrame containing prompts
    Returns:
        List of lists, with each inner list containing 8 model outputs in JSON format for a single note
    """
    model_output = []
    for prompt in tqdm(df[f"{criteria.value}_prompt"]):

        note_responses = []
        for _ in range(n):
            try:
                json_generator = ollama_client.generate(
                    model=model,
                    prompt=prompt,
                    format=getSchema(criteria).model_json_schema(),
                    options={
                        "temperature": 0.99,
                        "num_ctx": 20000,
                        "num_predict": 1500,
                    },
                    stream=False,
                )
                # response = json.dumps(json.loads(json_generator['message']['content']))
                response = getSchema(criteria).model_validate_json(
                    json_generator.response
                )
                note_responses.append(response)
            except:
                print("get into error")
                note_responses.append(create_empty_schema(criteria))

        # Add the list of 8 responses for this note to the model_output
        model_output.append(note_responses)

    return model_output
