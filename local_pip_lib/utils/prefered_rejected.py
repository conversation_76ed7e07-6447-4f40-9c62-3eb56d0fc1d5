import os
import sys
import json
import pandas as pd
from datasets import Dataset
from .prompt_schema import *


def merge_diagnosis_data(
    df1, df2, columns_to_merge=["diagnosis", "treatment", "self_harm"]
):
    """
    Merge two processed dataframes, combining their chosen diagnoses and validating against notes.

    Args:
        df1 (pandas.DataFrame): First dataframe with processed diagnosis data
        df2 (pandas.DataFrame): Second dataframe with processed diagnosis data

    Returns:
        pandas.DataFrame: Merged dataframe with combined chosen diagnoses
    """
    df_merged = pd.DataFrame({"chart": df1["note"]})  # The original note column

    for col in columns_to_merge:
        chosen_col = f"{col}_chosen"
        rejected_col = f"{col}_rejected"

        # Add chosen and rejected columns to merged dataframe
        df_merged[chosen_col] = df1[chosen_col]
        df_merged[rejected_col] = df2[chosen_col]

        # Process each row to combine chosen and rejected data
        for index, row in df_merged.iterrows():
            chosen = row[chosen_col]
            rejected = row[rejected_col]

            # Handle cases where one has a value and the other doesn't
            if pd.isna(chosen) and not pd.isna(rejected):
                df_merged.at[index, chosen_col] = rejected
            elif not pd.isna(chosen) and not pd.isna(rejected):
                # Combine COT information from both
                chosen.add_cot(rejected)
                df_merged.at[index, chosen_col] = chosen

            # Validate and update evidence if chosen exists
            if not pd.isna(chosen):
                chosen.update_evidence(row["chart"], rejected)

    return df_merged


def load_preference_data(
    data_source,
    tokenizer,
    chart,
    chosen_column,
    rejected_column,
    system_prompt,
    json_prompt,
    user_prompt,
):
    """
    Load preference data from a DataFrame or pickle file and convert it to a Dataset for DPO training.
    Args:
        data_source (Union[str, pd.DataFrame]): Path to the pickle file or DataFrame object
        chart (str): Name of the column containing chart information to be inserted in user_prompt
        chosen_column (str): Name of the column containing chosen responses
        rejected_column (str): Name of the column containing less chosen responses
        system_prompt (str): System prompt to use in the conversation
        user_prompt (str): User prompt template with '{chart}' placeholder to be replaced
        tokennizer (str): Name of the tokenizer to use for tokenization

    Returns:
        Dataset: A Dataset object compatible with DPOTrainer
    """
    # Read the pickle file
    if isinstance(data_source, str):
        df = pd.read_pickle(data_source)
    elif isinstance(data_source, pd.DataFrame):
        df = data_source
    else:
        raise TypeError(
            "data_source must be either a path to a pickle file (str) or a pandas DataFrame"
        )

    # Check if all required columns exist
    required_columns = [chart, chosen_column, rejected_column]
    missing_columns = [col for col in required_columns if col not in df.columns]

    if missing_columns:
        raise ValueError(f"Missing columns in pickle file: {missing_columns}")
    # Create formatted conversations and tokenize them
    prompts = []
    for chart in df[chart]:
        # Replace {chart} placeholder in user_prompt with actual chart content
        formatted_user_prompt = user_prompt.render(chart=chart)
        conversation = [
            {"role": "system", "content": system_prompt},
            {"role": "JSON", "content": json_prompt},
            {"role": "user", "content": formatted_user_prompt},
        ]

        # Apply chat template to format the conversation according to the model's expected format
        formatted_prompt = tokenizer.apply_chat_template(
            conversation, tokenize=False, add_generation_prompt=True
        )
        prompts.append(formatted_prompt)

    # Create preference data dictionary
    preference_data = {
        "prompt": prompts,
        "chosen": df[chosen_column].apply(lambda x: x.model_dump_json()).tolist(),
        "rejected": df[rejected_column].apply(lambda x: x.model_dump_json()).tolist(),
    }

    # Create dataset
    dataset = Dataset.from_dict(preference_data)

    return dataset


def extract_min_max(df, criteria):
    cols = {
        f"{criteria}_chosen": [],
        f"{criteria}_chosen_score": [],
        f"{criteria}_rejected": [],
        f"{criteria}_rejected_score": [],
        f"{criteria}_score": [],
    }
    # Process each row in a single pass
    for _, row in df.iterrows():
        # Calculate all scores at once
        model_response = row[criteria]
        scores = []
        for obj in model_response:
            scores.append(obj.score(row["note"]))
        # Find min and max indices
        max_idx = scores.index(max(scores))
        min_idx = scores.index(min(scores))

        # Append results to respective columns
        cols[f"{criteria}_chosen"].append(model_response[max_idx])
        cols[f"{criteria}_chosen_score"].append(scores[max_idx])
        cols[f"{criteria}_rejected"].append(model_response[min_idx])
        cols[f"{criteria}_rejected_score"].append(scores[min_idx])
        cols[f"{criteria}_score"].append(scores)

    # Assign all columns at once
    return df.assign(**cols)
