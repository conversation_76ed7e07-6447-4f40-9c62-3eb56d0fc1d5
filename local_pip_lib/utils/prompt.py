# This code defines prompts and JSON templates for analyzing patient charts based on three criteria:
# 1. Diagnosis of MDD (excluding bipolar depression)
# 2. Treatment for MDD
# 3. Evidence of self-harm
# Each criterion has a system prompt with specific instructions and a corresponding JSON output template.
# The prompts are designed to be substituted into a chat template for processing by a language model.
# The JSON templates ensure structured output with a schema.
# The user prompt uses Jinja2 templating to insert patient chart dynamically.
import json
from jinja2 import Template
from .criteria import CriteriaType


strict_instruction = """Strictly follow the JSON format schema and specific instructions within each set of double angle brackets <<instructions go here>> to determine the appropriate value for each field."""

# System prompt for diagnosing MDD based on ICD-9/ICD-10 codes or explicit mentions
icd9_mdd = r"296\.[23][0-9]|298\.0|311"
icd9_mdd = r"296\.2|296\.20|296\.21|296\.22|296\.23|296\.24|296\.25|296\.26|296\.3|296\.30|296\.31|296\.32|296\.33|296\.34|296\.35|296\.36|311|298\.0"
icd10_mdd = r"F32(\.\d+)?|F33(\.\d+)?"
icd9_bipolar = r"296\.7|296\.[014568].*"
icd10_bipolar = r"F30(\.\d+)?|F31(\.\d+)?"
icd9_schizophrenia = r"295\.[012345689].*"
icd10_schizophrenia = r"F20(\.\d+)?"
icd9_schizoaffective = r"295\.7.*"
icd10_schizoaffective = r"F25(\.\d+)?"

system_diagnosis = f"""You are an expert in medical chart review. Your task is to carefully analyze a patient chart and determine whether the patient has a diagnosis of major depressive disorder, MDD, (excluding bipolar disorder, schizophrenia, or schizoaffective disorder).
This can be represented in the text either with words that closely map to ICD-9 or ICD-10 MDD or an explicit mention in context that the patient has a specific code. NEVER say a code is present unless it is EXPLICITLY STATED in the chart as a code.
1. Check for the presence of any of these MDD ICD-9 codes (represented with regular expressions): {icd9_mdd}
2. Check for the presence of any of these MDD ICD-10 codes (represented with regular expressions): {icd10_mdd}
3. Check for the presence of bipolar disorder {icd9_bipolar}, {icd10_bipolar}, schizophrenia {icd9_schizophrenia}, {icd10_schizophrenia}, or schizoaffective ({icd9_schizoaffective}, {icd10_schizoaffective}) disorder and/or codes, which if seen would RULE OUT MDD, and should be provided as relevant evidence against MDD.
Please generate output strictly in JSON format according to the provided template. Ensure the output consists solely of valid JSON without any explanatory text or comments outside the JSON structure. You will identify up to three pieces of evidence in a list_of_evidence array, where each piece of evidence must be a VERBATIM excerpt from the chart placed in the "evidence" field, followed by icd_code if present and your reasoning in the "reasoning" field as to why you think the evidence points toward or against a diagnosis of MDD. Every item in the list_of_evidence must have the reasoning field non-blank.
After finishing generating the list_of_evidence array, synthesize all of the evidence to populate the conclusion elements as directed following the instructions in double angle brackets.
Only conclude "has_mdd": "yes" if confidence_mdd >= 0.5 else "no". {strict_instruction} Here is the JSON format:"""
system_diagnosis = system_diagnosis.replace("\n", " ")

# Jinja2 template for user prompt to insert patient chart
user_prompt = """{{ chart }}"""
user_prompt = Template(user_prompt)

# JSON template for MDD diagnosis
json_diagnosis = {
    "list_of_evidence": [
        {
            "evidence": "<<VERBATIM excerpt from the patient chart which reflects if the patient has a history of MDD. This needs to be no more than 350 characters. Include only the exact words from the chart, nothing else. This field may only be blank if there is no evidence for or against MDD (and thus no verbatim text to include), and the associated reasoning field below must also convey that.>>",
            "icd_code": "<<VERBATIM ICD-9 or ICD-10 code found in the chart for either MDD, one of the rule-out codes, or empty if this piece of evidence does not relate to a code explicitly listed in the chart.>>",
            "reasoning": "<<Short explanation based on the relevance of the evidence and/or the icd_code to the presence or absence of MDD. This needs to be no more than 350 characters in length, and must never be blank.>>",
        }
    ],
    "conclusion": {
        "evidence_synthesis": "<<Synthesis of the evidence, icd_code, and reasoning fields from the list_of_evidence for or against MDD.>>",
        "confidence_mdd": "<<What is the confidence of MDD? Give a single number ranging 0.0 to 1.0 where 0.0 is you are certain there is not MDD and 1.0 is you are certain there is MDD.>>",
        "has_mdd": "<<yes if confidence_mdd >= 0.5 else no.>>",
    },
}

json_diagnosis = json.dumps(json_diagnosis, separators=(",", ":"))

# System prompt for identifying MDD treatment
system_treatment = f"""You are an expert in medical chart review. Your task is to carefully analyze a patient chart and determine whether the patient has received treatment for major depressive disorder (MDD). Treatment may include:
- Medications explicitly mentioned in the text, including the following classes and specific drugs (listed), including brand names you are aware of for these drugs (not listed):
  1. Selective Serotonin Reuptake Inhibitors (SSRIs): citalopram, escitalopram, fluoxetine, fluvoxamine, paroxetine, sertraline, vilazodone, vortioxetine
  2. Serotonin-Norepinephrine Reuptake Inhibitors (SNRIs): desvenlafaxine, duloxetine, levomilnacipran, milnacipran, venlafaxine
  3. Tricyclic antidepressants: amitriptyline, amoxapine, clomipramine, desipramine, doxepin, imipramine, nortriptyline, protriptyline, trimipramine
  4. Tetracyclic antidepressants: maprotiline
  5. Monoamine Oxidase Inhibitors (MAOIs): isocarboxazid, phenelzine, selegiline, tranylcypromine
  6. Atypical Antidepressants: bupropion, mirtazapine
  7. Off-label: a drug used off-label for MDD where the chart explicitly mentions the drug being used for that purpose, e.g., "ketamine was administered for MDD"
- Psychotherapy specifically to treat MDD
- Electroconvulsive therapy (ECT) specifically for treatment-resistant MDD
- Transcranial Magnetic Stimulation (TMS) therapy specifically to treat MDD
Please generate output strictly in JSON format according to the provided template. Ensure the output consists solely of valid JSON without any explanatory text or comments outside the JSON structure. 
You will identify up to three pieces of evidence in a list_of_evidence array, where each piece of evidence must be a VERBATIM excerpt from the chart placed in the "evidence" field, followed by your reasoning in the "reasoning" field as to why you think the evidence points toward treatment of MDD. Every item in the list_of_evidence must have the reasoning field non-blank.
After finishing generating the list_of_evidence array, synthesize all of the evidence to populate the conclusion elements as directed following the instructions in double angle brackets.
Only conclude "has_treatment": "yes" if confidence_treatment >= 0.5 else "no". {strict_instruction} Here is the JSON format:"""
system_treatment = system_treatment.replace("\n", " ")

# JSON template for MDD treatment
json_treatment = {
    "list_of_evidence": [
        {
            "evidence": "<<VERBATIM excerpt from the patient chart which reflects if the patient has received the treatment of MDD. This needs to be no more than 350 characters. Include only the exact words from the chart, nothing else. This field may only be blank if there is no evidence for or against treatment for MDD (and thus no verbatim text to include), and the associated reasoning field below must also convey that.>>",
            "reasoning": "<<Short explanation based on relevance of the evidence to the treatment of MDD. This needs to be no more than 350 characters in length, and must never be blank.>>",
        }
    ],
    "conclusion": {
        "evidence_synthesis": "<<Synthesis of the evidence and reasoning fields from the list_of_evidence for or against treatment of MDD.>>",
        "type_treatment": "<<SSRI, SNRI, tricyclic, tetracyclic, MAOI, atypical, off-label, psychotherapy, ECT, TMS, or none based on evidence.>>",
        "confidence_treatment": "<<What is the confidence for treatment of MDD? Give a single number ranging 0.0 to 1.0 where 0.0 is you are certain there is no treatment of MDD and 1.0 is you are certain there is a treatment of MDD.>>",
        "has_treatment": "<<yes if confidence_treatment >= 0.5 else no.>>",
    },
}

# Convert the json_treatment to a compact JSON
json_treatment = json.dumps(json_treatment, separators=(",", ":"))

# X7{1-9}; X8{0-3}; T14.91; T36.{0-8}X2; T36.92; T37.{0-8}X2; T37.92; T38.{0-7}X2; T38.8{0,1,9}2; T38.9{0,9}2; T39.0{1,9}2; T39.{1-2}X2; T39.3{1,9}2; T39.{4,8}X2; T39.92; T40.{0-5}X2; T40.6{0,9}2; T40.{7,8}X2; T40.9{0,9}2; T41.{0,1}X2; T41.2{0,9}2; T41.{3,5}X2; T41.42; T42.{0-6}X2; T42.72; T42.8X2; T43.{0,1,3,4,6,8}X2; T43.{2,5,6}{0,9}2; T43.92; T43.{0,2,6}{1,2}2; T43.6{3,4}2; T44.{0-8}X2; T44.9{0,9}2; T45.{0-4}X2; T45.5{1,2}2; T45.6{0,1,2,9}2; T45.{7,8}X2; T45.92; T46.{0-8}X2; T46.9{0,9}2; T47.{0-8}X2; T47.92; T48.{0,1}X2; T48.2{0,9}2; T48.{3-6}X2; T48.9{0,9}2; T49.{0-8}X2; T49.92; T50.{0-8}X2; T50.A{1,2,9}2; T50.B{1,9}2; T50.Z{1,9}2; T50.9{0,1,9}2; T51.{0-3,8}X2; T51.92; T52.{0-4,8}X2; T52.92; T53.{0-7}X2; T53.92; T54.{0-3}X2; T54.92; T55.{0,1}X2; T56.{0-7}X2; T56.8{1,9,X}2; T56.92; T57.{0-3,8}X2; T57.92; T58.{0,1}2; T58.{2,8}X2; T58.892; T58.92; T59.{0-7}X2; T59.812; T59.892; T59.92; T60.{0-4,8}X2; T60.92; T61.{0,1}2; T61.7{7,8}2; T61.8X2; T61.92; T62.{0-2,8}X2; T62.92; T63.{0,3}02; T63.{0,1,3-8}12; T63.92; T63.{0,1,3,4,6,8}22; T63.{0,3,4,6,8}32; T63.{0,4}42; T63.452; T63.{0,4}62; T63.072; T63.{0,4}82; T63.{0,1,3,5-8}92; T63.2X2; T64.{0,8}2; T65.2{1,2,9}2; T65.{0,1,3-6}X2; T65.8{1-3,9}2; T65.92; T71.1{1-3,5,6,9}2; T71.2{2,3}2;

icd9_self_harm = r"E950|E950\.[0-9]"
icd10_self_harm = (
    r"X7[1-9]|"
    r"X8[0-3]|"
    r"T14\.91|"
    r"T36\.[0-8]X2|"
    r"T36\.92|"
    r"T37\.[0-8]X2|"
    r"T37\.92|"
    r"T38\.[0-7]X2|"
    r"T38\.8[019]2|"
    r"T38\.9[09]2|"
    r"T39\.0[19]2|"
    r"T39\.[1-2]X2|"
    r"T39\.3[19]2|"
    r"T39\.[48]X2|"
    r"T39\.92|"
    r"T40\.[0-5]X2|"
    r"T40\.6[09]2|"
    r"T40\.[78]X2|"
    r"T40\.9[09]2|"
    r"T41\.[01]X2|"
    r"T41\.2[09]2|"
    r"T41\.[35]X2|"
    r"T41\.42|"
    r"T42\.[0-6]X2|"
    r"T42\.72|"
    r"T42\.8X2|"
    r"T43\.[013468]X2|"
    r"T43\.[256][09]2|"
    r"T43\.92|"
    r"T43\.[026][12]2|"
    r"T43\.6[34]2|"
    r"T44\.[0-8]X2|"
    r"T44\.9[09]2|"
    r"T45\.[0-4]X2|"
    r"T45\.5[12]2|"
    r"T45\.6[0129]2|"
    r"T45\.[78]X2|"
    r"T45\.92|"
    r"T46\.[0-8]X2|"
    r"T46\.9[09]2|"
    r"T47\.[0-8]X2|"
    r"T47\.92|"
    r"T48\.[01]X2|"
    r"T48\.2[09]2|"
    r"T48\.[3-6]X2|"
    r"T48\.9[09]2|"
    r"T49\.[0-8]X2|"
    r"T49\.92|"
    r"T50\.[0-8]X2|"
    r"T50\.A[129]2|"
    r"T50\.B[19]2|"
    r"T50\.Z[19]2|"
    r"T50\.9[019]2|"
    r"T51\.[0-38]X2|"
    r"T51\.92|"
    r"T52\.[0-48]X2|"
    r"T52\.92|"
    r"T53\.[0-7]X2|"
    r"T53\.92|"
    r"T54\.[0-3]X2|"
    r"T54\.92|"
    r"T55\.[01]X2|"
    r"T56\.[0-7]X2|"
    r"T56\.8[19X]2|"
    r"T56\.92|"
    r"T57\.[0-38]X2|"
    r"T57\.92|"
    r"T58\.[01]2|"
    r"T58\.[28]X2|"
    r"T58\.892|"
    r"T58\.92|"
    r"T59\.[0-7]X2|"
    r"T59\.812|"
    r"T59\.892|"
    r"T59\.92|"
    r"T60\.[0-48]X2|"
    r"T60\.92|"
    r"T61\.[01]2|"
    r"T61\.7[78]2|"
    r"T61\.8X2|"
    r"T61\.92|"
    r"T62\.[0-28]X2|"
    r"T62\.92|"
    r"T63\.[03]02|"
    r"T63\.[013-8]12|"
    r"T63\.92|"
    r"T63\.[013468]22|"
    r"T63\.[03468]32|"
    r"T63\.[04]42|"
    r"T63\.452|"
    r"T63\.[04]62|"
    r"T63\.072|"
    r"T63\.[04]82|"
    r"T63\.[0135-8]92|"
    r"T63\.2X2|"
    r"T64\.[08]2|"
    r"T65\.2[129]2|"
    r"T65\.[013-6]X2|"
    r"T65\.8[1-39]2|"
    r"T65\.92|"
    r"T71\.1[1-3569]2|"
    r"T71\.2[23]2"
)

# System prompt for identifying self-harm based on ICD-9/ICD-10 codes or explicit mentions
system_self_harm = f"""You are an expert in medical chart review. Your task is to carefully analyze a patient chart and determine whether the patient has engaged in self-harm (either suicidal or non-suicidal). Follow these steps:
1. Check for the presence of any ICD-9 codes for self-harm, represented with regular expressions: {icd9_self_harm}
2. Check for the presence of any ICD-10 codes for self-harm, represented with regular expressions: {icd10_self_harm}
Please generate output strictly in JSON format according to the provided template. Ensure the output consists solely of valid JSON without any explanatory text or comments outside the JSON structure. 
You will identify up to three pieces of evidence in a list_of_evidence array, where each piece of evidence must be a VERBATIM excerpt from the chart placed in the "evidence" field, followed by icd_code if present and your reasoning in the "reasoning" field as to why you think the evidence points toward self-harm. Every item in the list_of_evidence must have the reasoning field non-blank.
After finishing generating the list_of_evidence array, synthesize all of the evidence to populate the conclusion elements as directed following the instructions in double angle brackets.
Only conclude "has_self_harm": "yes" if confidence_self_harm >= 0.5 else "no". {strict_instruction} Here is the JSON format:"""
system_self_harm = system_self_harm.replace("\n", " ")

# JSON template for self-harm output
json_self_harm = {
    "list_of_evidence": [
        {
            "evidence": "<<VERBATIM excerpt from the patient chart which reflects if the patient has engaged in self-harm. This needs to be no more than 350 characters in length and only include exact text and nothing else. This field may only be blank if there is no evidence for or against self-harm (and thus no verbatim text to include), and the associated reasoning field below must also convey that.>>",
            "icd_code": "<<Zero or more self-harm relevant comma-delimited ICD-9 or ICD-10 codes EXPLICITLY VERBATIM listed in the chart.>>",
            "reasoning": "<<Short explanation based on relevance of the evidence to self-harm. This needs to be no more than 350 characters in length, and must never be blank.>>",
        }
    ],
    "conclusion": {
        "evidence_synthesis": "<<Synthesis of the evidence, icd_code, and reasoning fields from the list_of_evidence for or against self-harm.>>",
        "confidence_self_harm": "<<What is the confidence for detecting self-harm? Give a single number ranging 0.0 to 1.0 where 0.0 is you are certain there is no self-harm and 1.0 is you are certain there is self-harm.>>",
        "has_self_harm": "<<yes if confidence_self_harm >= 0.5 else no.>>",
    },
}

# Convert the json_self_harm to a compact JSON
json_self_harm = json.dumps(json_self_harm, separators=(",", ":"))


def get_prompts(criteria_type: CriteriaType):
    """
    Returns the appropriate system prompt and user prompt template
    based on the specified criteria type.

    Args:
        criteria_type: A CriteriaType enum value indicating which prompts to return

    Returns:
        tuple: (system_prompt, user_prompt_template)
    """
    if criteria_type == CriteriaType.DIAGNOSIS:
        return system_diagnosis, json_diagnosis, user_prompt
    elif criteria_type == CriteriaType.TREATMENT:
        return system_treatment, json_treatment, user_prompt
    elif criteria_type == CriteriaType.SELF_HARM:
        return system_self_harm, json_self_harm, user_prompt
    else:
        raise ValueError(
            f"Invalid criteria type: {criteria_type}. Must be one of: diagnosis, treatment, self_harm"
        )
