import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import os
import pprint
from sklearn.metrics import (
    accuracy_score,
    precision_score,
    recall_score,
    f1_score,
    confusion_matrix,
    classification_report,
    matthews_corrcoef,
)


class ModelEvaluator:
    """
    A class to evaluate classification model performance with various metrics and visualization.

    Attributes:
        save_dir (str): Directory to save confusion matrix plot. If None, plot won't be saved.
        fig_format (str): Format for saving the figure (e.g., 'png', 'jpg', 'pdf').
    """

    def __init__(self, save_dir=None, fig_format="png"):
        """
        Initialize the ModelEvaluator.

        Args:
            save_dir (str, optional): Directory to save confusion matrix plot. Defaults to None.
            fig_format (str, optional): Format for saving the figure. Defaults to 'png'.
        """
        self.save_dir = save_dir
        self.fig_format = fig_format

        # Create save directory if it doesn't exist
        if self.save_dir is not None and not os.path.exists(self.save_dir):
            os.makedirs(self.save_dir)

    def evaluate(self, y_true, y_pred, title="Confusion Matrix", plot_cm=True):
        """
        Evaluate the model's performance using various metrics.

        Args:
            y_true (array-like): Ground truth (correct) target values.
            y_pred (array-like): Estimated targets as returned by a classifier.
            title (str, optional): Title for the confusion matrix plot. Defaults to "Confusion Matrix".
            plot_cm (bool, optional): Whether to plot the confusion matrix. Defaults to True.

        Returns:
            dict: Dictionary containing evaluation metrics.
        """
        accuracy = accuracy_score(y_true, y_pred)
        precision = precision_score(y_true, y_pred)
        recall = recall_score(y_true, y_pred)
        f1 = f1_score(y_true, y_pred)
        conf_matrix = confusion_matrix(y_true, y_pred)
        report = classification_report(y_true, y_pred)
        mcc = matthews_corrcoef(y_true, y_pred)

        # Reorder confusion matrix to have positive class first
        reordered_conf_matrix = np.array(
            [
                [conf_matrix[1, 0], conf_matrix[1, 1]],
                [conf_matrix[0, 0], conf_matrix[0, 1]],
            ]
        )
        cm_df = pd.DataFrame(
            reordered_conf_matrix, index=["1", "0"], columns=["0", "1"]
        )

        if plot_cm:
            plt.figure(figsize=(8, 6))
            sns.heatmap(
                cm_df,
                annot=True,
                fmt="d",
                cmap="Blues",
                cbar=True,
                linewidths=1,
                linecolor="black",
            )
            plt.ylabel("True Label")
            plt.xlabel("Predicted Label")
            plt.title(title)

            if self.save_dir is not None:
                save_path = os.path.join(
                    self.save_dir, f"confusion_matrix.{self.fig_format}"
                )
                plt.savefig(save_path, bbox_inches="tight", dpi=300)
                print(f"Confusion matrix saved to {save_path}")

            # plt.show()
            plt.close()

        return {
            "accuracy": accuracy,
            "precision": precision,
            "recall": recall,
            "F1_score": f1,
            "confusion_matrix": reordered_conf_matrix,
            "classification_report": report,
            "mcc": mcc,
        }

    @staticmethod
    def print_metrics(metrics, indent=2):
        """
        Pretty print the evaluation metrics.

        Args:
            metrics (dict): Dictionary of evaluation metrics.
            indent (int, optional): Indentation level for pretty printing. Defaults to 2.
        """
        pprint.pprint(metrics, indent=indent)
