import os
import subprocess


def convert_hf_to_ollama(hf_model_name, model_dir="./model"):
    """
    Convert a Hugging Face model to GGUF format, quantize it, and create an Ollama model.

    Parameters:
    -----------
    hf_model_name : str
        The Hugging Face model name in format "org/model_name"
    model_dir : str, optional
        Base directory for model storage, default is "./model"

    Returns:
    --------
    dict
        Dictionary containing paths to the created files and model information
    """
    # Parse model information
    model_brand = hf_model_name.split("/")[0]
    model_name = hf_model_name.split("/")[-1]

    # Setup directories
    current_dir = os.getcwd()
    local_model = f"{model_dir}/dpo_model/{hf_model_name}"
    gguf_dir = os.path.join(current_dir, f"{model_dir}/gguf/{model_name}.gguf")
    quantize_dir = os.path.join(
        current_dir, f"{model_dir}/gguf/{model_name}-q4-k-m.gguf"
    )

    # Convert HF model to GGUF
    convert_cmd = (
        f"python ./llama.cpp/convert_hf_to_gguf.py {local_model} --outfile {gguf_dir}"
    )
    subprocess.run(convert_cmd, shell=True, check=True)

    # Change directory to llama.cpp build/bin for quantization
    os.chdir("./llama.cpp/build/bin")

    # Quantize the GGUF model
    quantize_cmd = f"./llama-quantize {gguf_dir} {quantize_dir} Q4_K_M"
    subprocess.run(quantize_cmd, shell=True, check=True)

    # Return to original directory
    os.chdir(current_dir)

    # Create Modelfile for Ollama
    with open("./Modelfile", "w") as f:
        f.write(f"FROM {quantize_dir}")

    # Create Ollama model
    ollama_cmd = f"ollama create {model_brand}:{model_name} -f ./Modelfile"
    subprocess.run(ollama_cmd, shell=True, check=True)

    # Return information about the created model
    return {
        "model_brand": model_brand,
        "model_name": model_name,
        "local_model_path": local_model,
        "gguf_path": gguf_dir,
        "quantized_path": quantize_dir,
        "ollama_model": f"{model_brand}:{model_name}",
    }


# Example usage:
# result = convert_hf_to_ollama("Qwen/Qwen3-14B-treatment")
# print(f"Created Ollama model: {result['ollama_model']}")