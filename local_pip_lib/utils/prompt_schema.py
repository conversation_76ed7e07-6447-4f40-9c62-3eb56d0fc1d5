from __future__ import annotations
import re
from pydantic import BaseModel
from typing import Literal, List, TypeVar, Generic, Union, Optional
import pandas as pd
from .criteria import CriteriaType
from .prompt import *
import spacy


# Load spaCy model once at module level
nlp = spacy.load("en_core_web_md")
def is_semantically_similar(evidence: str, patient_note: str, similarity_threshold=0.92) -> bool:
    """
    Check if evidence is semantically similar to any part of the patient note.
    
    Args:
        evidence: The evidence text to look for
        patient_note: The full patient note to search within
        similarity_threshold: Threshold above which texts are considered similar
        
    Returns:
        Boolean indicating if the evidence is semantically found in the patient note
    """
    if not evidence.strip() or not patient_note.strip():
        return False
        
    # For very short evidence, fall back to substring check
    if len(evidence) < 5:
        return evidence.lower() in patient_note.lower()
    
    # Direct substring match check first
    if evidence.lower() in patient_note.lower():
        return True
    
    # Convert evidence to spaCy doc
    evidence_doc = nlp(evidence)
    
    # If patient note is very short, just compare directly
    if len(patient_note) < 1000:
        patient_doc = nlp(patient_note)
        # Check for empty vectors
        if not evidence_doc.has_vector or not patient_doc.has_vector:
            return False
        return evidence_doc.similarity(patient_doc) > similarity_threshold
    
    # For longer patient notes, use an improved sliding window approach
    # First tokenize the patient note
    patient_doc = nlp(patient_note)
    
    # Define window size approximately matching the evidence length with buffer
    evidence_token_count = len(evidence_doc)
    # Ensure minimum window size to avoid empty vectors
    window_size = min(max(evidence_token_count * 4, 10), len(patient_doc))
    
    # Use smaller step size for better overlap
    step_size = max(1, window_size // 4)  # 75% overlap between windows
    
    # Slide through patient note with overlapping windows
    max_similarity = 0
    for i in range(0, len(patient_doc) - window_size + 1, step_size):
        window_span = patient_doc[i:i+window_size]
        
        # Skip windows without valid vectors
        if not window_span.has_vector:
            continue
            
        try:
            similarity = evidence_doc.similarity(window_span)
            max_similarity = max(max_similarity, similarity)
            
            if max_similarity > similarity_threshold:
                return True
        except Warning:
            # Skip this window if similarity calculation raises a warning
            continue
    
    return max_similarity > similarity_threshold


# Generic type for conclusions
T = TypeVar("T", bound=BaseModel)


class Evidence(BaseModel):
    evidence: str
    icd_code: Optional[str] = ""
    reasoning: str
    def _normalize_evidence(self):
        normalized = self.evidence.strip()
        normalized = re.sub(
            r"^['\"]?(.*?)['\"]?$", r"\1", normalized
        )  # Remove outer quotes
        normalized = re.sub(r"^\[(.*?)\]$", r"\1", normalized)  # Remove brackets
        normalized = normalized.strip()
        return normalized

    def __hash__(self):
        # Hash based on normalized evidence field
        return hash(self._normalize_evidence())

    def __eq__(self, other):
        if not isinstance(other, Evidence):
            return False
        # Equal based on normalized evidence field
        return self._normalize_evidence() == other._normalize_evidence()


class ConclusionDiagnosis(BaseModel):
    evidence_synthesis: str
    confidence_mdd: float
    has_mdd: Literal["yes", "no"]

    @staticmethod
    def empty_conclusion():
        return ConclusionDiagnosis(
            evidence_synthesis="",
            confidence_mdd=0.0,
            has_mdd="no",
        )


class ConclusionTreatment(BaseModel):
    evidence_synthesis: str
    type_treatment: Union[List, Literal[
        "SSRI",
        "SNRI",
        "tricyclic",
        "tetracyclic",
        "MAOI",
        "atypical",
        "offlabel",
        "psychotherapy",
        "ECT",
        "TMS",
        "none",
    ]]
    confidence_treatment: float
    has_treatment: Literal["yes", "no"]

    @staticmethod
    def empty_conclusion():
        return ConclusionTreatment(
            evidence_synthesis="",
            type_treatment="none",
            confidence_treatment=0.0,
            has_treatment="no")


class ConclusionSelfHarm(BaseModel):
    evidence_synthesis: str
    confidence_self_harm: float
    has_self_harm: Literal["yes", "no"]

    @staticmethod
    def empty_conclusion():
        return ConclusionSelfHarm(
            evidence_synthesis="",
            confidence_self_harm=0.0,
            has_self_harm="no",
        )


class BaseSchema(BaseModel, Generic[T]):
    """
    Base Schema class for modeling reasoning about health assessments.
    Uses a generic type T for the conclusion and tracks reasoning steps.

    Attributes:
        chain_of_thought: List of reasoning steps that led to the conclusion
        conclusion: The final assessment result of type T
    """

    list_of_evidence: Optional[List[Evidence]]
    conclusion: T

    def validate_confidence(self):
        """
        Validates if the confidence score and conclusion are consistent.

        Returns:
            Boolean indicating whether confidence and conclusion are aligned:
            - True if confidence > 0.5 and has_value is "yes"
            - True if confidence < 0.5 and has_value is "no"
            - False otherwise
        """
        confidence_field = self._get_confidence_field()
        has_field = self._get_has_field()

        confidence = getattr(self.conclusion, confidence_field)
        has_value = getattr(self.conclusion, has_field)

        if not (0 <= confidence <= 1):
            return False

        if ((confidence >= 0.5) and (has_value == "yes")) or (
            (confidence < 0.5) and (has_value == "no")
        ):
            return True
        else:
            return False
        
    def score(self, chart: str) -> int:
        """
        Score the schema based on validation rules.
        Args:
            chart: The medical chart to validate against
            
        Returns:
            Integer score [0-5]
        """        
        score = 0
        # Rule 1: Check evidence entries and penalize if list length is 0 or >3
        if len(self.list_of_evidence) == 0 or len(self.list_of_evidence) > 3:
            score -= 1
        else:
            for evidence_entry in self.list_of_evidence:
                # Penalize if more than 350 characters
                if len(evidence_entry.evidence) > 350:
                    score -= 1
                # Check semantic similarity only if evidence is not empty or 'none'
                elif evidence_entry.evidence.strip() and evidence_entry.evidence.lower() != 'none':
                    if is_semantically_similar(evidence_entry.evidence.lower(), chart.lower()):
                        score += 1
                    else:
                        score -= 1

        # Rule 2: Check if the confidence is less than 0.5 and no in has_depression or vice versa
        if self.validate_confidence():
            score += 1
        else:
            score -= 1

        # Rule 3: Check for "none" or empty evidence with reasoning
        none_or_empty_count = 0

        for evidence_entry in self.list_of_evidence:
            if not evidence_entry.evidence.strip() or evidence_entry.evidence.lower() == "none":
                none_or_empty_count += 1
                # Check if this none/empty evidence has reasoning
                if not evidence_entry.reasoning.strip():
                    score -= 1
                else:
                    score += 1

        # If there is more than two none/empty evidence, penalize -1
        if none_or_empty_count > 2:
            score -= 1


        # Rule 3: Check ICD codes (only for diagnosis and self-harm)
        for evidence in self.list_of_evidence:
            if  not evidence.icd_code or not evidence.icd_code.strip():
                continue
                
            if isinstance(self.conclusion, ConclusionDiagnosis):
                diagnosis_pattern = f"({icd9_mdd})|({icd10_mdd})"
                if (evidence.icd_code.lower() in chart.lower()) and bool(re.match(diagnosis_pattern, evidence.icd_code)):
                    score += 1
                        
            elif isinstance(self.conclusion, ConclusionSelfHarm):
                self_harm_pattern = f"({icd9_self_harm})|({icd10_self_harm})"
                if (evidence.icd_code.lower() in chart.lower()) and bool(re.match(self_harm_pattern, evidence.icd_code)):
                    score += 1
        
        return score


    def icd_code_check(self):
        for evidence in self.list_of_evidence:
            if  not evidence.icd_code or not evidence.icd_code.strip():
                continue
            return evidence.icd_code
        return False
            
    @staticmethod
    def clean_list(input_list):
        """
        Removes unwanted elements from a list such as:
        - Empty strings
        - Whitespace characters (spaces, tabs, etc.)
        - Newline characters and their string representations
        - Commas and other common separators

        Args:
            input_list (list): The list to clean

        Returns:
            list: A new list with unwanted elements removed
        """
        # Define elements to explicitly remove
        unwanted_elements = ["/n", "\\n", "\n", ",", " ", "\t", "\r"]

        # Create a new list excluding unwanted elements and empty strings
        cleaned = []
        for item in input_list:
            # Skip if item is in unwanted_elements list
            if item in unwanted_elements:
                continue

            # Skip if item is empty after stripping whitespace
            if isinstance(item, str) and not item.strip():
                continue

            # Keep the item if it passed the filters
            cleaned.append(item)

        return cleaned

    def _get_confidence_field(self) -> str:
        """
        Returns the confidence field name based on conclusion type.

        Returns:
            String name of the confidence field appropriate for the conclusion type

        Raises:
            AttributeError: If conclusion type is not recognized
        """
        if isinstance(self.conclusion, ConclusionDiagnosis):
            return "confidence_mdd"
        elif isinstance(self.conclusion, ConclusionTreatment):
            return "confidence_treatment"
        elif isinstance(self.conclusion, ConclusionSelfHarm):
            return "confidence_self_harm"
        raise AttributeError("Unknown conclusion type")

    def _get_has_field(self) -> str:
        """
        Returns the has_* field name based on conclusion type.

        Returns:
            String name of the has_* field appropriate for the conclusion type

        Raises:
            AttributeError: If conclusion type is not recognized
        """
        if isinstance(self.conclusion, ConclusionDiagnosis):
            return "has_mdd"
        elif isinstance(self.conclusion, ConclusionTreatment):
            return "has_treatment"
        elif isinstance(self.conclusion, ConclusionSelfHarm):
            return "has_self_harm"
        raise AttributeError("Unknown conclusion type")


class DiagnosisSchema(BaseSchema[ConclusionDiagnosis]):
    pass


class TreatmentSchema(BaseSchema[ConclusionTreatment]):
    pass


class SelfHarmSchema(BaseSchema[ConclusionSelfHarm]):
    pass

def create_empty_schema(criteria: CriteriaType):
    """
    Create an empty schema based on the specified type.

    Args:
        criteria: One of CriteriaType

    Returns:
        An empty schema of the specified type
    """
    # Create empty Evidence
    empty_evidence = Evidence(evidence="", reasoning="", icd_code="")

    if criteria == CriteriaType.DIAGNOSIS:
        return DiagnosisSchema(
            list_of_evidence=[empty_evidence],
            conclusion=ConclusionDiagnosis.empty_conclusion(),
        )

    elif criteria == CriteriaType.TREATMENT:
        return TreatmentSchema(
            list_of_evidence=[empty_evidence],
            conclusion=ConclusionTreatment.empty_conclusion(),
        )

    elif criteria == CriteriaType.SELF_HARM:
        return SelfHarmSchema(
            list_of_evidence=[empty_evidence],
            conclusion=ConclusionSelfHarm.empty_conclusion(),
        )

    else:
        valid_types = ", ".join([f"CriteriaType.{ct.name}" for ct in CriteriaType])
        raise ValueError(
            f"Unknown Criteria type: {criteria}. Must be one of: {valid_types}"
        )

def getSchema(criteria: CriteriaType):
    if criteria == CriteriaType.DIAGNOSIS:
        return DiagnosisSchema
    elif criteria == CriteriaType.TREATMENT:
        return TreatmentSchema

    elif criteria == CriteriaType.SELF_HARM:
        return SelfHarmSchema

    else:
        valid_types = ", ".join([f"CriteriaType.{ct.name}" for ct in CriteriaType])
        raise ValueError(
            f"Unknown Criteria type: {criteria}. Must be one of: {valid_types}"
        )
