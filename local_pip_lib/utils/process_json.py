import ast


def extract_nested_dict_to_columns(
    df, list_column=["diagnosis", "prescription", "self_harm"], nested_key="conclusion"
):
    df_copy = df.copy()
    for dict_column in list_column:
        if isinstance(df_copy[dict_column].iloc[0], str):
            df_copy[dict_column] = df_copy[dict_column].apply(ast.literal_eval)

        # Get the keys from the nested dictionary (conclusion) to know what columns to create
        if len(df_copy) > 0:
            first_dict = df_copy[dict_column].iloc[0]
            nested_dict = first_dict.get(nested_key, {})
            for key in nested_dict.keys():
                df_copy[key] = df_copy[dict_column].apply(
                    lambda x: x.get(nested_key, {}).get(key, None)
                )
    return df_copy


def count_yes_occurrences(df, count=0):
    # Create a new column that counts 'yes' occurrences (case-insensitive) across the three column

    df["diagnosis_count"] = df["diagnosis"].apply(
        lambda x: int(x.conclusion.has_mdd.lower() == "yes")
    )
    df["treatment_count"] = df["treatment"].apply(
        lambda x: int(x.conclusion.has_treatment.lower() == "yes")
    )
    df["self_harm_count"] = df["self_harm"].apply(
        lambda x: int(x.conclusion.has_self_harm.lower() == "yes")
    )
    df["count"] = df["diagnosis_count"] + df["self_harm_count"] + df["treatment_count"]
    df["prediction"] = (df["count"] > count).astype(int)
    return df
