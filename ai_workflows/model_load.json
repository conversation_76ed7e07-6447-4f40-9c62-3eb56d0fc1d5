{"icon_bg_color": null, "is_component": false, "data": {"nodes": [{"id": "model_loading-r8Swr", "type": "genericNode", "position": {"x": 709.7553477375507, "y": 266.18659384571504}, "data": {"node": {"template": {"_type": "Component", "base_model": {"tool_mode": false, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "base_model", "value": "unsloth/Meta-Llama-3.1-8B-Instruct", "display_name": "Base Model", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "", "title_case": false, "type": "str", "_input_type": "MultilineInput"}, "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.field_typing import Data\nimport torch\nimport os\nimport gc\nfrom langflow.custom import Component\nfrom langflow.io import Output, MultilineInput\nfrom langflow.schema import Message\nfrom unsloth import FastLanguageModel\n\n\nclass ModelLoad(Component):\n    \"\"\"\n    A component to load a language model, apply LoRA fine-tuning, and save it to a specified directory.\n    \"\"\"\n\n    display_name = \"Model Load\"\n    description: str = (\n        \"Takes the name of the model and saves it to the models directory.\"\n    )\n    icon = \"code\"\n    name = \"model_loading\"\n\n    inputs = [\n        MultilineInput(\n            name=\"base_model\",\n            value=\"unsloth/Meta-Llama-3.1-8B-Instruct\",\n            display_name=\"Base Model\",\n        )\n    ]\n\n    outputs = [Output(display_name=\"path\", name=\"path\", method=\"build_model\")]\n\n    def build_model(self) -> Message:\n        \"\"\"\n        Loads the model, applies LoRA fine-tuning, and saves it to the specified directory.\n        Returns the path where the model is saved.\n        \"\"\"\n        model_path = \"./models/\" + self.base_model\n\n        # Check if the model directory already exists\n        if not os.path.exists(model_path):\n            os.makedirs(model_path)\n        else:\n            return Message(text=model_path)\n\n        try:\n            # Clear GPU cache to free up memory\n            torch.cuda.empty_cache()\n\n            # Load the base model and tokenizer\n            model, tokenizer = FastLanguageModel.from_pretrained(\n                model_name=self.base_model,\n                dtype=None,\n                load_in_4bit=True,\n                device_map=\"cuda\",\n            )\n\n            # Apply LoRA fine-tuning to the model\n            model = FastLanguageModel.get_peft_model(\n                model,\n                r=128,  # LoRA rank (suggested values: 8, 16, 32, 64, 128)\n                target_modules=[\n                    \"q_proj\",\n                    \"k_proj\",\n                    \"v_proj\",\n                    \"o_proj\",\n                    \"gate_proj\",\n                    \"up_proj\",\n                    \"down_proj\",\n                ],\n                lora_alpha=16,\n                lora_dropout=0,\n                bias=\"none\",\n                use_gradient_checkpointing=\"unsloth\",\n                random_state=3407,\n                use_rslora=False,\n                loftq_config=None,\n            )\n\n            # Define a custom chat template for the tokenizer\n            llama3_template = \"\"\"\n            {% set loop_messages = messages %}\n            {% for message in loop_messages %}\n                {% if message['role'] == 'user' %}\n                    {{ '<|start_header_id|>user<|end_header_id|>\\n\\n' + message['content'] + '<|eot_id|>' }}\n                {% elif message['role'] == 'assistant' %}\n                    {{ '<|start_header_id|>assistant<|end_header_id|>\\n\\n' + message['content'] + '<|eot_id|>' }}\n                {% endif %}\n            {% endfor %}\n            {% if add_generation_prompt %}{{ '<|start_header_id|>assistant<|end_header_id|>\\n\\n' }}{% endif %}\n            \"\"\"\n            tokenizer.chat_template = llama3_template\n\n            # Save the fine-tuned model and tokenizer\n            model.save_pretrained(model_path)\n            tokenizer.save_pretrained(model_path)\n\n            # Clean up memory\n            del model\n            del tokenizer\n            gc.collect()\n            if torch.cuda.is_available():\n                torch.cuda.empty_cache()\n\n            return Message(text=model_path)\n        except Exception as e:\n            # Return the model path even if an error occurs\n            print(f\"An error occurred: {e}\")\n            return Message(text=model_path)\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}}, "description": "Takes the name of the model and saves it to the models directory.", "icon": "code", "base_classes": ["Message"], "display_name": "Model Load", "documentation": "", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Message"], "selected": "Message", "name": "path", "hidden": null, "display_name": "path", "method": "build_model", "value": "__UNDEFINED__", "cache": true, "required_inputs": null, "allows_loop": false}], "field_order": ["base_model"], "beta": false, "legacy": false, "edited": true, "metadata": {}, "tool_mode": false}, "showNode": true, "type": "model_loading", "id": "model_loading-r8Swr"}, "selected": true, "measured": {"width": 320, "height": 249}, "dragging": false}, {"id": "TextInput-KeKtO", "type": "genericNode", "position": {"x": 333.2219019670037, "y": 283.04130014501027}, "data": {"node": {"template": {"_type": "Component", "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.base.io.text import TextComponent\nfrom langflow.io import MultilineInput, Output\nfrom langflow.schema.message import Message\n\n\nclass TextInputComponent(TextComponent):\n    display_name = \"Text Input\"\n    description = \"Get text inputs from the Playground.\"\n    icon = \"type\"\n    name = \"TextInput\"\n\n    inputs = [\n        MultilineInput(\n            name=\"input_value\",\n            display_name=\"Text\",\n            info=\"Text to be passed as input.\",\n        ),\n    ]\n    outputs = [\n        Output(display_name=\"Message\", name=\"text\", method=\"text_response\"),\n    ]\n\n    def text_response(self) -> Message:\n        return Message(\n            text=self.input_value,\n        )\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "input_value": {"tool_mode": false, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "input_value", "value": "unsloth/Meta-Llama-3.1-8B-Instruct-bnb-4bit", "display_name": "Text", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Text to be passed as input.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}}, "description": "Get text inputs from the Playground.", "icon": "type", "base_classes": ["Message"], "display_name": "Text Input", "documentation": "", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Message"], "selected": "Message", "name": "text", "display_name": "Message", "method": "text_response", "value": "__UNDEFINED__", "cache": true, "allows_loop": false, "tool_mode": true}], "field_order": ["input_value"], "beta": false, "legacy": false, "edited": false, "metadata": {}, "tool_mode": false, "lf_version": "1.1.3"}, "showNode": true, "type": "TextInput", "id": "TextInput-KeKtO"}, "selected": false, "measured": {"width": 320, "height": 229}, "dragging": false}, {"id": "ChatOutput-1kzgP", "type": "genericNode", "position": {"x": 1196.7408852513292, "y": 419.7171154214442}, "data": {"node": {"template": {"_type": "Component", "background_color": {"tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "background_color", "value": "", "display_name": "Background Color", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "The background color of the icon.", "title_case": false, "type": "str", "_input_type": "MessageTextInput"}, "chat_icon": {"tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "chat_icon", "value": "", "display_name": "Icon", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "The icon of the message.", "title_case": false, "type": "str", "_input_type": "MessageTextInput"}, "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.base.io.chat import ChatComponent\nfrom langflow.inputs import BoolInput\nfrom langflow.io import DropdownInput, MessageInput, MessageTextInput, Output\nfrom langflow.schema.message import Message\nfrom langflow.schema.properties import Source\nfrom langflow.utils.constants import (\n    MESSAGE_SENDER_AI,\n    MESSAGE_SENDER_NAME_AI,\n    MESSAGE_SENDER_USER,\n)\n\n\nclass ChatOutput(ChatComponent):\n    display_name = \"Chat Output\"\n    description = \"Display a chat message in the Playground.\"\n    icon = \"MessagesSquare\"\n    name = \"ChatOutput\"\n    minimized = True\n\n    inputs = [\n        MessageInput(\n            name=\"input_value\",\n            display_name=\"Text\",\n            info=\"Message to be passed as output.\",\n        ),\n        BoolInput(\n            name=\"should_store_message\",\n            display_name=\"Store Messages\",\n            info=\"Store the message in the history.\",\n            value=True,\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"sender\",\n            display_name=\"Sender Type\",\n            options=[MESSAGE_SENDER_AI, MESSAGE_SENDER_USER],\n            value=MESSAGE_SENDER_AI,\n            advanced=True,\n            info=\"Type of sender.\",\n        ),\n        MessageTextInput(\n            name=\"sender_name\",\n            display_name=\"Sender Name\",\n            info=\"Name of the sender.\",\n            value=MESSAGE_SENDER_NAME_AI,\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"session_id\",\n            display_name=\"Session ID\",\n            info=\"The session ID of the chat. If empty, the current session ID parameter will be used.\",\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"data_template\",\n            display_name=\"Data Template\",\n            value=\"{text}\",\n            advanced=True,\n            info=\"Template to convert Data to Text. If left empty, it will be dynamically set to the Data's text key.\",\n        ),\n        MessageTextInput(\n            name=\"background_color\",\n            display_name=\"Background Color\",\n            info=\"The background color of the icon.\",\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"chat_icon\",\n            display_name=\"Icon\",\n            info=\"The icon of the message.\",\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"text_color\",\n            display_name=\"Text Color\",\n            info=\"The text color of the name\",\n            advanced=True,\n        ),\n    ]\n    outputs = [\n        Output(\n            display_name=\"Message\",\n            name=\"message\",\n            method=\"message_response\",\n        ),\n    ]\n\n    def _build_source(self, id_: str | None, display_name: str | None, source: str | None) -> Source:\n        source_dict = {}\n        if id_:\n            source_dict[\"id\"] = id_\n        if display_name:\n            source_dict[\"display_name\"] = display_name\n        if source:\n            source_dict[\"source\"] = source\n        return Source(**source_dict)\n\n    async def message_response(self) -> Message:\n        source, icon, display_name, source_id = self.get_properties_from_source_component()\n        background_color = self.background_color\n        text_color = self.text_color\n        if self.chat_icon:\n            icon = self.chat_icon\n        message = self.input_value if isinstance(self.input_value, Message) else Message(text=self.input_value)\n        message.sender = self.sender\n        message.sender_name = self.sender_name\n        message.session_id = self.session_id\n        message.flow_id = self.graph.flow_id if hasattr(self, \"graph\") else None\n        message.properties.source = self._build_source(source_id, display_name, source)\n        message.properties.icon = icon\n        message.properties.background_color = background_color\n        message.properties.text_color = text_color\n        if self.session_id and isinstance(message, Message) and self.should_store_message:\n            stored_message = await self.send_message(\n                message,\n            )\n            self.message.value = stored_message\n            message = stored_message\n\n        self.status = message\n        return message\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "data_template": {"tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "data_template", "value": "{text}", "display_name": "Data Template", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "Template to convert Data to Text. If left empty, it will be dynamically set to the Data's text key.", "title_case": false, "type": "str", "_input_type": "MessageTextInput"}, "input_value": {"trace_as_input": true, "tool_mode": false, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "input_value", "value": "", "display_name": "Text", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Message to be passed as output.", "title_case": false, "type": "str", "_input_type": "MessageInput"}, "sender": {"tool_mode": false, "trace_as_metadata": true, "options": ["Machine", "User"], "options_metadata": [], "combobox": false, "dialog_inputs": {}, "required": false, "placeholder": "", "show": true, "name": "sender", "value": "Machine", "display_name": "Sender Type", "advanced": true, "dynamic": false, "info": "Type of sender.", "title_case": false, "type": "str", "_input_type": "DropdownInput"}, "sender_name": {"tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "sender_name", "value": "AI", "display_name": "Sender Name", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "Name of the sender.", "title_case": false, "type": "str", "_input_type": "MessageTextInput"}, "session_id": {"tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "session_id", "value": "", "display_name": "Session ID", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "The session ID of the chat. If empty, the current session ID parameter will be used.", "title_case": false, "type": "str", "_input_type": "MessageTextInput"}, "should_store_message": {"tool_mode": false, "trace_as_metadata": true, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "should_store_message", "value": true, "display_name": "Store Messages", "advanced": true, "dynamic": false, "info": "Store the message in the history.", "title_case": false, "type": "bool", "_input_type": "BoolInput"}, "text_color": {"tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "text_color", "value": "", "display_name": "Text Color", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "The text color of the name", "title_case": false, "type": "str", "_input_type": "MessageTextInput"}}, "description": "Display a chat message in the Playground.", "icon": "MessagesSquare", "base_classes": ["Message"], "display_name": "Chat Output", "documentation": "", "minimized": true, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Message"], "selected": "Message", "name": "message", "display_name": "Message", "method": "message_response", "value": "__UNDEFINED__", "cache": true, "allows_loop": false}], "field_order": ["input_value", "should_store_message", "sender", "sender_name", "session_id", "data_template", "background_color", "chat_icon", "text_color"], "beta": false, "legacy": false, "edited": false, "metadata": {}, "tool_mode": false}, "showNode": false, "type": "ChatOutput", "id": "ChatOutput-1kzgP"}, "selected": false, "measured": {"width": 192, "height": 66}, "dragging": false}], "edges": [{"source": "TextInput-KeKtO", "sourceHandle": "{œdataTypeœ:œTextInputœ,œidœ:œTextInput-KeKtOœ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}", "target": "model_loading-r8Swr", "targetHandle": "{œfieldNameœ:œbase_modelœ,œidœ:œmodel_loading-r8Swrœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "data": {"targetHandle": {"fieldName": "base_model", "id": "model_loading-r8Swr", "inputTypes": ["Message"], "type": "str"}, "sourceHandle": {"dataType": "TextInput", "id": "TextInput-KeKtO", "name": "text", "output_types": ["Message"]}}, "id": "reactflow__edge-TextInput-KeKtO{œdataTypeœ:œTextInputœ,œidœ:œTextInput-KeKtOœ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}-model_loading-r8Swr{œfieldNameœ:œbase_modelœ,œidœ:œmodel_loading-r8Swrœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "className": "", "animated": false}, {"source": "model_loading-r8Swr", "sourceHandle": "{œdataTypeœ:œmodel_loadingœ,œidœ:œmodel_loading-r8Swrœ,œnameœ:œpathœ,œoutput_typesœ:[œMessageœ]}", "target": "ChatOutput-1kzgP", "targetHandle": "{œfieldNameœ:œinput_valueœ,œidœ:œChatOutput-1kzgPœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "data": {"targetHandle": {"fieldName": "input_value", "id": "ChatOutput-1kzgP", "inputTypes": ["Message"], "type": "str"}, "sourceHandle": {"dataType": "model_loading", "id": "model_loading-r8Swr", "name": "path", "output_types": ["Message"]}}, "id": "xy-edge__model_loading-r8Swr{œdataTypeœ:œmodel_loadingœ,œidœ:œmodel_loading-r8Swrœ,œnameœ:œpathœ,œoutput_typesœ:[œMessageœ]}-ChatOutput-1kzgP{œfieldNameœ:œinput_valueœ,œidœ:œChatOutput-1kzgPœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "animated": false, "className": ""}], "viewport": {"x": 180.0976555162896, "y": 237.2651846263086, "zoom": 0.7791645315885721}}, "updated_at": "2025-03-11T19:39:11+00:00", "webhook": false, "description": "Takes the Unsloth LLaMA Instruct model as input, saves it to the \"models\" directory on your computer, and returns the file path where the model is saved. The returned path is formatted as a Langflow message data type.", "icon": null, "locked": false, "name": "model_load", "endpoint_name": null, "id": "0c6e041b-1fc5-4fce-88ca-a3736ae53e5b", "tags": null, "gradient": null, "user_id": "8aeac423-0c22-4fa5-ad8d-1fca48a9fd5c", "folder_id": "a54bb9f3-3fb2-42cd-a017-02fa33ffcfd6"}