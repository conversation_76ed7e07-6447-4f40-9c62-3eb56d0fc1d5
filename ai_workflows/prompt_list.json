{"updated_at": "2025-03-17T15:30:58+00:00", "webhook": false, "description": "This flow takes a file containing a list of prompts as input, fine-tunes the specified model on these prompts, and evaluates the model's performance. It returns the accuracy metric for each prompt after fine-tuning.", "icon": null, "locked": false, "name": "prompt_list", "endpoint_name": null, "tags": null, "gradient": null, "id": "a15070c1-0f52-4291-8615-fc5192826009", "is_component": false, "user_id": "852a84df-3c42-4c14-a7a3-a13e5c7b8489", "folder_id": "1b8531fb-dac2-4960-b8f7-a22ccb1403f1", "icon_bg_color": null, "data": {"nodes": [{"id": "RunFlow-NHJUX", "type": "genericNode", "position": {"x": 378.11123219153296, "y": 181.20479529289165}, "data": {"node": {"template": {"_type": "Component", "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from typing import Any\n\nfrom loguru import logger\n\nfrom langflow.base.tools.run_flow import RunFlow<PERSON><PERSON>Component\nfrom langflow.helpers.flow import run_flow\nfrom langflow.schema import dotdict\n\n\nclass RunFlowComponent(RunFlowBaseComponent):\n    display_name = \"Run Flow\"\n    description = \"Creates a tool component from a Flow that takes all its inputs and runs it.\"\n    beta = True\n    name = \"RunFlow\"\n    icon = \"Workflow\"\n\n    inputs = RunFlowBaseComponent._base_inputs\n    outputs = RunFlowBaseComponent._base_outputs\n\n    async def update_build_config(self, build_config: dotdict, field_value: Any, field_name: str | None = None):\n        if field_name == \"flow_name_selected\":\n            build_config[\"flow_name_selected\"][\"options\"] = await self.get_flow_names()\n            missing_keys = [key for key in self.default_keys if key not in build_config]\n            if missing_keys:\n                msg = f\"Missing required keys in build_config: {missing_keys}\"\n                raise ValueError(msg)\n            if field_value is not None:\n                try:\n                    graph = await self.get_graph(field_value)\n                    build_config = self.update_build_config_from_graph(build_config, graph)\n                except Exception as e:\n                    msg = f\"Error building graph for flow {field_value}\"\n                    logger.exception(msg)\n                    raise RuntimeError(msg) from e\n        return build_config\n\n    async def run_flow_with_tweaks(self):\n        tweaks: dict = {}\n\n        flow_name_selected = self._attributes.get(\"flow_name_selected\")\n        parsed_flow_tweak_data = self._attributes.get(\"flow_tweak_data\", {})\n        if not isinstance(parsed_flow_tweak_data, dict):\n            parsed_flow_tweak_data = parsed_flow_tweak_data.dict()\n\n        if parsed_flow_tweak_data != {}:\n            for field in parsed_flow_tweak_data:\n                if \"~\" in field:\n                    [node, name] = field.split(\"~\")\n                    if node not in tweaks:\n                        tweaks[node] = {}\n                    tweaks[node][name] = parsed_flow_tweak_data[field]\n        else:\n            for field in self._attributes:\n                if field not in self.default_keys and \"~\" in field:\n                    [node, name] = field.split(\"~\")\n                    if node not in tweaks:\n                        tweaks[node] = {}\n                    tweaks[node][name] = self._attributes[field]\n\n        return await run_flow(\n            inputs=None,\n            output_type=\"all\",\n            flow_id=None,\n            flow_name=flow_name_selected,\n            tweaks=tweaks,\n            user_id=str(self.user_id),\n            # run_id=self.graph.run_id,\n            session_id=self.graph.session_id or self.session_id,\n        )\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "flow_name_selected": {"tool_mode": false, "trace_as_metadata": true, "options": ["model_load", "fine_tuning", "Confidence_interval", "Inference", "prompt_list", "hyperparameter_tuning"], "options_metadata": [], "combobox": false, "dialog_inputs": {}, "required": false, "placeholder": "", "show": true, "name": "flow_name_selected", "display_name": "Flow Name", "advanced": false, "dynamic": false, "info": "The name of the flow to run.", "real_time_refresh": true, "refresh_button": true, "title_case": false, "type": "str", "_input_type": "DropdownInput", "value": "fine_tuning"}, "session_id": {"trace_as_input": true, "tool_mode": false, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "session_id", "value": {"text_key": "text", "data": {"text": "", "files": [], "timestamp": "2025-03-03 15:31:17 UTC"}, "default_value": ""}, "display_name": "Session ID", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "The session ID to run the flow in.", "title_case": false, "type": "str", "_input_type": "MessageInput"}, "TextInput-bcn5q~input_value": {"tool_mode": true, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "TextInput-bcn5q~input_value", "value": "datasets/train_test_split/x_train.csv", "display_name": "Patient Note - Text", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Text to be passed as input.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}, "TextInput-pNP0w~input_value": {"tool_mode": true, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "TextInput-pNP0w~input_value", "value": "datasets/train_test_split/y_train.csv", "display_name": "Patient Label - Text", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Text to be passed as input.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}, "TextInput-DBkbt~input_value": {"tool_mode": true, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "TextInput-DBkbt~input_value", "value": "./models/unsloth/Meta-Llama-3.1-8B-Instruct", "display_name": "Model path - Text", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Text to be passed as input.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}, "TextInput-6u1pS~input_value": {"tool_mode": true, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "TextInput-6u1pS~input_value", "value": "\"\"\" Your task is to determine whether the patient described in the note have depression or not   **Output Instructions:** provide your final answer (only the number \"1\" or \"0\") in this format **The final answer is: {{ 0 or 1}}**.      ---     ** Patient Note Summary:**     {}     Output the result (1 for depression present, or 0 otherwise) in this format **The final answer is: **.     \"\"\"", "display_name": "Prompt - Text", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Text to be passed as input.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}, "TextInput-BHYQU~input_value": {"tool_mode": true, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "TextInput-BHYQU~input_value", "value": "{'learning_rate': 0.0003068487651615263, 'gradient_accumulation_steps': 16, 'epochs': 1, 'weight_decay': 0.02857038520567239, 'warmup_ratio': 0.1757736057365846}", "display_name": "config - Text", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Text to be passed as input.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}}, "description": "Creates a tool component from a Flow that takes all its inputs and runs it.", "icon": "Workflow", "base_classes": ["Data", "DataFrame", "Message"], "display_name": "Run Flow", "documentation": "", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Data"], "selected": "Data", "name": "flow_outputs_data", "hidden": true, "display_name": "Flow Data Output", "method": "data_output", "value": "__UNDEFINED__", "cache": true, "required_inputs": [], "allows_loop": false}, {"types": ["DataFrame"], "selected": "DataFrame", "name": "flow_outputs_dataframe", "hidden": true, "display_name": "Flow Dataframe Output", "method": "dataframe_output", "value": "__UNDEFINED__", "cache": true, "required_inputs": [], "allows_loop": false}, {"types": ["Message"], "selected": "Message", "name": "flow_outputs_message", "hidden": null, "display_name": "Flow Message Output", "method": "message_output", "value": "__UNDEFINED__", "cache": true, "required_inputs": [], "allows_loop": false}], "field_order": ["flow_name_selected", "session_id"], "beta": true, "legacy": false, "edited": false, "metadata": {}, "tool_mode": false, "lf_version": "1.1.3"}, "showNode": true, "type": "RunFlow", "id": "RunFlow-NHJUX"}, "selected": false, "measured": {"width": 320, "height": 660}, "dragging": false}, {"id": "RunFlow-IinJF", "type": "genericNode", "position": {"x": -4.2001866341338285, "y": 298.8853285383497}, "data": {"node": {"template": {"_type": "Component", "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from typing import Any\n\nfrom loguru import logger\n\nfrom langflow.base.tools.run_flow import RunFlow<PERSON><PERSON>Component\nfrom langflow.helpers.flow import run_flow\nfrom langflow.schema import dotdict\n\n\nclass RunFlowComponent(RunFlowBaseComponent):\n    display_name = \"Run Flow\"\n    description = \"Creates a tool component from a Flow that takes all its inputs and runs it.\"\n    beta = True\n    name = \"RunFlow\"\n    icon = \"Workflow\"\n\n    inputs = RunFlowBaseComponent._base_inputs\n    outputs = RunFlowBaseComponent._base_outputs\n\n    async def update_build_config(self, build_config: dotdict, field_value: Any, field_name: str | None = None):\n        if field_name == \"flow_name_selected\":\n            build_config[\"flow_name_selected\"][\"options\"] = await self.get_flow_names()\n            missing_keys = [key for key in self.default_keys if key not in build_config]\n            if missing_keys:\n                msg = f\"Missing required keys in build_config: {missing_keys}\"\n                raise ValueError(msg)\n            if field_value is not None:\n                try:\n                    graph = await self.get_graph(field_value)\n                    build_config = self.update_build_config_from_graph(build_config, graph)\n                except Exception as e:\n                    msg = f\"Error building graph for flow {field_value}\"\n                    logger.exception(msg)\n                    raise RuntimeError(msg) from e\n        return build_config\n\n    async def run_flow_with_tweaks(self):\n        tweaks: dict = {}\n\n        flow_name_selected = self._attributes.get(\"flow_name_selected\")\n        parsed_flow_tweak_data = self._attributes.get(\"flow_tweak_data\", {})\n        if not isinstance(parsed_flow_tweak_data, dict):\n            parsed_flow_tweak_data = parsed_flow_tweak_data.dict()\n\n        if parsed_flow_tweak_data != {}:\n            for field in parsed_flow_tweak_data:\n                if \"~\" in field:\n                    [node, name] = field.split(\"~\")\n                    if node not in tweaks:\n                        tweaks[node] = {}\n                    tweaks[node][name] = parsed_flow_tweak_data[field]\n        else:\n            for field in self._attributes:\n                if field not in self.default_keys and \"~\" in field:\n                    [node, name] = field.split(\"~\")\n                    if node not in tweaks:\n                        tweaks[node] = {}\n                    tweaks[node][name] = self._attributes[field]\n\n        return await run_flow(\n            inputs=None,\n            output_type=\"all\",\n            flow_id=None,\n            flow_name=flow_name_selected,\n            tweaks=tweaks,\n            user_id=str(self.user_id),\n            # run_id=self.graph.run_id,\n            session_id=self.graph.session_id or self.session_id,\n        )\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "flow_name_selected": {"tool_mode": false, "trace_as_metadata": true, "options": ["model_load", "fine_tuning", "Confidence_interval", "Inference", "prompt_list"], "options_metadata": [], "combobox": false, "dialog_inputs": {}, "required": false, "placeholder": "", "show": true, "name": "flow_name_selected", "display_name": "Flow Name", "advanced": false, "dynamic": false, "info": "The name of the flow to run.", "real_time_refresh": true, "refresh_button": true, "title_case": false, "type": "str", "_input_type": "DropdownInput", "value": "model_load"}, "session_id": {"trace_as_input": true, "tool_mode": false, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "session_id", "value": {"text_key": "text", "data": {"text": "", "files": [], "timestamp": "2025-03-03 17:30:54 UTC"}, "default_value": ""}, "display_name": "Session ID", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "The session ID to run the flow in.", "title_case": false, "type": "str", "_input_type": "MessageInput"}, "TextInput-KeKtO~input_value": {"tool_mode": true, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "TextInput-KeKtO~input_value", "value": "unsloth/Meta-Llama-3.1-8B-Instruct", "display_name": "Text Input - Text", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Text to be passed as input.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}}, "description": "Creates a tool component from a Flow that takes all its inputs and runs it.", "icon": "Workflow", "base_classes": ["Data", "DataFrame", "Message"], "display_name": "Run Flow", "documentation": "", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Data"], "selected": "Data", "name": "flow_outputs_data", "hidden": true, "display_name": "Flow Data Output", "method": "data_output", "value": "__UNDEFINED__", "cache": true, "required_inputs": [], "allows_loop": false}, {"types": ["DataFrame"], "selected": "DataFrame", "name": "flow_outputs_dataframe", "hidden": true, "display_name": "Flow Dataframe Output", "method": "dataframe_output", "value": "__UNDEFINED__", "cache": true, "required_inputs": [], "allows_loop": false}, {"types": ["Message"], "selected": "Message", "name": "flow_outputs_message", "hidden": null, "display_name": "Flow Message Output", "method": "message_output", "value": "__UNDEFINED__", "cache": true, "required_inputs": [], "allows_loop": false}], "field_order": ["flow_name_selected", "session_id"], "beta": true, "legacy": false, "edited": false, "metadata": {}, "tool_mode": false, "lf_version": "1.1.3"}, "showNode": true, "type": "RunFlow", "id": "RunFlow-IinJF"}, "selected": false, "measured": {"width": 320, "height": 332}, "dragging": false}, {"id": "TextInput-UX4dS", "type": "genericNode", "position": {"x": -1214.6288744853518, "y": 279.3853472524106}, "data": {"node": {"template": {"_type": "Component", "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.base.io.text import TextComponent\nfrom langflow.io import MultilineInput, Output\nfrom langflow.schema.message import Message\n\n\nclass TextInputComponent(TextComponent):\n    display_name = \"Prompt Loader\"\n    description = \"Load the prompt from a txt file.\"\n    icon = \"type\"\n    name = \"prompts\"\n\n    inputs = [\n        MultilineInput(\n            name=\"prompts\",\n            display_name=\"Text\",\n            info=\"Text to be passed as input.\",\n        ),\n    ]\n    outputs = [\n        Output(display_name=\"Data_list\", name=\"text\", method=\"text_response\"),\n    ]\n\n    def text_response(self) -> [Data]:\n        with open(self.prompts, 'r') as file:\n            loaded_prompts = file.readlines()\n            # Remove any trailing newline characters\n            prompts = [prompt.strip() for prompt in loaded_prompts]\n        ret_prompts = [Data(data={\"prompt\":p}) for p in prompts]\n        return ret_prompts\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "prompts": {"tool_mode": false, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "prompts", "value": "datasets/prompts.txt", "display_name": "Text", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Text to be passed as input.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}}, "description": "Load the prompt from a txt file.", "icon": "type", "base_classes": ["Data"], "display_name": "Prompt Loader", "documentation": "", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Data"], "selected": "Data", "name": "text", "hidden": null, "display_name": "Data_list", "method": "text_response", "value": "__UNDEFINED__", "cache": true, "required_inputs": null, "allows_loop": false}], "field_order": ["prompts"], "beta": false, "legacy": false, "edited": true, "metadata": {}, "tool_mode": false, "lf_version": "1.1.3"}, "showNode": true, "type": "prompts", "id": "TextInput-UX4dS"}, "selected": false, "measured": {"width": 320, "height": 229}, "dragging": false}, {"id": "LoopComponent-qgNpM", "type": "genericNode", "position": {"x": -795.6668015476957, "y": 293.8350476190032}, "data": {"node": {"template": {"_type": "Component", "data": {"tool_mode": false, "trace_as_metadata": true, "list": false, "list_add_label": "Add More", "trace_as_input": true, "required": false, "placeholder": "", "show": true, "name": "data", "value": "", "display_name": "Data", "advanced": false, "input_types": ["Data"], "dynamic": false, "info": "The initial list of Data objects to iterate over.", "title_case": false, "type": "other", "_input_type": "DataInput"}, "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.custom import Component\nfrom langflow.io import DataInput, Output\nfrom langflow.schema import Data\n\n\nclass LoopComponent(Component):\n    display_name = \"Loop\"\n    description = (\n        \"Iterates over a list of Data objects, outputting one item at a time and aggregating results from loop inputs.\"\n    )\n    icon = \"infinity\"\n\n    inputs = [\n        DataInput(\n            name=\"data\",\n            display_name=\"Data\",\n            info=\"The initial list of Data objects to iterate over.\",\n        ),\n    ]\n\n    outputs = [\n        Output(display_name=\"Item\", name=\"item\", method=\"item_output\", allows_loop=True),\n        Output(display_name=\"Done\", name=\"done\", method=\"done_output\"),\n    ]\n\n    def initialize_data(self) -> None:\n        \"\"\"Initialize the data list, context index, and aggregated list.\"\"\"\n        if self.ctx.get(f\"{self._id}_initialized\", False):\n            return\n\n        # Ensure data is a list of Data objects\n        data_list = self._validate_data(self.data)\n\n        # Store the initial data and context variables\n        self.update_ctx(\n            {\n                f\"{self._id}_data\": data_list,\n                f\"{self._id}_index\": 0,\n                f\"{self._id}_aggregated\": [],\n                f\"{self._id}_initialized\": True,\n            }\n        )\n\n    def _validate_data(self, data):\n        \"\"\"Validate and return a list of Data objects.\"\"\"\n        if isinstance(data, Data):\n            return [data]\n        if isinstance(data, list) and all(isinstance(item, Data) for item in data):\n            return data\n        msg = \"The 'data' input must be a list of Data objects or a single Data object.\"\n        raise TypeError(msg)\n\n    def evaluate_stop_loop(self) -> bool:\n        \"\"\"Evaluate whether to stop item or done output.\"\"\"\n        current_index = self.ctx.get(f\"{self._id}_index\", 0)\n        data_length = len(self.ctx.get(f\"{self._id}_data\", []))\n        return current_index > data_length\n\n    def item_output(self) -> Data:\n        \"\"\"Output the next item in the list or stop if done.\"\"\"\n        self.initialize_data()\n        current_item = Data(text=\"\")\n\n        if self.evaluate_stop_loop():\n            self.stop(\"item\")\n            return Data(text=\"\")\n\n        # Get data list and current index\n        data_list, current_index = self.loop_variables()\n        if current_index < len(data_list):\n            # Output current item and increment index\n            try:\n                current_item = data_list[current_index]\n            except IndexError:\n                current_item = Data(text=\"\")\n        self.aggregated_output()\n        self.update_ctx({f\"{self._id}_index\": current_index + 1})\n        return current_item\n\n    def done_output(self) -> Data:\n        \"\"\"Trigger the done output when iteration is complete.\"\"\"\n        self.initialize_data()\n\n        if self.evaluate_stop_loop():\n            self.stop(\"item\")\n            self.start(\"done\")\n\n            return self.ctx.get(f\"{self._id}_aggregated\", [])\n        self.stop(\"done\")\n        return Data(text=\"\")\n\n    def loop_variables(self):\n        \"\"\"Retrieve loop variables from context.\"\"\"\n        return (\n            self.ctx.get(f\"{self._id}_data\", []),\n            self.ctx.get(f\"{self._id}_index\", 0),\n        )\n\n    def aggregated_output(self) -> Data:\n        \"\"\"Return the aggregated list once all items are processed.\"\"\"\n        self.initialize_data()\n\n        # Get data list and aggregated list\n        data_list = self.ctx.get(f\"{self._id}_data\", [])\n        aggregated = self.ctx.get(f\"{self._id}_aggregated\", [])\n\n        # Check if loop input is provided and append to aggregated list\n        if self.item is not None and not isinstance(self.item, str) and len(aggregated) <= len(data_list):\n            aggregated.append(self.item)\n            self.update_ctx({f\"{self._id}_aggregated\": aggregated})\n        return aggregated\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}}, "description": "Iterates over a list of Data objects, outputting one item at a time and aggregating results from loop inputs.", "icon": "infinity", "base_classes": ["Data"], "display_name": "Loop", "documentation": "", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Data"], "selected": "Data", "name": "item", "display_name": "<PERSON><PERSON>", "method": "item_output", "value": "__UNDEFINED__", "cache": true, "allows_loop": true}, {"types": ["Data"], "selected": "Data", "name": "done", "display_name": "Done", "method": "done_output", "value": "__UNDEFINED__", "cache": true, "allows_loop": false}], "field_order": ["data"], "beta": false, "legacy": false, "edited": false, "metadata": {}, "tool_mode": false, "lf_version": "1.1.3"}, "showNode": true, "type": "LoopComponent", "id": "LoopComponent-qgNpM"}, "selected": false, "measured": {"width": 320, "height": 279}, "dragging": false}, {"id": "ParseData-lUiuT", "type": "genericNode", "position": {"x": -367.5404844774772, "y": 252.1300267679337}, "data": {"node": {"template": {"_type": "Component", "data": {"tool_mode": false, "trace_as_metadata": true, "list": true, "list_add_label": "Add More", "trace_as_input": true, "required": true, "placeholder": "", "show": true, "name": "data", "value": "", "display_name": "Data", "advanced": false, "input_types": ["Data"], "dynamic": false, "info": "The data to convert to text.", "title_case": false, "type": "other", "_input_type": "DataInput"}, "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.custom import Component\nfrom langflow.helpers.data import data_to_text, data_to_text_list\nfrom langflow.io import DataInput, MultilineInput, Output, StrInput\nfrom langflow.schema import Data\nfrom langflow.schema.message import Message\n\n\nclass ParseDataComponent(Component):\n    display_name = \"Data to Message\"\n    description = \"Convert Data objects into Messages using any {field_name} from input data.\"\n    icon = \"message-square\"\n    name = \"ParseData\"\n\n    inputs = [\n        DataInput(name=\"data\", display_name=\"Data\", info=\"The data to convert to text.\", is_list=True, required=True),\n        MultilineInput(\n            name=\"template\",\n            display_name=\"Template\",\n            info=\"The template to use for formatting the data. \"\n            \"It can contain the keys {text}, {data} or any other key in the Data.\",\n            value=\"{text}\",\n            required=True,\n        ),\n        StrInput(name=\"sep\", display_name=\"Separator\", advanced=True, value=\"\\n\"),\n    ]\n\n    outputs = [\n        Output(\n            display_name=\"Message\",\n            name=\"text\",\n            info=\"Data as a single Message, with each input Data separated by Separator\",\n            method=\"parse_data\",\n        ),\n        Output(\n            display_name=\"Data List\",\n            name=\"data_list\",\n            info=\"Data as a list of new Data, each having `text` formatted by Template\",\n            method=\"parse_data_as_list\",\n        ),\n    ]\n\n    def _clean_args(self) -> tuple[list[Data], str, str]:\n        data = self.data if isinstance(self.data, list) else [self.data]\n        template = self.template\n        sep = self.sep\n        return data, template, sep\n\n    def parse_data(self) -> Message:\n        data, template, sep = self._clean_args()\n        result_string = data_to_text(template, data, sep)\n        self.status = result_string\n        return Message(text=result_string)\n\n    def parse_data_as_list(self) -> list[Data]:\n        data, template, _ = self._clean_args()\n        text_list, data_list = data_to_text_list(template, data)\n        for item, text in zip(data_list, text_list, strict=True):\n            item.set_text(text)\n        self.status = data_list\n        return data_list\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "sep": {"tool_mode": false, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "sep", "value": "\n", "display_name": "Separator", "advanced": true, "dynamic": false, "info": "", "title_case": false, "type": "str", "_input_type": "StrInput"}, "template": {"tool_mode": false, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": true, "placeholder": "", "show": true, "name": "template", "value": "{prompt}", "display_name": "Template", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "The template to use for formatting the data. It can contain the keys {text}, {data} or any other key in the Data.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}}, "description": "Convert Data objects into Messages using any {field_name} from input data.", "icon": "message-square", "base_classes": ["Data", "Message"], "display_name": "Data to Message", "documentation": "", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Message"], "selected": "Message", "name": "text", "display_name": "Message", "method": "parse_data", "value": "__UNDEFINED__", "cache": true, "allows_loop": false}, {"types": ["Data"], "selected": "Data", "name": "data_list", "display_name": "Data List", "method": "parse_data_as_list", "value": "__UNDEFINED__", "cache": true, "allows_loop": false}], "field_order": ["data", "template", "sep"], "beta": false, "legacy": false, "edited": false, "metadata": {}, "tool_mode": false, "category": "processing", "key": "ParseData", "score": 0.008664293911514902, "lf_version": "1.1.3"}, "showNode": true, "type": "ParseData", "id": "ParseData-lUiuT"}, "selected": false, "measured": {"width": 320, "height": 341}, "dragging": false}, {"id": "MessagetoData-7TkrT", "type": "genericNode", "position": {"x": 1360.5035612125448, "y": 528.6285976147362}, "data": {"node": {"template": {"_type": "Component", "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from loguru import logger\n\nfrom langflow.custom import Component\nfrom langflow.io import MessageInput, Output\nfrom langflow.schema import Data\nfrom langflow.schema.message import Message\nimport torch\n\n\n\nclass MessageToDataComponent(Component):\n    display_name = \"Message to Data\"\n    description = \"Convert a Message object to a Data object\"\n    icon = \"message-square-share\"\n    beta = True\n    name = \"MessagetoData\"\n\n    inputs = [\n        MessageInput(\n            name=\"message\",\n            display_name=\"Message\",\n            info=\"The Message object to convert to a Data object\",\n        ),\n    ]\n\n    outputs = [\n        Output(display_name=\"Data\", name=\"data\", method=\"convert_message_to_data\"),\n    ]\n\n    def convert_message_to_data(self) -> Data:\n        torch.cuda.empty_cache()\n        if isinstance(self.message, Message):\n            # Convert Message to Data\n            return Data(data=self.message.data)\n\n        msg = \"Error converting Message to Data: Input must be a Message object\"\n        logger.opt(exception=True).debug(msg)\n        self.status = msg\n        return Data(data={\"error\": msg})\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "message": {"trace_as_input": true, "tool_mode": false, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "message", "value": "", "display_name": "Message", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "The Message object to convert to a Data object", "title_case": false, "type": "str", "_input_type": "MessageInput"}}, "description": "Convert a Message object to a Data object", "icon": "message-square-share", "base_classes": ["Data"], "display_name": "Message to Data", "documentation": "", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Data"], "selected": "Data", "name": "data", "hidden": null, "display_name": "Data", "method": "convert_message_to_data", "value": "__UNDEFINED__", "cache": true, "required_inputs": null, "allows_loop": false}], "field_order": ["message"], "beta": true, "legacy": false, "edited": true, "metadata": {}, "tool_mode": false, "lf_version": "1.1.3"}, "showNode": true, "type": "MessagetoData", "id": "MessagetoData-7TkrT"}, "selected": false, "measured": {"width": 320, "height": 229}, "dragging": false}, {"id": "ParseData-7NXif", "type": "genericNode", "position": {"x": 1853.9730945773026, "y": 444.97348265102994}, "data": {"node": {"template": {"_type": "Component", "data": {"tool_mode": false, "trace_as_metadata": true, "list": true, "list_add_label": "Add More", "trace_as_input": true, "required": true, "placeholder": "", "show": true, "name": "data", "value": "", "display_name": "Data", "advanced": false, "input_types": ["Data"], "dynamic": false, "info": "The data to convert to text.", "title_case": false, "type": "other", "_input_type": "DataInput"}, "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.custom import Component\nfrom langflow.helpers.data import data_to_text, data_to_text_list\nfrom langflow.io import DataInput, MultilineInput, Output, StrInput\nfrom langflow.schema import Data\nfrom langflow.schema.message import Message\n\n\nclass ParseDataComponent(Component):\n    display_name = \"Data to Message\"\n    description = \"Convert Data objects into Messages using any {field_name} from input data.\"\n    icon = \"message-square\"\n    name = \"ParseData\"\n\n    inputs = [\n        DataInput(name=\"data\", display_name=\"Data\", info=\"The data to convert to text.\", is_list=True, required=True),\n        MultilineInput(\n            name=\"template\",\n            display_name=\"Template\",\n            info=\"The template to use for formatting the data. \"\n            \"It can contain the keys {text}, {data} or any other key in the Data.\",\n            value=\"{text}\",\n            required=True,\n        ),\n        StrInput(name=\"sep\", display_name=\"Separator\", advanced=True, value=\"\\n\"),\n    ]\n\n    outputs = [\n        Output(\n            display_name=\"Message\",\n            name=\"text\",\n            info=\"Data as a single Message, with each input Data separated by Separator\",\n            method=\"parse_data\",\n        ),\n        Output(\n            display_name=\"Data List\",\n            name=\"data_list\",\n            info=\"Data as a list of new Data, each having `text` formatted by Template\",\n            method=\"parse_data_as_list\",\n        ),\n    ]\n\n    def _clean_args(self) -> tuple[list[Data], str, str]:\n        data = self.data if isinstance(self.data, list) else [self.data]\n        template = self.template\n        sep = self.sep\n        return data, template, sep\n\n    def parse_data(self) -> Message:\n        data, template, sep = self._clean_args()\n        result_string = data_to_text(template, data, sep)\n        self.status = result_string\n        return Message(text=result_string)\n\n    def parse_data_as_list(self) -> list[Data]:\n        data, template, _ = self._clean_args()\n        text_list, data_list = data_to_text_list(template, data)\n        for item, text in zip(data_list, text_list, strict=True):\n            item.set_text(text)\n        self.status = data_list\n        return data_list\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "sep": {"tool_mode": false, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "sep", "value": "\n", "display_name": "Separator", "advanced": true, "dynamic": false, "info": "", "title_case": false, "type": "str", "_input_type": "StrInput"}, "template": {"tool_mode": false, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": true, "placeholder": "", "show": true, "name": "template", "value": "{text}", "display_name": "Template", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "The template to use for formatting the data. It can contain the keys {text}, {data} or any other key in the Data.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}}, "description": "Convert Data objects into Messages using any {field_name} from input data.", "icon": "message-square", "base_classes": ["Data", "Message"], "display_name": "Data to Message", "documentation": "", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Message"], "selected": "Message", "name": "text", "display_name": "Message", "method": "parse_data", "value": "__UNDEFINED__", "cache": true, "allows_loop": false}, {"types": ["Data"], "selected": "Data", "name": "data_list", "display_name": "Data List", "method": "parse_data_as_list", "value": "__UNDEFINED__", "cache": true, "allows_loop": false}], "field_order": ["data", "template", "sep"], "beta": false, "legacy": false, "edited": false, "metadata": {}, "tool_mode": false, "lf_version": "1.1.3"}, "showNode": true, "type": "ParseData", "id": "ParseData-7NXif"}, "selected": false, "measured": {"width": 320, "height": 341}, "dragging": false}, {"id": "RunFlow-noxmK", "type": "genericNode", "position": {"x": 853.9147485895177, "y": 80.1265271001526}, "data": {"node": {"template": {"_type": "Component", "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from typing import Any\n\nfrom loguru import logger\n\nfrom langflow.base.tools.run_flow import RunFlow<PERSON><PERSON>Component\nfrom langflow.helpers.flow import run_flow\nfrom langflow.schema import dotdict\n\n\nclass RunFlowComponent(RunFlowBaseComponent):\n    display_name = \"Run Flow\"\n    description = \"Creates a tool component from a Flow that takes all its inputs and runs it.\"\n    beta = True\n    name = \"RunFlow\"\n    icon = \"Workflow\"\n\n    inputs = RunFlowBaseComponent._base_inputs\n    outputs = RunFlowBaseComponent._base_outputs\n\n    async def update_build_config(self, build_config: dotdict, field_value: Any, field_name: str | None = None):\n        if field_name == \"flow_name_selected\":\n            build_config[\"flow_name_selected\"][\"options\"] = await self.get_flow_names()\n            missing_keys = [key for key in self.default_keys if key not in build_config]\n            if missing_keys:\n                msg = f\"Missing required keys in build_config: {missing_keys}\"\n                raise ValueError(msg)\n            if field_value is not None:\n                try:\n                    graph = await self.get_graph(field_value)\n                    build_config = self.update_build_config_from_graph(build_config, graph)\n                except Exception as e:\n                    msg = f\"Error building graph for flow {field_value}\"\n                    logger.exception(msg)\n                    raise RuntimeError(msg) from e\n        return build_config\n\n    async def run_flow_with_tweaks(self):\n        tweaks: dict = {}\n\n        flow_name_selected = self._attributes.get(\"flow_name_selected\")\n        parsed_flow_tweak_data = self._attributes.get(\"flow_tweak_data\", {})\n        if not isinstance(parsed_flow_tweak_data, dict):\n            parsed_flow_tweak_data = parsed_flow_tweak_data.dict()\n\n        if parsed_flow_tweak_data != {}:\n            for field in parsed_flow_tweak_data:\n                if \"~\" in field:\n                    [node, name] = field.split(\"~\")\n                    if node not in tweaks:\n                        tweaks[node] = {}\n                    tweaks[node][name] = parsed_flow_tweak_data[field]\n        else:\n            for field in self._attributes:\n                if field not in self.default_keys and \"~\" in field:\n                    [node, name] = field.split(\"~\")\n                    if node not in tweaks:\n                        tweaks[node] = {}\n                    tweaks[node][name] = self._attributes[field]\n\n        return await run_flow(\n            inputs=None,\n            output_type=\"all\",\n            flow_id=None,\n            flow_name=flow_name_selected,\n            tweaks=tweaks,\n            user_id=str(self.user_id),\n            # run_id=self.graph.run_id,\n            session_id=self.graph.session_id or self.session_id,\n        )\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "flow_name_selected": {"tool_mode": false, "trace_as_metadata": true, "options": ["model_load", "model_inference", "model_fine_tune", "prompt_list", "hyperparameter_tuning"], "options_metadata": [], "combobox": false, "dialog_inputs": {}, "required": false, "placeholder": "", "show": true, "name": "flow_name_selected", "display_name": "Flow Name", "advanced": false, "dynamic": false, "info": "The name of the flow to run.", "real_time_refresh": true, "refresh_button": true, "title_case": false, "type": "str", "_input_type": "DropdownInput", "value": "model_inference"}, "session_id": {"trace_as_input": true, "tool_mode": false, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "session_id", "value": {"text_key": "text", "data": {"text": "", "files": [], "timestamp": "2025-03-17 14:43:54 UTC"}, "default_value": ""}, "display_name": "Session ID", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "The session ID to run the flow in.", "title_case": false, "type": "str", "_input_type": "MessageInput"}, "TextInput-yOcIj~input_value": {"tool_mode": true, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "TextInput-yOcIj~input_value", "value": " Your task is to determine whether the patient described in the note have depression or not  \n **Output Instructions:** provide your final answer (only the number \"1\" or \"0\") in this format **The final answer is: {{ 0 or 1}}**.      \n---    \n** Patient Note Summary:**   \n  {}    \n___\n Output the result (1 for depression present, or 0 otherwise) in this format **The final answer is: **.", "display_name": "Prompt - Text", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Text to be passed as input.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}, "TextInput-Hp49c~input_text": {"tool_mode": true, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "TextInput-Hp49c~input_text", "value": "datasets/train_test_split/x_test.csv", "display_name": "Patient Note - Input Text", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Enter the text to be processed.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}, "TextInput-iJHmK~input_value": {"tool_mode": true, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "TextInput-iJHmK~input_value", "value": "datasets/train_test_split/y_test.csv", "display_name": "Depression labels - Text", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Text to be passed as input.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}, "TextInput-gvImP~input_value": {"tool_mode": true, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "TextInput-gvImP~input_value", "value": "finetuned_model/models/unsloth/Meta-Llama-3.1-8B-Instruct-bnb-4bit", "display_name": "ft_model_path - Text", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Text to be passed as input.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}}, "description": "Creates a tool component from a Flow that takes all its inputs and runs it.", "icon": "Workflow", "base_classes": ["Data", "DataFrame", "Message"], "display_name": "Run Flow", "documentation": "", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Data"], "selected": "Data", "name": "flow_outputs_data", "hidden": true, "display_name": "Flow Data Output", "method": "data_output", "value": "__UNDEFINED__", "cache": true, "required_inputs": [], "allows_loop": false}, {"types": ["DataFrame"], "selected": "DataFrame", "name": "flow_outputs_dataframe", "hidden": true, "display_name": "Flow Dataframe Output", "method": "dataframe_output", "value": "__UNDEFINED__", "cache": true, "required_inputs": [], "allows_loop": false}, {"types": ["Message"], "selected": "Message", "name": "flow_outputs_message", "hidden": null, "display_name": "Flow Message Output", "method": "message_output", "value": "__UNDEFINED__", "cache": true, "required_inputs": [], "allows_loop": false}], "field_order": ["flow_name_selected", "session_id"], "beta": true, "legacy": false, "edited": false, "metadata": {}, "tool_mode": false}, "showNode": true, "type": "RunFlow", "id": "RunFlow-noxmK"}, "selected": true, "measured": {"width": 320, "height": 578}, "dragging": false}], "edges": [{"source": "RunFlow-IinJF", "sourceHandle": "{œdataTypeœ:œRunFlowœ,œidœ:œRunFlow-IinJFœ,œnameœ:œflow_outputs_messageœ,œoutput_typesœ:[œMessageœ]}", "target": "RunFlow-NHJUX", "targetHandle": "{œfieldNameœ:œTextInput-DBkbt~input_valueœ,œidœ:œRunFlow-NHJUXœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "data": {"targetHandle": {"fieldName": "TextInput-DBkbt~input_value", "id": "RunFlow-NHJUX", "inputTypes": ["Message"], "type": "str"}, "sourceHandle": {"dataType": "RunFlow", "id": "RunFlow-IinJF", "name": "flow_outputs_message", "output_types": ["Message"]}}, "id": "xy-edge__RunFlow-IinJF{œdataTypeœ:œRunFlowœ,œidœ:œRunFlow-IinJFœ,œnameœ:œflow_outputs_messageœ,œoutput_typesœ:[œMessageœ]}-RunFlow-NHJUX{œfieldNameœ:œTextInput-DBkbt~input_valueœ,œidœ:œRunFlow-NHJUXœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "className": "", "animated": false}, {"source": "TextInput-UX4dS", "sourceHandle": "{œdataTypeœ:œpromptsœ,œidœ:œTextInput-UX4dSœ,œnameœ:œtextœ,œoutput_typesœ:[œDataœ]}", "target": "LoopComponent-qgNpM", "targetHandle": "{œfieldNameœ:œdataœ,œidœ:œLoopComponent-qgNpMœ,œinputTypesœ:[œDataœ],œtypeœ:œotherœ}", "data": {"targetHandle": {"fieldName": "data", "id": "LoopComponent-qgNpM", "inputTypes": ["Data"], "type": "other"}, "sourceHandle": {"dataType": "prompts", "id": "TextInput-UX4dS", "name": "text", "output_types": ["Data"]}}, "id": "xy-edge__TextInput-UX4dS{œdataTypeœ:œpromptsœ,œidœ:œTextInput-UX4dSœ,œnameœ:œtextœ,œoutput_typesœ:[œDataœ]}-LoopComponent-qgNpM{œfieldNameœ:œdataœ,œidœ:œLoopComponent-qgNpMœ,œinputTypesœ:[œDataœ],œtypeœ:œotherœ}", "animated": false, "className": ""}, {"source": "LoopComponent-qgNpM", "sourceHandle": "{œdataTypeœ:œLoopComponentœ,œidœ:œLoopComponent-qgNpMœ,œnameœ:œitemœ,œoutput_typesœ:[œDataœ]}", "target": "ParseData-lUiuT", "targetHandle": "{œfieldNameœ:œdataœ,œidœ:œParseData-lUiuTœ,œinputTypesœ:[œDataœ],œtypeœ:œotherœ}", "data": {"targetHandle": {"fieldName": "data", "id": "ParseData-lUiuT", "inputTypes": ["Data"], "type": "other"}, "sourceHandle": {"dataType": "LoopComponent", "id": "LoopComponent-qgNpM", "name": "item", "output_types": ["Data"]}}, "id": "xy-edge__LoopComponent-qgNpM{œdataTypeœ:œLoopComponentœ,œidœ:œLoopComponent-qgNpMœ,œnameœ:œitemœ,œoutput_typesœ:[œDataœ]}-ParseData-lUiuT{œfieldNameœ:œdataœ,œidœ:œParseData-lUiuTœ,œinputTypesœ:[œDataœ],œtypeœ:œotherœ}", "animated": false, "className": ""}, {"source": "MessagetoData-7TkrT", "sourceHandle": "{œdataTypeœ:œMessagetoDataœ,œidœ:œMessagetoData-7TkrTœ,œnameœ:œdataœ,œoutput_typesœ:[œDataœ]}", "target": "LoopComponent-qgNpM", "targetHandle": "{œdataTypeœ:œLoopComponentœ,œidœ:œLoopComponent-qgNpMœ,œnameœ:œitemœ,œoutput_typesœ:[œDataœ]}", "data": {"targetHandle": {"dataType": "LoopComponent", "id": "LoopComponent-qgNpM", "name": "item", "output_types": ["Data"]}, "sourceHandle": {"dataType": "MessagetoData", "id": "MessagetoData-7TkrT", "name": "data", "output_types": ["Data"]}}, "id": "xy-edge__MessagetoData-7TkrT{œdataTypeœ:œMessagetoDataœ,œidœ:œMessagetoData-7TkrTœ,œnameœ:œdataœ,œoutput_typesœ:[œDataœ]}-LoopComponent-qgNpM{œdataTypeœ:œLoopComponentœ,œidœ:œLoopComponent-qgNpMœ,œnameœ:œitemœ,œoutput_typesœ:[œDataœ]}", "animated": false, "className": "", "selected": false}, {"source": "LoopComponent-qgNpM", "sourceHandle": "{œdataTypeœ:œLoopComponentœ,œidœ:œLoopComponent-qgNpMœ,œnameœ:œdoneœ,œoutput_typesœ:[œDataœ]}", "target": "ParseData-7NXif", "targetHandle": "{œfieldNameœ:œdataœ,œidœ:œParseData-7NXifœ,œinputTypesœ:[œDataœ],œtypeœ:œotherœ}", "data": {"targetHandle": {"fieldName": "data", "id": "ParseData-7NXif", "inputTypes": ["Data"], "type": "other"}, "sourceHandle": {"dataType": "LoopComponent", "id": "LoopComponent-qgNpM", "name": "done", "output_types": ["Data"]}}, "id": "xy-edge__LoopComponent-qgNpM{œdataTypeœ:œLoopComponentœ,œidœ:œLoopComponent-qgNpMœ,œnameœ:œdoneœ,œoutput_typesœ:[œDataœ]}-ParseData-7NXif{œfieldNameœ:œdataœ,œidœ:œParseData-7NXifœ,œinputTypesœ:[œDataœ],œtypeœ:œotherœ}", "animated": false, "className": ""}, {"source": "ParseData-lUiuT", "sourceHandle": "{œdataTypeœ:œParseDataœ,œidœ:œParseData-lUiuTœ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}", "target": "RunFlow-NHJUX", "targetHandle": "{œfieldNameœ:œTextInput-6u1pS~input_valueœ,œidœ:œRunFlow-NHJUXœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "data": {"targetHandle": {"fieldName": "TextInput-6u1pS~input_value", "id": "RunFlow-NHJUX", "inputTypes": ["Message"], "type": "str"}, "sourceHandle": {"dataType": "ParseData", "id": "ParseData-lUiuT", "name": "text", "output_types": ["Message"]}}, "id": "xy-edge__ParseData-lUiuT{œdataTypeœ:œParseDataœ,œidœ:œParseData-lUiuTœ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}-RunFlow-NHJUX{œfieldNameœ:œTextInput-6u1pS~input_valueœ,œidœ:œRunFlow-NHJUXœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "className": "", "animated": false}, {"source": "RunFlow-NHJUX", "sourceHandle": "{œdataTypeœ:œRunFlowœ,œidœ:œRunFlow-NHJUXœ,œnameœ:œflow_outputs_messageœ,œoutput_typesœ:[œMessageœ]}", "target": "RunFlow-noxmK", "targetHandle": "{œfieldNameœ:œTextInput-gvImP~input_valueœ,œidœ:œRunFlow-noxmKœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "data": {"targetHandle": {"fieldName": "TextInput-gvImP~input_value", "id": "RunFlow-noxmK", "inputTypes": ["Message"], "type": "str"}, "sourceHandle": {"dataType": "RunFlow", "id": "RunFlow-NHJUX", "name": "flow_outputs_message", "output_types": ["Message"]}}, "id": "xy-edge__RunFlow-NHJUX{œdataTypeœ:œRunFlowœ,œidœ:œRunFlow-NHJUXœ,œnameœ:œflow_outputs_messageœ,œoutput_typesœ:[œMessageœ]}-RunFlow-noxmK{œfieldNameœ:œTextInput-gvImP~input_valueœ,œidœ:œRunFlow-noxmKœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "className": ""}, {"source": "RunFlow-noxmK", "sourceHandle": "{œdataTypeœ:œRunFlowœ,œidœ:œRunFlow-noxmKœ,œnameœ:œflow_outputs_messageœ,œoutput_typesœ:[œMessageœ]}", "target": "MessagetoData-7TkrT", "targetHandle": "{œfieldNameœ:œmessageœ,œidœ:œMessagetoData-7TkrTœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "data": {"targetHandle": {"fieldName": "message", "id": "MessagetoData-7TkrT", "inputTypes": ["Message"], "type": "str"}, "sourceHandle": {"dataType": "RunFlow", "id": "RunFlow-noxmK", "name": "flow_outputs_message", "output_types": ["Message"]}}, "id": "xy-edge__RunFlow-noxmK{œdataTypeœ:œRunFlowœ,œidœ:œRunFlow-noxmKœ,œnameœ:œflow_outputs_messageœ,œoutput_typesœ:[œMessageœ]}-MessagetoData-7TkrT{œfieldNameœ:œmessageœ,œidœ:œMessagetoData-7TkrTœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "className": ""}, {"source": "ParseData-lUiuT", "sourceHandle": "{œdataTypeœ:œParseDataœ,œidœ:œParseData-lUiuTœ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}", "target": "RunFlow-noxmK", "targetHandle": "{œfieldNameœ:œTextInput-yOcIj~input_valueœ,œidœ:œRunFlow-noxmKœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "data": {"targetHandle": {"fieldName": "TextInput-yOcIj~input_value", "id": "RunFlow-noxmK", "inputTypes": ["Message"], "type": "str"}, "sourceHandle": {"dataType": "ParseData", "id": "ParseData-lUiuT", "name": "text", "output_types": ["Message"]}}, "id": "xy-edge__ParseData-lUiuT{œdataTypeœ:œParseDataœ,œidœ:œParseData-lUiuTœ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}-RunFlow-noxmK{œfieldNameœ:œTextInput-yOcIj~input_valueœ,œidœ:œRunFlow-noxmKœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "className": ""}], "viewport": {"x": 300.80107152905714, "y": 25.50661208265865, "zoom": 0.8264574732679625}}}