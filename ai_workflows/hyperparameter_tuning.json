{"updated_at": "2025-03-12T22:17:28+00:00", "description": "This flow is designed for hyperparameter search, where it generates random values for the specified configuration of parameters over a defined number of generations. It evaluates these parameter sets and provides corresponding accuracies", "webhook": false, "icon": null, "locked": false, "name": "hyperparameter_tuning", "endpoint_name": null, "tags": null, "gradient": null, "id": "8c6d12fc-77c0-47f8-bc60-55d8400ea99c", "is_component": false, "user_id": "938fff2c-f0ff-4de8-8853-b8825c3f76ea", "folder_id": "66998fec-d310-4886-89a0-35a27eab4da1", "icon_bg_color": null, "data": {"nodes": [{"id": "CustomComponent-rtPYG", "type": "genericNode", "position": {"x": 156.90636019941184, "y": -960.8574495835617}, "data": {"node": {"template": {"_type": "Component", "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.field_typing import Data\nfrom langflow.custom import Component\nfrom langflow.io import MessageTextInput, Output\nfrom langflow.schema import Data\nimport random\nfrom typing import List\n\n\nclass CustomComponent(Component):\n    \"\"\"\n    A custom component to generate random configurations for model fine-tuning.\n    The component generates a specified number of random hyperparameter configurations\n    based on the provided template.\n    \"\"\"\n\n    display_name = \"parameter_generation\"\n    description = \"Generate random configurations for model fine-tuning\"\n    documentation: str = \"http://docs.langflow.org/components/custom\"\n    icon = \"code\"\n    name = \"CustomComponent\"\n\n    # Define input fields for the component\n    inputs = [\n        MessageTextInput(\n            name=\"num_generations\",\n            display_name=\"Number of Generations\",\n            info=\"Number of times to generate random configurations for fine-tuning.\",\n            value=\"5\",\n            tool_mode=True,\n        ),\n        MessageTextInput(\n            name=\"config_template\",\n            display_name=\"Configuration Template\",\n            info=\"Template for generating random hyperparameter configurations.\",\n            value=\"\"\"{\n                \"learning_rate\": 10 ** random.uniform(-5, -3),\n                \"gradient_accumulation_steps\": random.choice([2, 4, 8, 16]),\n                \"epochs\": random.randint(1, 3),\n                \"weight_decay\": random.uniform(0.0, 0.2),\n                \"warmup_ratio\": random.uniform(0.0, 0.2),\n            }\"\"\",\n            tool_mode=True,\n        ),\n    ]\n\n    # Define output fields for the component\n    outputs = [\n        Output(display_name=\"Output\", name=\"output\", method=\"build_output\"),\n    ]\n\n    def build_output(self) -> List[Data]:\n        \"\"\"\n        Generates a list of random hyperparameter configurations based on the provided template.\n\n        Returns:\n            List[Data]: A list of Data objects, each containing a randomly generated configuration.\n        \"\"\"\n        configurations = []\n        for _ in range(int(self.num_generations)):\n            # Evaluate the config template to generate a new set of random values\n            config_values = eval(self.config_template)\n            configurations.append(Data(data={\"text\": config_values}))\n\n        return configurations", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "config_template": {"tool_mode": true, "trace_as_input": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "config_template", "value": "{\n                \"learning_rate\": 10 ** random.uniform(-5, -3),\n                \"gradient_accumulation_steps\": random.choice([2, 4, 8, 16]),\n                \"epochs\": random.randint(1, 3),\n                \"weight_decay\": random.uniform(0.0, 0.2),\n                \"warmup_ratio\": random.uniform(0.0, 0.2),\n            }", "display_name": "Configuration Template", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Template for generating random hyperparameter configurations.", "title_case": false, "type": "str", "_input_type": "MessageTextInput"}, "num_generations": {"tool_mode": true, "trace_as_input": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "num_generations", "value": "3", "display_name": "Number of Generations", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Number of times to generate random configurations for fine-tuning.", "title_case": false, "type": "str", "_input_type": "MessageTextInput"}}, "description": "Generate random configurations for model fine-tuning", "icon": "code", "base_classes": ["Data"], "display_name": "parameter_generation", "documentation": "http://docs.langflow.org/components/custom", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Data"], "selected": "Data", "name": "output", "hidden": null, "display_name": "Output", "method": "build_output", "value": "__UNDEFINED__", "cache": true, "required_inputs": null, "allows_loop": false}], "field_order": ["num_generations", "config_template"], "beta": false, "legacy": false, "edited": true, "metadata": {}, "tool_mode": false}, "showNode": true, "type": "CustomComponent", "id": "CustomComponent-rtPYG"}, "selected": false, "measured": {"width": 320, "height": 332}, "dragging": false}, {"id": "ParseData-8gINE", "type": "genericNode", "position": {"x": 1034.2382994403813, "y": -1039.4963736383193}, "data": {"node": {"template": {"_type": "Component", "data": {"tool_mode": false, "trace_as_metadata": true, "list": true, "list_add_label": "Add More", "trace_as_input": true, "required": true, "placeholder": "", "show": true, "name": "data", "value": "", "display_name": "Data", "advanced": false, "input_types": ["Data"], "dynamic": false, "info": "The data to convert to text.", "title_case": false, "type": "other", "_input_type": "DataInput"}, "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.custom import Component\nfrom langflow.helpers.data import data_to_text, data_to_text_list\nfrom langflow.io import DataInput, MultilineInput, Output, StrInput\nfrom langflow.schema import Data\nfrom langflow.schema.message import Message\n\nimport random\n\nclass ParseDataComponent(Component):\n    display_name = \"Data to Message\"\n    description = \"Convert Data objects into Messages using any {field_name} from input data.\"\n    icon = \"message-square\"\n    name = \"ParseData\"\n\n    inputs = [\n        DataInput(name=\"data\", display_name=\"Data\", info=\"The data to convert to text.\", is_list=True, required=True),\n        MultilineInput(\n            name=\"template\",\n            display_name=\"Template\",\n            info=\"The template to use for formatting the data. \"\n            \"It can contain the keys {text}, {data} or any other key in the Data.\",\n            value=\"{text}\",\n            required=True,\n        ),\n        StrInput(name=\"sep\", display_name=\"Separator\", advanced=True, value=\"\\n\"),\n    ]\n\n    outputs = [\n        Output(\n            display_name=\"Message\",\n            name=\"text\",\n            info=\"Data as a single Message, with each input Data separated by Separator\",\n            method=\"parse_data\",\n        ),\n        Output(\n            display_name=\"Data List\",\n            name=\"data_list\",\n            info=\"Data as a list of new Data, each having `text` formatted by Template\",\n            method=\"parse_data_as_list\",\n        ),\n    ]\n\n    def _clean_args(self) -> tuple[list[Data], str, str]:\n        data = self.data if isinstance(self.data, list) else [self.data]\n        template = self.template\n        sep = self.sep\n        return data, template, sep\n\n    def parse_data(self) -> Message:\n        data, template, sep = self._clean_args()\n        result_string = data_to_text(template, data, sep)\n        result_string = str(eval(result_string))\n        self.status = result_string\n        return Message(text=result_string)\n\n    def parse_data_as_list(self) -> list[Data]:\n        data, template, _ = self._clean_args()\n        text_list, data_list = data_to_text_list(template, data)\n        for item, text in zip(data_list, text_list, strict=True):\n            item.set_text(text)\n        self.status = data_list\n        return data_list\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "sep": {"tool_mode": false, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "sep", "value": "\n", "display_name": "Separator", "advanced": true, "dynamic": false, "info": "", "title_case": false, "type": "str", "_input_type": "StrInput"}, "template": {"tool_mode": false, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": true, "placeholder": "", "show": true, "name": "template", "value": "{text}", "display_name": "Template", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "The template to use for formatting the data. It can contain the keys {text}, {data} or any other key in the Data.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}}, "description": "Convert Data objects into Messages using any {field_name} from input data.", "icon": "message-square", "base_classes": ["Data", "Message"], "display_name": "Data to Message", "documentation": "", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Message"], "selected": "Message", "name": "text", "hidden": null, "display_name": "Message", "method": "parse_data", "value": "__UNDEFINED__", "cache": true, "required_inputs": null, "allows_loop": false}, {"types": ["Data"], "selected": "Data", "name": "data_list", "hidden": null, "display_name": "Data List", "method": "parse_data_as_list", "value": "__UNDEFINED__", "cache": true, "required_inputs": null, "allows_loop": false}], "field_order": ["data", "template", "sep"], "beta": false, "legacy": false, "edited": true, "metadata": {}, "tool_mode": false, "lf_version": "1.1.3"}, "showNode": true, "type": "ParseData", "id": "ParseData-8gINE"}, "selected": false, "measured": {"width": 320, "height": 341}, "dragging": false}, {"id": "ParseData-wam5V", "type": "genericNode", "position": {"x": 1464.8198897999473, "y": -446.7772504057905}, "data": {"node": {"template": {"_type": "Component", "data": {"tool_mode": false, "trace_as_metadata": true, "list": true, "list_add_label": "Add More", "trace_as_input": true, "required": true, "placeholder": "", "show": true, "name": "data", "value": "", "display_name": "Data", "advanced": false, "input_types": ["Data"], "dynamic": false, "info": "The data to convert to text.", "title_case": false, "type": "other", "_input_type": "DataInput"}, "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.custom import Component\nfrom langflow.helpers.data import data_to_text, data_to_text_list\nfrom langflow.io import DataInput, MultilineInput, Output, StrInput\nfrom langflow.schema import Data\nfrom langflow.schema.message import Message\n\n\nclass ParseDataComponent(Component):\n    display_name = \"Best config\"\n    description = \"Convert Data objects into Messages using any {field_name} from input data.\"\n    icon = \"message-square\"\n    name = \"ParseData\"\n\n    inputs = [\n        DataInput(name=\"data\", display_name=\"Data\", info=\"The data to convert to text.\", is_list=True, required=True),\n        MultilineInput(\n            name=\"template\",\n            display_name=\"Template\",\n            info=\"The template to use for formatting the data. \"\n            \"It can contain the keys {text}, {data} or any other key in the Data.\",\n            value=\"{text}\",\n            required=True,\n        ),\n    ]\n\n    outputs = [\n        Output(\n            display_name=\"Data\",\n            name=\"text\",\n            info=\"Data as a single Message, with each input Data separated by Separator\",\n            method=\"parse_data\",\n        ),\n      \n    ]\n\n    def parse_data(self) -> Data:\n        data = self.data\n        best_accuray = 0\n        best_config = None\n        for i in range(len(data)):\n            c = data[i]\n            acc = float(c.accuracy)\n            if acc >= best_accuray:\n                best_accuray = acc\n                best_config = c.config\n                \n        d = Data(data={\n            \"best_accuray\": best_accuray,\n            \"best_config\": best_config\n        })\n            \n        return d\n\n \n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "template": {"tool_mode": false, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": true, "placeholder": "", "show": true, "name": "template", "value": "{accuracy}", "display_name": "Template", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "The template to use for formatting the data. It can contain the keys {text}, {data} or any other key in the Data.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}}, "description": "Convert Data objects into Messages using any {field_name} from input data.", "icon": "message-square", "base_classes": ["Data"], "display_name": "Best config", "documentation": "", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Data"], "selected": "Data", "name": "text", "hidden": null, "display_name": "Data", "method": "parse_data", "value": "__UNDEFINED__", "cache": true, "required_inputs": null, "allows_loop": false}], "field_order": ["data", "template"], "beta": false, "legacy": false, "edited": true, "metadata": {}, "tool_mode": false, "lf_version": "1.1.3"}, "showNode": true, "type": "ParseData", "id": "ParseData-wam5V"}, "selected": false, "measured": {"width": 320, "height": 293}, "dragging": false}, {"id": "CustomComponent-Qiuq3", "type": "genericNode", "position": {"x": 947.4350753325059, "y": -410.77168851776}, "data": {"node": {"template": {"_type": "Component", "accuracy": {"tool_mode": false, "trace_as_metadata": true, "list": true, "list_add_label": "Add More", "trace_as_input": true, "required": false, "placeholder": "", "show": true, "name": "accuracy", "value": "", "display_name": "accuracy", "advanced": false, "input_types": ["Data"], "dynamic": false, "info": "", "title_case": false, "type": "other", "_input_type": "DataInput"}, "config": {"tool_mode": false, "trace_as_metadata": true, "list": true, "list_add_label": "Add More", "trace_as_input": true, "required": false, "placeholder": "", "show": true, "name": "config", "value": "", "display_name": "config", "advanced": false, "input_types": ["Data"], "dynamic": false, "info": "", "title_case": false, "type": "other", "_input_type": "DataInput"}, "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "# from langflow.field_typing import Data\nfrom langflow.custom import Component\nfrom langflow.io import MessageTextInput, Output, DataInput\nfrom langflow.schema import Data\n\n\nclass CustomComponent(Component):\n    display_name = \"Custom Component\"\n    description = \"Use as a template to create your own component.\"\n    documentation: str = \"http://docs.langflow.org/components/custom\"\n    icon = \"code\"\n    name = \"CustomComponent\"\n\n    inputs = [\n        DataInput(\n            name=\"accuracy\",\n            display_name=\"accuracy\",\n            is_list=True),\n        \n        DataInput(\n            name=\"config\",\n            display_name=\"config\",\n            is_list=True\n        ),\n    ]\n\n    outputs = [\n        Output(display_name=\"Output\", name=\"output\", method=\"build_output\"),\n    ]\n\n    def build_output(self) -> Data:\n        best_accuray = 0\n        best_config = None\n        for i in range(len(self.accuracy)):\n            print(f\"Best config: {self.config[i].text}\")\n            acc = float(self.accuracy[i].text)\n            if acc >= best_accuray:\n                best_accuray = acc\n                best_config = self.config[i].text\n    \n        data_dict = {\n            \"config\": best_config,\n            \"accuracy\": best_accuray\n        }\n        data = Data(data=data_dict)\n        return data\n\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}}, "description": "Use as a template to create your own component.", "icon": "code", "base_classes": ["Data"], "display_name": "Custom Component", "documentation": "http://docs.langflow.org/components/custom", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Data"], "selected": "Data", "name": "output", "hidden": null, "display_name": "Output", "method": "build_output", "value": "__UNDEFINED__", "cache": true, "required_inputs": null, "allows_loop": false}], "field_order": ["accuracy", "config"], "beta": false, "legacy": false, "edited": true, "metadata": {}, "tool_mode": false, "lf_version": "1.1.3"}, "showNode": true, "type": "CustomComponent", "id": "CustomComponent-Qiuq3"}, "selected": false, "measured": {"width": 320, "height": 255}, "dragging": false}, {"id": "ParseData-HCIAt", "type": "genericNode", "position": {"x": 1476.846738709685, "y": -37.17172155027098}, "data": {"node": {"template": {"_type": "Component", "data": {"tool_mode": false, "trace_as_metadata": true, "list": true, "list_add_label": "Add More", "trace_as_input": true, "required": true, "placeholder": "", "show": true, "name": "data", "value": "", "display_name": "Data", "advanced": false, "input_types": ["Data"], "dynamic": false, "info": "The data to convert to text.", "title_case": false, "type": "other", "_input_type": "DataInput"}, "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.custom import Component\nfrom langflow.helpers.data import data_to_text, data_to_text_list\nfrom langflow.io import DataInput, MultilineInput, Output, StrInput\nfrom langflow.schema import Data\nfrom langflow.schema.message import Message\n\n\nclass ParseDataComponent(Component):\n    display_name = \"Data to Message\"\n    description = \"Convert Data objects into Messages using any {field_name} from input data.\"\n    icon = \"message-square\"\n    name = \"ParseData\"\n\n    inputs = [\n        DataInput(name=\"data\", display_name=\"Data\", info=\"The data to convert to text.\", is_list=True, required=True),\n        MultilineInput(\n            name=\"template\",\n            display_name=\"Template\",\n            info=\"The template to use for formatting the data. \"\n            \"It can contain the keys {text}, {data} or any other key in the Data.\",\n            value=\"{text}\",\n            required=True,\n        ),\n        StrInput(name=\"sep\", display_name=\"Separator\", advanced=True, value=\"\\n\"),\n    ]\n\n    outputs = [\n        Output(\n            display_name=\"Message\",\n            name=\"text\",\n            info=\"Data as a single Message, with each input Data separated by Separator\",\n            method=\"parse_data\",\n        ),\n        Output(\n            display_name=\"Data List\",\n            name=\"data_list\",\n            info=\"Data as a list of new Data, each having `text` formatted by Template\",\n            method=\"parse_data_as_list\",\n        ),\n    ]\n\n    def _clean_args(self) -> tuple[list[Data], str, str]:\n        data = self.data if isinstance(self.data, list) else [self.data]\n        template = self.template\n        sep = self.sep\n        return data, template, sep\n\n    def parse_data(self) -> Message:\n        data, template, sep = self._clean_args()\n        result_string = data_to_text(template, data, sep)\n        self.status = result_string\n        return Message(text=result_string)\n\n    def parse_data_as_list(self) -> list[Data]:\n        data, template, _ = self._clean_args()\n        text_list, data_list = data_to_text_list(template, data)\n        for item, text in zip(data_list, text_list, strict=True):\n            item.set_text(text)\n        self.status = data_list\n        return data_list\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "sep": {"tool_mode": false, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "sep", "value": "\n", "display_name": "Separator", "advanced": true, "dynamic": false, "info": "", "title_case": false, "type": "str", "_input_type": "StrInput"}, "template": {"tool_mode": false, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": true, "placeholder": "", "show": true, "name": "template", "value": "{config}", "display_name": "Template", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "The template to use for formatting the data. It can contain the keys {text}, {data} or any other key in the Data.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}}, "description": "Convert Data objects into Messages using any {field_name} from input data.", "icon": "message-square", "base_classes": ["Data", "Message"], "display_name": "Data to Message", "documentation": "", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Message"], "selected": "Message", "name": "text", "display_name": "Message", "method": "parse_data", "value": "__UNDEFINED__", "cache": true, "allows_loop": false}, {"types": ["Data"], "selected": "Data", "name": "data_list", "display_name": "Data List", "method": "parse_data_as_list", "value": "__UNDEFINED__", "cache": true, "allows_loop": false}], "field_order": ["data", "template", "sep"], "beta": false, "legacy": false, "edited": false, "metadata": {}, "tool_mode": false, "category": "processing", "key": "ParseData", "score": 0.01857804455091699, "lf_version": "1.1.3"}, "showNode": true, "type": "ParseData", "id": "ParseData-HCIAt"}, "selected": false, "measured": {"width": 320, "height": 341}, "dragging": false}, {"id": "MessagetoData-n96f0", "type": "genericNode", "position": {"x": 2325, "y": -1020}, "data": {"node": {"template": {"_type": "Component", "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from loguru import logger\n\nfrom langflow.custom import Component\nfrom langflow.io import MessageInput, Output\nfrom langflow.schema import Data\nfrom langflow.schema.message import Message\n\n\nclass MessageToDataComponent(Component):\n    display_name = \"Message to Data\"\n    description = \"Convert a Message object to a Data object\"\n    icon = \"message-square-share\"\n    beta = True\n    name = \"MessagetoData\"\n\n    inputs = [\n        MessageInput(\n            name=\"message\",\n            display_name=\"Message\",\n            info=\"The Message object to convert to a Data object\",\n        ),\n    ]\n\n    outputs = [\n        Output(display_name=\"Data\", name=\"data\", method=\"convert_message_to_data\"),\n    ]\n\n    def convert_message_to_data(self) -> Data:\n        if isinstance(self.message, Message):\n            # Convert Message to Data\n            return Data(data=self.message.data)\n\n        msg = \"Error converting Message to Data: Input must be a Message object\"\n        logger.opt(exception=True).debug(msg)\n        self.status = msg\n        return Data(data={\"error\": msg})\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "message": {"trace_as_input": true, "tool_mode": false, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "message", "value": "", "display_name": "Message", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "The Message object to convert to a Data object", "title_case": false, "type": "str", "_input_type": "MessageInput"}}, "description": "Convert a Message object to a Data object", "icon": "message-square-share", "base_classes": ["Data"], "display_name": "Message to Data", "documentation": "", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Data"], "selected": "Data", "name": "data", "display_name": "Data", "method": "convert_message_to_data", "value": "__UNDEFINED__", "cache": true, "allows_loop": false}], "field_order": ["message"], "beta": true, "legacy": false, "edited": false, "metadata": {}, "tool_mode": false, "category": "processing", "key": "MessagetoData", "score": 0.008222426499470714, "lf_version": "1.1.3"}, "showNode": true, "type": "MessagetoData", "id": "MessagetoData-n96f0"}, "selected": false, "measured": {"width": 320, "height": 229}}, {"id": "LoopComponent-mbKDL", "type": "genericNode", "position": {"x": 596.8486822821333, "y": -988.1663533562189}, "data": {"node": {"template": {"_type": "Component", "data": {"tool_mode": false, "trace_as_metadata": true, "list": false, "list_add_label": "Add More", "trace_as_input": true, "required": false, "placeholder": "", "show": true, "name": "data", "value": "", "display_name": "Data", "advanced": false, "input_types": ["Data"], "dynamic": false, "info": "The initial list of Data objects to iterate over.", "title_case": false, "type": "other", "_input_type": "DataInput"}, "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.custom import Component\nfrom langflow.io import DataInput, Output\nfrom langflow.schema import Data\n\n\nclass LoopComponent(Component):\n    display_name = \"Loop\"\n    description = (\n        \"Iterates over a list of Data objects, outputting one item at a time and aggregating results from loop inputs.\"\n    )\n    icon = \"infinity\"\n\n    inputs = [\n        DataInput(\n            name=\"data\",\n            display_name=\"Data\",\n            info=\"The initial list of Data objects to iterate over.\",\n        ),\n    ]\n\n    outputs = [\n        Output(display_name=\"Item\", name=\"item\", method=\"item_output\", allows_loop=True),\n        Output(display_name=\"Done\", name=\"done\", method=\"done_output\"),\n    ]\n\n    def initialize_data(self) -> None:\n        \"\"\"Initialize the data list, context index, and aggregated list.\"\"\"\n        if self.ctx.get(f\"{self._id}_initialized\", False):\n            return\n\n        # Ensure data is a list of Data objects\n        data_list = self._validate_data(self.data)\n\n        # Store the initial data and context variables\n        self.update_ctx(\n            {\n                f\"{self._id}_data\": data_list,\n                f\"{self._id}_index\": 0,\n                f\"{self._id}_aggregated\": [],\n                f\"{self._id}_initialized\": True,\n            }\n        )\n\n    def _validate_data(self, data):\n        \"\"\"Validate and return a list of Data objects.\"\"\"\n        if isinstance(data, Data):\n            return [data]\n        if isinstance(data, list) and all(isinstance(item, Data) for item in data):\n            return data\n        msg = \"The 'data' input must be a list of Data objects or a single Data object.\"\n        raise TypeError(msg)\n\n    def evaluate_stop_loop(self) -> bool:\n        \"\"\"Evaluate whether to stop item or done output.\"\"\"\n        current_index = self.ctx.get(f\"{self._id}_index\", 0)\n        data_length = len(self.ctx.get(f\"{self._id}_data\", []))\n        return current_index > data_length\n\n    def item_output(self) -> Data:\n        \"\"\"Output the next item in the list or stop if done.\"\"\"\n        self.initialize_data()\n        current_item = Data(text=\"\")\n\n        if self.evaluate_stop_loop():\n            self.stop(\"item\")\n            return Data(text=\"\")\n\n        # Get data list and current index\n        data_list, current_index = self.loop_variables()\n        if current_index < len(data_list):\n            # Output current item and increment index\n            try:\n                current_item = data_list[current_index]\n            except IndexError:\n                current_item = Data(text=\"\")\n        self.aggregated_output()\n        self.update_ctx({f\"{self._id}_index\": current_index + 1})\n        return current_item\n\n    def done_output(self) -> Data:\n        \"\"\"Trigger the done output when iteration is complete.\"\"\"\n        self.initialize_data()\n\n        if self.evaluate_stop_loop():\n            self.stop(\"item\")\n            self.start(\"done\")\n\n            return self.ctx.get(f\"{self._id}_aggregated\", [])\n        self.stop(\"done\")\n        return Data(text=\"\")\n\n    def loop_variables(self):\n        \"\"\"Retrieve loop variables from context.\"\"\"\n        return (\n            self.ctx.get(f\"{self._id}_data\", []),\n            self.ctx.get(f\"{self._id}_index\", 0),\n        )\n\n    def aggregated_output(self) -> Data:\n        \"\"\"Return the aggregated list once all items are processed.\"\"\"\n        self.initialize_data()\n\n        # Get data list and aggregated list\n        data_list = self.ctx.get(f\"{self._id}_data\", [])\n        aggregated = self.ctx.get(f\"{self._id}_aggregated\", [])\n\n        # Check if loop input is provided and append to aggregated list\n        if self.item is not None and not isinstance(self.item, str) and len(aggregated) <= len(data_list):\n            aggregated.append(self.item)\n            self.update_ctx({f\"{self._id}_aggregated\": aggregated})\n        return aggregated\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}}, "description": "Iterates over a list of Data objects, outputting one item at a time and aggregating results from loop inputs.", "icon": "infinity", "base_classes": ["Data"], "display_name": "Loop", "documentation": "", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Data"], "selected": "Data", "name": "item", "display_name": "<PERSON><PERSON>", "method": "item_output", "value": "__UNDEFINED__", "cache": true, "allows_loop": true}, {"types": ["Data"], "selected": "Data", "name": "done", "display_name": "Done", "method": "done_output", "value": "__UNDEFINED__", "cache": true, "allows_loop": false}], "field_order": ["data"], "beta": false, "legacy": false, "edited": false, "metadata": {}, "tool_mode": false, "lf_version": "1.1.3"}, "showNode": true, "type": "LoopComponent", "id": "LoopComponent-mbKDL"}, "selected": false, "measured": {"width": 320, "height": 279}, "dragging": false}, {"id": "ParseData-RqsuS", "type": "genericNode", "position": {"x": 566.5536491140117, "y": -432.4046813101411}, "data": {"node": {"template": {"_type": "Component", "data": {"tool_mode": false, "trace_as_metadata": true, "list": true, "list_add_label": "Add More", "trace_as_input": true, "required": true, "placeholder": "", "show": true, "name": "data", "value": "", "display_name": "Data", "advanced": false, "input_types": ["Data"], "dynamic": false, "info": "The data to convert to text.", "title_case": false, "type": "other", "_input_type": "DataInput"}, "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.custom import Component\nfrom langflow.helpers.data import data_to_text, data_to_text_list\nfrom langflow.io import DataInput, MultilineInput, Output, StrInput\nfrom langflow.schema import Data\nfrom langflow.schema.message import Message\n\n\nclass ParseDataComponent(Component):\n    display_name = \"Data to Message\"\n    description = \"Convert Data objects into Messages using any {field_name} from input data.\"\n    icon = \"message-square\"\n    name = \"ParseData\"\n\n    inputs = [\n        DataInput(name=\"data\", display_name=\"Data\", info=\"The data to convert to text.\", is_list=True, required=True),\n    ]\n\n    outputs = [\n        Output(\n            display_name=\"Data list\",\n            name=\"text\",\n            info=\"Data as a single Message, with each input Data separated by Separator\",\n            method=\"parse_data\",\n        )\n    ]\n\n   \n\n    def parse_data(self) -> [Data]:\n        return self.data\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}}, "description": "Convert Data objects into Messages using any {field_name} from input data.", "icon": "message-square", "base_classes": ["Data"], "display_name": "Data to Message", "documentation": "", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Data"], "selected": "Data", "name": "text", "hidden": null, "display_name": "Data list", "method": "parse_data", "value": "__UNDEFINED__", "cache": true, "required_inputs": null, "allows_loop": false}], "field_order": ["data"], "beta": false, "legacy": false, "edited": true, "metadata": {}, "tool_mode": false, "lf_version": "1.1.3"}, "showNode": true, "type": "ParseData", "id": "ParseData-RqsuS"}, "selected": false, "measured": {"width": 320, "height": 211}, "dragging": false}, {"id": "RunFlow-KfiaH", "type": "genericNode", "position": {"x": 1446.1664140278904, "y": -1365.0865142852}, "data": {"node": {"template": {"_type": "Component", "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from typing import Any\n\nfrom loguru import logger\n\nfrom langflow.base.tools.run_flow import RunFlow<PERSON><PERSON>Component\nfrom langflow.helpers.flow import run_flow\nfrom langflow.schema import dotdict\n\n\nclass RunFlowComponent(RunFlowBaseComponent):\n    display_name = \"Run Flow\"\n    description = \"Creates a tool component from a Flow that takes all its inputs and runs it.\"\n    beta = True\n    name = \"RunFlow\"\n    icon = \"Workflow\"\n\n    inputs = RunFlowBaseComponent._base_inputs\n    outputs = RunFlowBaseComponent._base_outputs\n\n    async def update_build_config(self, build_config: dotdict, field_value: Any, field_name: str | None = None):\n        if field_name == \"flow_name_selected\":\n            build_config[\"flow_name_selected\"][\"options\"] = await self.get_flow_names()\n            missing_keys = [key for key in self.default_keys if key not in build_config]\n            if missing_keys:\n                msg = f\"Missing required keys in build_config: {missing_keys}\"\n                raise ValueError(msg)\n            if field_value is not None:\n                try:\n                    graph = await self.get_graph(field_value)\n                    build_config = self.update_build_config_from_graph(build_config, graph)\n                except Exception as e:\n                    msg = f\"Error building graph for flow {field_value}\"\n                    logger.exception(msg)\n                    raise RuntimeError(msg) from e\n        return build_config\n\n    async def run_flow_with_tweaks(self):\n        tweaks: dict = {}\n\n        flow_name_selected = self._attributes.get(\"flow_name_selected\")\n        parsed_flow_tweak_data = self._attributes.get(\"flow_tweak_data\", {})\n        if not isinstance(parsed_flow_tweak_data, dict):\n            parsed_flow_tweak_data = parsed_flow_tweak_data.dict()\n\n        if parsed_flow_tweak_data != {}:\n            for field in parsed_flow_tweak_data:\n                if \"~\" in field:\n                    [node, name] = field.split(\"~\")\n                    if node not in tweaks:\n                        tweaks[node] = {}\n                    tweaks[node][name] = parsed_flow_tweak_data[field]\n        else:\n            for field in self._attributes:\n                if field not in self.default_keys and \"~\" in field:\n                    [node, name] = field.split(\"~\")\n                    if node not in tweaks:\n                        tweaks[node] = {}\n                    tweaks[node][name] = self._attributes[field]\n\n        return await run_flow(\n            inputs=None,\n            output_type=\"all\",\n            flow_id=None,\n            flow_name=flow_name_selected,\n            tweaks=tweaks,\n            user_id=str(self.user_id),\n            # run_id=self.graph.run_id,\n            session_id=self.graph.session_id or self.session_id,\n        )\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "flow_name_selected": {"tool_mode": false, "trace_as_metadata": true, "options": ["model_load", "model_fine_tune", "Confidence_interval", "model_inference", "prompt_list", "hyperparameter_tuning"], "options_metadata": [], "combobox": false, "dialog_inputs": {}, "required": false, "placeholder": "", "show": true, "name": "flow_name_selected", "display_name": "Flow Name", "advanced": false, "dynamic": false, "info": "The name of the flow to run.", "real_time_refresh": true, "refresh_button": true, "title_case": false, "type": "str", "_input_type": "DropdownInput", "value": "model_fine_tune"}, "session_id": {"trace_as_input": true, "tool_mode": false, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "session_id", "value": {"text_key": "text", "data": {"text": "", "files": [], "timestamp": "2025-03-12 14:36:37 UTC"}, "default_value": ""}, "display_name": "Session ID", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "The session ID to run the flow in.", "title_case": false, "type": "str", "_input_type": "MessageInput"}, "TextInput-bcn5q~input_value": {"tool_mode": true, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "TextInput-bcn5q~input_value", "value": "datasets/train_test_split/x_train.csv", "display_name": "Patient Note - Text", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Text to be passed as input.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}, "TextInput-pNP0w~input_value": {"tool_mode": true, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "TextInput-pNP0w~input_value", "value": "datasets/train_test_split/y_train.csv", "display_name": "Patient Label - Text", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Text to be passed as input.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}, "TextInput-DBkbt~input_value": {"tool_mode": true, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "TextInput-DBkbt~input_value", "value": "./models/unsloth/Meta-Llama-3.1-8B-Instruct-bnb-4bit", "display_name": "Model path - Text", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Text to be passed as input.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}, "TextInput-6u1pS~input_text": {"tool_mode": true, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "TextInput-6u1pS~input_text", "value": " \"\"\" Your task is to determine whether the patient described in the note have depression or not   **Output Instructions:** provide your final answer (only the number \"1\" or \"0\") in this format **The final answer is: {{ 0 or 1}}**.      ---     ** Patient Note Summary:**     {}     Output the result (1 for depression present, or 0 otherwise) in this format **The final answer is: **.     \"\"\"", "display_name": "Prompt - Input Text", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Enter the text to be processed.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}, "TextInput-BHYQU~config_input": {"tool_mode": true, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "TextInput-BHYQU~config_input", "value": "{\n                \"learning_rate\": 10 ** random.uniform(-5, -3),\n                \"gradient_accumulation_steps\": random.choice([2, 4, 8, 16]),\n                \"epochs\": 3,\n                \"weight_decay\": random.uniform(0.0, 0.2),\n                \"warmup_ratio\": random.uniform(0.0, 0.2)\n            }", "display_name": "Hyperparameter Config - Configuration", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "JSON-like configuration for hyperparameters.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}}, "description": "Creates a tool component from a Flow that takes all its inputs and runs it.", "icon": "Workflow", "base_classes": ["Data", "DataFrame", "Message"], "display_name": "Run Flow", "documentation": "", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Data"], "selected": "Data", "name": "flow_outputs_data", "hidden": true, "display_name": "Flow Data Output", "method": "data_output", "value": "__UNDEFINED__", "cache": true, "required_inputs": [], "allows_loop": false}, {"types": ["DataFrame"], "selected": "DataFrame", "name": "flow_outputs_dataframe", "hidden": true, "display_name": "Flow Dataframe Output", "method": "dataframe_output", "value": "__UNDEFINED__", "cache": true, "required_inputs": [], "allows_loop": false}, {"types": ["Message"], "selected": "Message", "name": "flow_outputs_message", "hidden": null, "display_name": "Flow Message Output", "method": "message_output", "value": "__UNDEFINED__", "cache": true, "required_inputs": [], "allows_loop": false}], "field_order": ["flow_name_selected", "session_id"], "beta": true, "legacy": false, "edited": false, "metadata": {}, "tool_mode": false}, "showNode": true, "type": "RunFlow", "id": "RunFlow-KfiaH"}, "selected": false, "measured": {"width": 320, "height": 660}, "dragging": false}, {"id": "RunFlow-OIU5N", "type": "genericNode", "position": {"x": 1928.013000005254, "y": -1204.2806849360654}, "data": {"node": {"template": {"_type": "Component", "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from typing import Any\n\nfrom loguru import logger\n\nfrom langflow.base.tools.run_flow import RunFlow<PERSON><PERSON>Component\nfrom langflow.helpers.flow import run_flow\nfrom langflow.schema import dotdict\n\n\nclass RunFlowComponent(RunFlowBaseComponent):\n    display_name = \"Run Flow\"\n    description = \"Creates a tool component from a Flow that takes all its inputs and runs it.\"\n    beta = True\n    name = \"RunFlow\"\n    icon = \"Workflow\"\n\n    inputs = RunFlowBaseComponent._base_inputs\n    outputs = RunFlowBaseComponent._base_outputs\n\n    async def update_build_config(self, build_config: dotdict, field_value: Any, field_name: str | None = None):\n        if field_name == \"flow_name_selected\":\n            build_config[\"flow_name_selected\"][\"options\"] = await self.get_flow_names()\n            missing_keys = [key for key in self.default_keys if key not in build_config]\n            if missing_keys:\n                msg = f\"Missing required keys in build_config: {missing_keys}\"\n                raise ValueError(msg)\n            if field_value is not None:\n                try:\n                    graph = await self.get_graph(field_value)\n                    build_config = self.update_build_config_from_graph(build_config, graph)\n                except Exception as e:\n                    msg = f\"Error building graph for flow {field_value}\"\n                    logger.exception(msg)\n                    raise RuntimeError(msg) from e\n        return build_config\n\n    async def run_flow_with_tweaks(self):\n        tweaks: dict = {}\n\n        flow_name_selected = self._attributes.get(\"flow_name_selected\")\n        parsed_flow_tweak_data = self._attributes.get(\"flow_tweak_data\", {})\n        if not isinstance(parsed_flow_tweak_data, dict):\n            parsed_flow_tweak_data = parsed_flow_tweak_data.dict()\n\n        if parsed_flow_tweak_data != {}:\n            for field in parsed_flow_tweak_data:\n                if \"~\" in field:\n                    [node, name] = field.split(\"~\")\n                    if node not in tweaks:\n                        tweaks[node] = {}\n                    tweaks[node][name] = parsed_flow_tweak_data[field]\n        else:\n            for field in self._attributes:\n                if field not in self.default_keys and \"~\" in field:\n                    [node, name] = field.split(\"~\")\n                    if node not in tweaks:\n                        tweaks[node] = {}\n                    tweaks[node][name] = self._attributes[field]\n\n        return await run_flow(\n            inputs=None,\n            output_type=\"all\",\n            flow_id=None,\n            flow_name=flow_name_selected,\n            tweaks=tweaks,\n            user_id=str(self.user_id),\n            # run_id=self.graph.run_id,\n            session_id=self.graph.session_id or self.session_id,\n        )\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "flow_name_selected": {"tool_mode": false, "trace_as_metadata": true, "options": ["model_load", "model_fine_tune", "Confidence_interval", "model_inference", "prompt_list", "hyperparameter_tuning"], "options_metadata": [], "combobox": false, "dialog_inputs": {}, "required": false, "placeholder": "", "show": true, "name": "flow_name_selected", "display_name": "Flow Name", "advanced": false, "dynamic": false, "info": "The name of the flow to run.", "real_time_refresh": true, "refresh_button": true, "title_case": false, "type": "str", "_input_type": "DropdownInput", "value": "model_inference"}, "session_id": {"trace_as_input": true, "tool_mode": false, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "session_id", "value": {"text_key": "text", "data": {"text": "", "files": [], "timestamp": "2025-03-12 14:36:37 UTC"}, "default_value": ""}, "display_name": "Session ID", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "The session ID to run the flow in.", "title_case": false, "type": "str", "_input_type": "MessageInput"}, "TextInput-yOcIj~input_value": {"tool_mode": true, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "TextInput-yOcIj~input_value", "value": " Your task is to determine whether the patient described in the note have depression or not   **Output Instructions:** provide your final answer (only the number \"1\" or \"0\") in this format **The final answer is: {{ 0 or 1}}**.      ---     ** Patient Note Summary:**     {}     Output the result (1 for depression present, or 0 otherwise) in this format **The final answer is: **.", "display_name": "Prompt - Text", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Text to be passed as input.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}, "TextInput-Hp49c~input_text": {"tool_mode": true, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "TextInput-Hp49c~input_text", "value": "datasets/train_test_split/x_test.csv", "display_name": "Patient Note - Input Text", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Enter the text to be processed.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}, "TextInput-iJHmK~input_value": {"tool_mode": true, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "TextInput-iJHmK~input_value", "value": "datasets/train_test_split/y_test.csv", "display_name": "Depression labels - Text", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Text to be passed as input.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}, "TextInput-gvImP~input_value": {"tool_mode": true, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "TextInput-gvImP~input_value", "value": "finetuned_model/models/unsloth/Meta-Llama-3.1-8B-Instruct", "display_name": "ft_model_path - Text", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Text to be passed as input.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}}, "description": "Creates a tool component from a Flow that takes all its inputs and runs it.", "icon": "Workflow", "base_classes": ["Data", "DataFrame", "Message"], "display_name": "Run Flow", "documentation": "", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Data"], "selected": "Data", "name": "flow_outputs_data", "hidden": true, "display_name": "Flow Data Output", "method": "data_output", "value": "__UNDEFINED__", "cache": true, "required_inputs": [], "allows_loop": false}, {"types": ["DataFrame"], "selected": "DataFrame", "name": "flow_outputs_dataframe", "hidden": true, "display_name": "Flow Dataframe Output", "method": "dataframe_output", "value": "__UNDEFINED__", "cache": true, "required_inputs": [], "allows_loop": false}, {"types": ["Message"], "selected": "Message", "name": "flow_outputs_message", "hidden": null, "display_name": "Flow Message Output", "method": "message_output", "value": "__UNDEFINED__", "cache": true, "required_inputs": [], "allows_loop": false}], "field_order": ["flow_name_selected", "session_id"], "beta": true, "legacy": false, "edited": false, "metadata": {}, "tool_mode": false}, "showNode": true, "type": "RunFlow", "id": "RunFlow-OIU5N"}, "selected": false, "measured": {"width": 320, "height": 578}, "dragging": false}], "edges": [{"source": "CustomComponent-rtPYG", "sourceHandle": "{œdataTypeœ:œCustomComponentœ,œidœ:œCustomComponent-rtPYGœ,œnameœ:œoutputœ,œoutput_typesœ:[œDataœ]}", "target": "LoopComponent-mbKDL", "targetHandle": "{œfieldNameœ:œdataœ,œidœ:œLoopComponent-mbKDLœ,œinputTypesœ:[œDataœ],œtypeœ:œotherœ}", "data": {"targetHandle": {"fieldName": "data", "id": "LoopComponent-mbKDL", "inputTypes": ["Data"], "type": "other"}, "sourceHandle": {"dataType": "CustomComponent", "id": "CustomComponent-rtPYG", "name": "output", "output_types": ["Data"]}}, "id": "reactflow__edge-CustomComponent-rtPYG{œdataTypeœ:œCustomComponentœ,œidœ:œCustomComponent-rtPYGœ,œnameœ:œoutputœ,œoutput_typesœ:[œDataœ]}-LoopComponent-mbKDL{œfieldNameœ:œdataœ,œidœ:œLoopComponent-mbKDLœ,œinputTypesœ:[œDataœ],œtypeœ:œotherœ}", "animated": false, "className": ""}, {"source": "LoopComponent-mbKDL", "sourceHandle": "{œdataTypeœ:œLoopComponentœ,œidœ:œLoopComponent-mbKDLœ,œnameœ:œitemœ,œoutput_typesœ:[œDataœ]}", "target": "ParseData-8gINE", "targetHandle": "{œfieldNameœ:œdataœ,œidœ:œParseData-8gINEœ,œinputTypesœ:[œDataœ],œtypeœ:œotherœ}", "data": {"targetHandle": {"fieldName": "data", "id": "ParseData-8gINE", "inputTypes": ["Data"], "type": "other"}, "sourceHandle": {"dataType": "LoopComponent", "id": "LoopComponent-mbKDL", "name": "item", "output_types": ["Data"]}}, "id": "reactflow__edge-LoopComponent-mbKDL{œdataTypeœ:œLoopComponentœ,œidœ:œLoopComponent-mbKDLœ,œnameœ:œitemœ,œoutput_typesœ:[œDataœ]}-ParseData-8gINE{œfieldNameœ:œdataœ,œidœ:œParseData-8gINEœ,œinputTypesœ:[œDataœ],œtypeœ:œotherœ}", "animated": false, "className": ""}, {"source": "CustomComponent-rtPYG", "sourceHandle": "{œdataTypeœ:œCustomComponentœ,œidœ:œCustomComponent-rtPYGœ,œnameœ:œoutputœ,œoutput_typesœ:[œDataœ]}", "target": "CustomComponent-Qiuq3", "targetHandle": "{œfieldNameœ:œconfigœ,œidœ:œCustomComponent-Qiuq3œ,œinputTypesœ:[œDataœ],œtypeœ:œotherœ}", "data": {"targetHandle": {"fieldName": "config", "id": "CustomComponent-Qiuq3", "inputTypes": ["Data"], "type": "other"}, "sourceHandle": {"dataType": "CustomComponent", "id": "CustomComponent-rtPYG", "name": "output", "output_types": ["Data"]}}, "id": "reactflow__edge-CustomComponent-rtPYG{œdataTypeœ:œCustomComponentœ,œidœ:œCustomComponent-rtPYGœ,œnameœ:œoutputœ,œoutput_typesœ:[œDataœ]}-CustomComponent-Qiuq3{œfieldNameœ:œconfigœ,œidœ:œCustomComponent-Qiuq3œ,œinputTypesœ:[œDataœ],œtypeœ:œotherœ}", "animated": false, "className": "", "selected": false}, {"source": "CustomComponent-Qiuq3", "sourceHandle": "{œdataTypeœ:œCustomComponentœ,œidœ:œCustomComponent-Qiuq3œ,œnameœ:œoutputœ,œoutput_typesœ:[œDataœ]}", "target": "ParseData-wam5V", "targetHandle": "{œfieldNameœ:œdataœ,œidœ:œParseData-wam5Vœ,œinputTypesœ:[œDataœ],œtypeœ:œotherœ}", "data": {"targetHandle": {"fieldName": "data", "id": "ParseData-wam5V", "inputTypes": ["Data"], "type": "other"}, "sourceHandle": {"dataType": "CustomComponent", "id": "CustomComponent-Qiuq3", "name": "output", "output_types": ["Data"]}}, "id": "reactflow__edge-CustomComponent-Qiuq3{œdataTypeœ:œCustomComponentœ,œidœ:œCustomComponent-Qiuq3œ,œnameœ:œoutputœ,œoutput_typesœ:[œDataœ]}-ParseData-wam5V{œfieldNameœ:œdataœ,œidœ:œParseData-wam5Vœ,œinputTypesœ:[œDataœ],œtypeœ:œotherœ}", "animated": false, "className": ""}, {"source": "LoopComponent-mbKDL", "sourceHandle": "{œdataTypeœ:œLoopComponentœ,œidœ:œLoopComponent-mbKDLœ,œnameœ:œdoneœ,œoutput_typesœ:[œDataœ]}", "target": "ParseData-RqsuS", "targetHandle": "{œfieldNameœ:œdataœ,œidœ:œParseData-RqsuSœ,œinputTypesœ:[œDataœ],œtypeœ:œotherœ}", "data": {"targetHandle": {"fieldName": "data", "id": "ParseData-RqsuS", "inputTypes": ["Data"], "type": "other"}, "sourceHandle": {"dataType": "LoopComponent", "id": "LoopComponent-mbKDL", "name": "done", "output_types": ["Data"]}}, "id": "reactflow__edge-LoopComponent-mbKDL{œdataTypeœ:œLoopComponentœ,œidœ:œLoopComponent-mbKDLœ,œnameœ:œdoneœ,œoutput_typesœ:[œDataœ]}-ParseData-RqsuS{œfieldNameœ:œdataœ,œidœ:œParseData-RqsuSœ,œinputTypesœ:[œDataœ],œtypeœ:œotherœ}", "animated": false, "className": ""}, {"source": "ParseData-RqsuS", "sourceHandle": "{œdataTypeœ:œParseDataœ,œidœ:œParseData-RqsuSœ,œnameœ:œtextœ,œoutput_typesœ:[œDataœ]}", "target": "CustomComponent-Qiuq3", "targetHandle": "{œfieldNameœ:œaccuracyœ,œidœ:œCustomComponent-Qiuq3œ,œinputTypesœ:[œDataœ],œtypeœ:œotherœ}", "data": {"targetHandle": {"fieldName": "accuracy", "id": "CustomComponent-Qiuq3", "inputTypes": ["Data"], "type": "other"}, "sourceHandle": {"dataType": "ParseData", "id": "ParseData-RqsuS", "name": "text", "output_types": ["Data"]}}, "id": "reactflow__edge-ParseData-RqsuS{œdataTypeœ:œParseDataœ,œidœ:œParseData-RqsuSœ,œnameœ:œtextœ,œoutput_typesœ:[œDataœ]}-CustomComponent-Qiuq3{œfieldNameœ:œaccuracyœ,œidœ:œCustomComponent-Qiuq3œ,œinputTypesœ:[œDataœ],œtypeœ:œotherœ}", "animated": false, "className": ""}, {"source": "CustomComponent-Qiuq3", "sourceHandle": "{œdataTypeœ:œCustomComponentœ,œidœ:œCustomComponent-Qiuq3œ,œnameœ:œoutputœ,œoutput_typesœ:[œDataœ]}", "target": "ParseData-HCIAt", "targetHandle": "{œfieldNameœ:œdataœ,œidœ:œParseData-HCIAtœ,œinputTypesœ:[œDataœ],œtypeœ:œotherœ}", "data": {"targetHandle": {"fieldName": "data", "id": "ParseData-HCIAt", "inputTypes": ["Data"], "type": "other"}, "sourceHandle": {"dataType": "CustomComponent", "id": "CustomComponent-Qiuq3", "name": "output", "output_types": ["Data"]}}, "id": "reactflow__edge-CustomComponent-Qiuq3{œdataTypeœ:œCustomComponentœ,œidœ:œCustomComponent-Qiuq3œ,œnameœ:œoutputœ,œoutput_typesœ:[œDataœ]}-ParseData-HCIAt{œfieldNameœ:œdataœ,œidœ:œParseData-HCIAtœ,œinputTypesœ:[œDataœ],œtypeœ:œotherœ}", "animated": false, "className": ""}, {"source": "MessagetoData-n96f0", "sourceHandle": "{œdataTypeœ:œMessagetoDataœ,œidœ:œMessagetoData-n96f0œ,œnameœ:œdataœ,œoutput_typesœ:[œDataœ]}", "target": "LoopComponent-mbKDL", "targetHandle": "{œdataTypeœ:œLoopComponentœ,œidœ:œLoopComponent-mbKDLœ,œnameœ:œitemœ,œoutput_typesœ:[œDataœ]}", "data": {"targetHandle": {"dataType": "LoopComponent", "id": "LoopComponent-mbKDL", "name": "item", "output_types": ["Data"]}, "sourceHandle": {"dataType": "MessagetoData", "id": "MessagetoData-n96f0", "name": "data", "output_types": ["Data"]}}, "id": "reactflow__edge-MessagetoData-n96f0{œdataTypeœ:œMessagetoDataœ,œidœ:œMessagetoData-n96f0œ,œnameœ:œdataœ,œoutput_typesœ:[œDataœ]}-LoopComponent-mbKDL{œdataTypeœ:œLoopComponentœ,œidœ:œLoopComponent-mbKDLœ,œnameœ:œitemœ,œoutput_typesœ:[œDataœ]}", "animated": false, "className": ""}, {"source": "ParseData-8gINE", "sourceHandle": "{œdataTypeœ:œParseDataœ,œidœ:œParseData-8gINEœ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}", "target": "RunFlow-KfiaH", "targetHandle": "{œfieldNameœ:œTextInput-BHYQU~config_inputœ,œidœ:œRunFlow-KfiaHœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "data": {"targetHandle": {"fieldName": "TextInput-BHYQU~config_input", "id": "RunFlow-KfiaH", "inputTypes": ["Message"], "type": "str"}, "sourceHandle": {"dataType": "ParseData", "id": "ParseData-8gINE", "name": "text", "output_types": ["Message"]}}, "id": "reactflow__edge-ParseData-8gINE{œdataTypeœ:œParseDataœ,œidœ:œParseData-8gINEœ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}-RunFlow-KfiaH{œfieldNameœ:œTextInput-BHYQU~config_inputœ,œidœ:œRunFlow-KfiaHœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "className": ""}, {"source": "RunFlow-KfiaH", "sourceHandle": "{œdataTypeœ:œRunFlowœ,œidœ:œRunFlow-KfiaHœ,œnameœ:œflow_outputs_messageœ,œoutput_typesœ:[œMessageœ]}", "target": "RunFlow-OIU5N", "targetHandle": "{œfieldNameœ:œTextInput-gvImP~input_valueœ,œidœ:œRunFlow-OIU5Nœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "data": {"targetHandle": {"fieldName": "TextInput-gvImP~input_value", "id": "RunFlow-OIU5N", "inputTypes": ["Message"], "type": "str"}, "sourceHandle": {"dataType": "RunFlow", "id": "RunFlow-KfiaH", "name": "flow_outputs_message", "output_types": ["Message"]}}, "id": "reactflow__edge-RunFlow-KfiaH{œdataTypeœ:œRunFlowœ,œidœ:œRunFlow-KfiaHœ,œnameœ:œflow_outputs_messageœ,œoutput_typesœ:[œMessageœ]}-RunFlow-OIU5N{œfieldNameœ:œTextInput-gvImP~input_valueœ,œidœ:œRunFlow-OIU5Nœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "className": ""}, {"source": "RunFlow-OIU5N", "sourceHandle": "{œdataTypeœ:œRunFlowœ,œidœ:œRunFlow-OIU5Nœ,œnameœ:œflow_outputs_messageœ,œoutput_typesœ:[œMessageœ]}", "target": "MessagetoData-n96f0", "targetHandle": "{œfieldNameœ:œmessageœ,œidœ:œMessagetoData-n96f0œ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "data": {"targetHandle": {"fieldName": "message", "id": "MessagetoData-n96f0", "inputTypes": ["Message"], "type": "str"}, "sourceHandle": {"dataType": "RunFlow", "id": "RunFlow-OIU5N", "name": "flow_outputs_message", "output_types": ["Message"]}}, "id": "reactflow__edge-Run<PERSON>low-OIU5N{œdataTypeœ:œRunFlowœ,œidœ:œRunFlow-OIU5Nœ,œnameœ:œflow_outputs_messageœ,œoutput_typesœ:[œMessageœ]}-MessagetoData-n96f0{œfieldNameœ:œmessageœ,œidœ:œMessagetoData-n96f0œ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "className": ""}], "viewport": {"x": 182.98281942758928, "y": 661.63395061922, "zoom": 0.3531840924360468}}}