{"updated_at": "2025-03-12T21:47:07+00:00", "description": "This flow takes a base model, a training dataset (consisting of patient_notes and corresponding labels), and configuration parameters as inputs. It fine-tunes the model on the provided dataset using the specified configurations, saves the fine-tuned model to the designated directory, and returns the file path of the saved model.", "webhook": false, "icon": null, "locked": false, "name": "model_fine_tune", "endpoint_name": null, "tags": null, "gradient": null, "id": "312d2477-c090-4c75-8b49-a0e531b514a5", "is_component": false, "user_id": "938fff2c-f0ff-4de8-8853-b8825c3f76ea", "folder_id": "66998fec-d310-4886-89a0-35a27eab4da1", "icon_bg_color": null, "data": {"nodes": [{"id": "DataExtractor-FSJDU", "type": "genericNode", "position": {"x": 845.9094378118162, "y": 130.62659927323259}, "data": {"node": {"template": {"_type": "Component", "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.custom import Component\nfrom langflow.io import MultilineInput, Output\nfrom langflow.schema import DataFrame\nimport pandas as pd\nimport os\n\n\nclass DataExtractor(Component):\n    \"\"\"\n    A component to extract and process patient notes (X) and depression labels (Y) from CSV files.\n    This component reads the input files, extracts the relevant columns, and outputs them as DataFrames.\n    \"\"\"\n\n    display_name = \"Data Parser\"  # Display name for the component in the UI\n    description = \"\"\"\n    Extract patient notes (X) and depression labels (Y) from CSV files.\n    The component reads the input files, processes them, and outputs the extracted data as DataFrames.\n    \"\"\"\n    documentation: str = \"http://docs.langflow.org/components/custom\"  # Link to documentation\n    icon = \"code\"  # Icon representing the component in the UI\n    name = \"DataExtractor\"  # Internal name of the component\n\n    # Define input fields\n    inputs = [\n        MultilineInput(\n            name=\"patient_notes_path\",  # Internal name for the patient notes file path\n            display_name=\"Patient Notes Path\",  # Display name for the patient notes file path\n            required=True,  # This input is mandatory\n            info=\"Path to the CSV file containing patient notes.\",\n        ),\n        MultilineInput(\n            name=\"depression_labels_path\",  # Internal name for the depression labels file path\n            display_name=\"Depression Labels Path\",  # Display name for the depression labels file path\n            required=True,  # This input is mandatory\n            info=\"Path to the CSV file containing depression labels.\",\n        ),\n    ]\n\n    # Define output fields\n    outputs = [\n        Output(\n            display_name=\"Patient Notes\",  # Display name for the patient notes output\n            name=\"patient_notes\",  # Internal name for the patient notes output\n            method=\"extract_patient_notes\",  # Method to generate the output\n        ),\n        Output(\n            display_name=\"Depression Labels\",  # Display name for the depression labels output\n            name=\"depression_labels\",  # Internal name for the depression labels output\n            method=\"extract_depression_labels\",  # Method to generate the output\n        ),\n    ]\n\n    def _read_csv_file(self, file_path: str) -> pd.DataFrame:\n        \"\"\"\n        Read a CSV file and return its contents as a DataFrame.\n\n        Args:\n            file_path (str): Path to the CSV file.\n\n        Returns:\n            pd.DataFrame: DataFrame containing the file data.\n\n        Raises:\n            ValueError: If the file cannot be read.\n        \"\"\"\n        try:\n            return pd.read_csv(file_path)\n        except Exception as e:\n            raise ValueError(f\"Failed to read the file at {file_path}: {e}\")\n\n    def extract_patient_notes(self) -> DataFrame:\n        \"\"\"\n        Extract patient notes from the input CSV file.\n\n        Returns:\n            DataFrame: A DataFrame containing the patient notes.\n        \"\"\"\n        df = self._read_csv_file(self.patient_notes_path)\n        return DataFrame({\"note\": df[\"note\"]})  # Extract the \"note\" column\n\n    def extract_depression_labels(self) -> DataFrame:\n        \"\"\"\n        Extract depression labels from the input CSV file.\n\n        Returns:\n            DataFrame: A DataFrame containing the depression labels.\n        \"\"\"\n        df = self._read_csv_file(self.depression_labels_path)\n        return DataFrame({\"label\": df[\"label\"]})  # Extract the \"label\" column", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "depression_labels_path": {"tool_mode": false, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": true, "placeholder": "", "show": true, "name": "depression_labels_path", "value": "", "display_name": "Depression Labels Path", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Path to the CSV file containing depression labels.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}, "patient_notes_path": {"tool_mode": false, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": true, "placeholder": "", "show": true, "name": "patient_notes_path", "value": "", "display_name": "Patient Notes Path", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Path to the CSV file containing patient notes.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}}, "description": "\n    Extract patient notes (X) and depression labels (Y) from CSV files.\n    The component reads the input files, processes them, and outputs the extracted data as DataFrames.\n    ", "icon": "code", "base_classes": ["DataFrame"], "display_name": "Data Parser", "documentation": "http://docs.langflow.org/components/custom", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["DataFrame"], "selected": "DataFrame", "name": "patient_notes", "hidden": null, "display_name": "Patient Notes", "method": "extract_patient_notes", "value": "__UNDEFINED__", "cache": true, "required_inputs": null, "allows_loop": false}, {"types": ["DataFrame"], "selected": "DataFrame", "name": "depression_labels", "hidden": null, "display_name": "Depression Labels", "method": "extract_depression_labels", "value": "__UNDEFINED__", "cache": true, "required_inputs": null, "allows_loop": false}], "field_order": ["patient_notes_path", "depression_labels_path"], "beta": false, "legacy": false, "edited": true, "metadata": {}, "tool_mode": false, "lf_version": "1.1.3"}, "showNode": true, "type": "DataExtractor", "id": "DataExtractor-FSJDU"}, "selected": false, "measured": {"width": 320, "height": 456}, "dragging": false}, {"id": "TextInput-EUxil", "type": "genericNode", "position": {"x": 308.498903621745, "y": 130.2435899753766}, "data": {"node": {"template": {"_type": "Component", "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.base.io.text import TextComponent\nfrom langflow.io import MultilineInput, Output\nfrom langflow.schema.message import Message\n\n\nclass TextInputComponent(TextComponent):\n    display_name = \"Patient Note\"\n    description = \"Takes patient notes csv file path\"\n    icon = \"type\"\n    name = \"TextInput\"\n\n    inputs = [\n        MultilineInput(\n            name=\"input_value\",\n            display_name=\"Text\",\n            info=\"Text to be passed as input.\",\n        ),\n    ]\n    outputs = [\n        Output(display_name=\"Message\", name=\"text\", method=\"text_response\"),\n    ]\n\n    def text_response(self) -> Message:\n        return Message(\n            text=self.input_value,\n        )\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "input_value": {"tool_mode": false, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "input_value", "value": "datasets/train_test_split/x_train.csv", "display_name": "Text", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Text to be passed as input.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}}, "description": "Takes patient notes csv file path", "icon": "type", "base_classes": ["Message"], "display_name": "Patient Note", "documentation": "", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Message"], "selected": "Message", "name": "text", "hidden": null, "display_name": "Message", "method": "text_response", "value": "__UNDEFINED__", "cache": true, "required_inputs": null, "allows_loop": false}], "field_order": ["input_value"], "beta": false, "legacy": false, "edited": true, "metadata": {}, "tool_mode": false, "lf_version": "1.1.3"}, "showNode": true, "type": "TextInput", "id": "TextInput-EUxil"}, "selected": false, "measured": {"width": 320, "height": 229}, "dragging": false}, {"id": "TextInput-ZMwVx", "type": "genericNode", "position": {"x": 301.2143745030032, "y": 380.1466467204386}, "data": {"node": {"template": {"_type": "Component", "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.base.io.text import TextComponent\nfrom langflow.io import MultilineInput, Output\nfrom langflow.schema.message import Message\n\n\nclass TextInputComponent(TextComponent):\n    display_name = \"Patient Label\"\n    description = \"Takes patient label csv file path.\"\n    icon = \"type\"\n    name = \"TextInput\"\n\n    inputs = [\n        MultilineInput(\n            name=\"input_value\",\n            display_name=\"Text\",\n            info=\"Text to be passed as input.\",\n        ),\n    ]\n    outputs = [\n        Output(display_name=\"Message\", name=\"text\", method=\"text_response\"),\n    ]\n\n    def text_response(self) -> Message:\n        return Message(\n            text=self.input_value,\n        )\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "input_value": {"tool_mode": false, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "input_value", "value": "datasets/train_test_split/y_train.csv", "display_name": "Text", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Text to be passed as input.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}}, "description": "Takes patient label csv file path.", "icon": "type", "base_classes": ["Message"], "display_name": "Patient Label", "documentation": "", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Message"], "selected": "Message", "name": "text", "hidden": null, "display_name": "Message", "method": "text_response", "value": "__UNDEFINED__", "cache": true, "required_inputs": null, "allows_loop": false}], "field_order": ["input_value"], "beta": false, "legacy": false, "edited": true, "metadata": {}, "tool_mode": false, "lf_version": "1.1.3"}, "showNode": true, "type": "TextInput", "id": "TextInput-ZMwVx"}, "selected": false, "measured": {"width": 320, "height": 229}, "dragging": false}, {"id": "PromptBuilder-5staL", "type": "genericNode", "position": {"x": 1251.0148056086448, "y": 125.44550584025532}, "data": {"node": {"template": {"_type": "Component", "label_data": {"tool_mode": false, "trace_as_metadata": true, "list": false, "list_add_label": "Add More", "trace_as_input": true, "required": true, "placeholder": "", "show": true, "name": "label_data", "value": "", "display_name": "Label Data", "advanced": false, "input_types": ["DataFrame"], "dynamic": false, "info": "", "title_case": false, "type": "other", "_input_type": "DataFrameInput"}, "note_data": {"tool_mode": false, "trace_as_metadata": true, "list": false, "list_add_label": "Add More", "trace_as_input": true, "required": true, "placeholder": "", "show": true, "name": "note_data", "value": "", "display_name": "Note Data", "advanced": false, "input_types": ["DataFrame"], "dynamic": false, "info": "", "title_case": false, "type": "other", "_input_type": "DataFrameInput"}, "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.custom import Component\nfrom langflow.io import Output, MultilineInput, DataFrameInput, MessageTextInput\nfrom langflow.schema import DataFrame\nimport pandas as pd\nfrom transformers import AutoTokenizer\nimport gc\nimport torch\n\n\nclass PromptBuilder(Component):\n    \"\"\"\n    A component to build prompts for training or inference using a template.\n    This component takes a note and label, formats them into a prompt using a template,\n    and applies a tokenizer to generate the final prompt structure.\n    \"\"\"\n\n    display_name = \"Prompt Builder\"  # Display name for the component in the UI\n    description = \"\"\"\n    Build prompts for training or inference by providing a template with placeholders for notes and labels.\n    The component formats the input data into a structured prompt and applies a tokenizer to generate the final output.\n    \"\"\"\n    icon = \"code\"  # Icon representing the component in the UI\n    name = \"PromptBuilder\"  # Internal name of the component\n\n    # Define input fields\n    inputs = [\n        DataFrameInput(\n            name=\"note_data\",  # Internal name for the note input\n            display_name=\"Note Data\",  # Display name for the note input\n            required=True,  # This input is mandatory\n        ),\n        DataFrameInput(\n            name=\"label_data\",  # Internal name for the label input\n            display_name=\"Label Data\",  # Display name for the label input\n            required=True,  # This input is mandatory\n        ),\n        MessageTextInput(\n            name=\"tokenizer_path\",  # Internal name for the tokenizer input\n            display_name=\"Tokenizer Path\",  # Display name for the tokenizer input\n            info=\"Path to the tokenizer to be used for prompt formatting.\",  # Help text for the input\n            tool_mode=True,  # Indicates this input is used in tool mode\n        ),\n        MultilineInput(\n            name=\"input_template\",  # Internal name for the input prompt template\n            display_name=\"Input Prompt Template\",  # Display name for the input prompt template\n            info=\"Template for the user input prompt. Use `{}` as a placeholder for the note.\",\n        ),\n        MultilineInput(\n            name=\"output_template\",  # Internal name for the output prompt template\n            display_name=\"Output Prompt Template\",  # Display name for the output prompt template\n            value=\"**The final answer is: {}**\",  # Default value for the output template\n            info=\"Template for the model's output prompt. Use `{}` as a placeholder for the label.\",\n        ),\n    ]\n\n    # Define output fields\n    outputs = [\n        Output(\n            display_name=\"Formatted Prompts\",  # Display name for the output\n            name=\"formatted_prompts\",  # Internal name for the output\n            method=\"build_prompts\",  # Method to generate the output\n        )\n    ]\n\n    def format_chat_prompt(self, note: str, label: str, tokenizer) -> str:\n        \"\"\"\n        Format the note and label into a structured chat prompt using the provided templates.\n\n        Args:\n            note (str): The note to be included in the prompt.\n            label (str): The label to be included in the prompt.\n            tokenizer: The tokenizer to apply the chat template.\n\n        Returns:\n            str: The formatted chat prompt.\n        \"\"\"\n        user_content = self.input_template.format(note)  # Format the user input template\n        assistant_content = self.output_template.format(label)  # Format the assistant output template\n        chat = [\n            {\"role\": \"user\", \"content\": user_content},\n            {\"role\": \"assistant\", \"content\": assistant_content},\n        ]\n        return tokenizer.apply_chat_template(chat, tokenize=False)  # Apply the tokenizer's chat template\n\n    def build_prompts(self) -> DataFrame:\n        \"\"\"\n        Build and format prompts for all note-label pairs in the input data.\n\n        Returns:\n            DataFrame: A DataFrame containing the formatted prompts.\n        \"\"\"\n        notes = self.note_data[\"note\"]  # Extract notes from the input DataFrame\n        labels = self.label_data[\"label\"]  # Extract labels from the input DataFrame\n        tokenizer = AutoTokenizer.from_pretrained(self.tokenizer_path)  # Load the tokenizer\n\n        formatted_prompts = []\n        for note, label in zip(notes, labels):\n            prompt = self.format_chat_prompt(note, label, tokenizer)  # Format each note-label pair\n            formatted_prompts.append(prompt)\n\n        # Clean up resources\n        del tokenizer\n        gc.collect()\n        if torch.cuda.is_available():\n            torch.cuda.empty_cache()\n\n        # Return the formatted prompts as a DataFrame\n        return DataFrame(pd.DataFrame(formatted_prompts, columns=[\"prompt\"]))", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "input_template": {"tool_mode": false, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "input_template", "value": "", "display_name": "Input Prompt Template", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Template for the user input prompt. Use `{}` as a placeholder for the note.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}, "output_template": {"tool_mode": false, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "output_template", "value": "**The final answer is: {}**", "display_name": "Output Prompt Template", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Template for the model's output prompt. Use `{}` as a placeholder for the label.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}, "tokenizer_path": {"tool_mode": true, "trace_as_input": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "tokenizer_path", "value": "", "display_name": "Tokenizer Path", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Path to the tokenizer to be used for prompt formatting.", "title_case": false, "type": "str", "_input_type": "MessageTextInput"}}, "description": "\n    Build prompts for training or inference by providing a template with placeholders for notes and labels.\n    The component formats the input data into a structured prompt and applies a tokenizer to generate the final output.\n    ", "icon": "code", "base_classes": ["DataFrame"], "display_name": "Prompt Builder", "documentation": "", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["DataFrame"], "selected": "DataFrame", "name": "formatted_prompts", "hidden": null, "display_name": "Formatted Prompts", "method": "build_prompts", "value": "__UNDEFINED__", "cache": true, "required_inputs": null, "allows_loop": false}], "field_order": ["note_data", "label_data", "tokenizer_path", "input_template", "output_template"], "beta": false, "legacy": false, "edited": true, "metadata": {}, "tool_mode": false, "lf_version": "1.1.3"}, "showNode": true, "type": "PromptBuilder", "id": "PromptBuilder-5staL"}, "selected": false, "measured": {"width": 320, "height": 637}, "dragging": false}, {"id": "TextInput-135mX", "type": "genericNode", "position": {"x": 298.7863582500732, "y": 651.6607733064886}, "data": {"node": {"template": {"_type": "Component", "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.base.io.text import TextComponent\nfrom langflow.io import MultilineInput, Output\nfrom langflow.schema.message import Message\n\n\nclass TextInputComponent(TextComponent):\n    display_name = \"Model path\"\n    description = \"Takes model path as input\"\n    icon = \"type\"\n    name = \"TextInput\"\n\n    inputs = [\n        MultilineInput(\n            name=\"input_value\",\n            display_name=\"Text\",\n            info=\"Text to be passed as input.\",\n        ),\n    ]\n    outputs = [\n        Output(display_name=\"Message\", name=\"text\", method=\"text_response\"),\n    ]\n\n    def text_response(self) -> Message:\n        return Message(\n            text=self.input_value,\n        )\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "input_value": {"tool_mode": false, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "input_value", "value": "./models/unsloth/Meta-Llama-3.1-8B-Instruct-bnb-4bit", "display_name": "Text", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Text to be passed as input.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}}, "description": "Takes model path as input", "icon": "type", "base_classes": ["Message"], "display_name": "Model path", "documentation": "", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Message"], "selected": "Message", "name": "text", "hidden": null, "display_name": "Message", "method": "text_response", "value": "__UNDEFINED__", "cache": true, "required_inputs": null, "allows_loop": false}], "field_order": ["input_value"], "beta": false, "legacy": false, "edited": true, "metadata": {}, "tool_mode": false, "lf_version": "1.1.3"}, "showNode": true, "type": "TextInput", "id": "TextInput-135mX"}, "selected": true, "measured": {"width": 320, "height": 229}, "dragging": false}, {"id": "ModelFineTuning-JkCoD", "type": "genericNode", "position": {"x": 1632.1339645738399, "y": 140.78475604449957}, "data": {"node": {"template": {"_type": "Component", "prompts": {"tool_mode": false, "trace_as_metadata": true, "list": false, "list_add_label": "Add More", "trace_as_input": true, "required": true, "placeholder": "", "show": true, "name": "prompts", "value": "", "display_name": "Prompts", "advanced": false, "input_types": ["DataFrame"], "dynamic": false, "info": "", "title_case": false, "type": "other", "_input_type": "DataFrameInput"}, "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.custom import Component\nfrom langflow.io import MessageTextInput, Output, DataFrameInput\nfrom langflow.schema import Message\nfrom trl import SFTTrainer, SFTConfig\nfrom datasets import Dataset\nfrom unsloth import FastLanguageModel, is_bfloat16_supported\nimport gc\nimport torch\nimport os\nimport random\nimport shutil\n\n\n\nclass ModelTrainer(Component):\n    \"\"\"\n    A component for fine-tuning a language model using the provided prompts and configuration.\n    This component handles model loading, training, and saving the fine-tuned model to a specified path.\n    \"\"\"\n\n    display_name = \"Model Fine-Tuning\"  # Display name for the component in the UI\n    description = \"\"\"\n    Fine-tune a pre-trained language model using the provided prompts and training configuration.\n    The component supports LoRA (Low-Rank Adaptation) for efficient fine-tuning and saves the fine-tuned model to the specified path.\n    \"\"\"\n    documentation: str = \"http://docs.langflow.org/components/custom\"  # Link to documentation\n    icon = \"code\"  # Icon representing the component in the UI\n    name = \"ModelFineTuning\"  # Internal name of the component\n\n    # Define input fields\n    inputs = [\n        DataFrameInput(\n            name=\"prompts\",  # Internal name for the prompts input\n            display_name=\"Prompts\",  # Display name for the prompts input\n            required=True,  # This input is mandatory\n        ),\n        MessageTextInput(\n            name=\"model_path\",  # Internal name for the model path input\n            display_name=\"Model Path\",  # Display name for the model path input\n            required=True,  # This input is mandatory\n        ),\n        MessageTextInput(\n            name=\"save_path\",  # Internal name for the save path input\n            display_name=\"Save Path\",  # Display name for the save path input\n            info=\"Path to save the fine-tuned model.\",  # Help text for the input\n            value=\"finetuned_model\",  # Default value for the save path\n            tool_mode=True,  # Indicates this input is used in tool mode\n        ),\n        MessageTextInput(\n            name=\"training_config\",  # Internal name for the training config input\n            display_name=\"Training Config\",  # Display name for the training config input\n            info=\"Configuration for fine-tuning parameters.\",  # Help text for the input\n            value=\"\"\"{\n                'learning_rate': 0.0003068487651615263,\n                'gradient_accumulation_steps': 16,\n                'epochs': 1,\n                'weight_decay': 0.02857038520567239,\n                'warmup_ratio': 0.1757736057365846\n            }\"\"\",  # Default configuration\n            tool_mode=True,  # Indicates this input is used in tool mode\n        ),\n    ]\n\n    # Define output fields\n    outputs = [\n        Output(\n            display_name=\"Fine-Tuned Model Path\",  # Display name for the output\n            name=\"fine_tuned_model_path\",  # Internal name for the output\n            method=\"fine_tune_model\",  # Method to generate the output\n        )\n    ]\n\n    def train_model(self) -> str:\n        \"\"\"\n        Fine-tune the model using the provided prompts and configuration.\n\n        Returns:\n            str: The path where the fine-tuned model is saved.\n        \"\"\"\n        # Load the training configuration\n        config = eval(self.training_config)\n\n        # Load the pre-trained model and tokenizer\n        model, tokenizer = FastLanguageModel.from_pretrained(model_name=self.model_path)\n\n        # Prepare the training dataset\n        data = Dataset.from_dict({\"text\": self.prompts[\"prompt\"]})\n    \n\n        # Define the save path for the fine-tuned model\n        ft_pth = f\"./{self.save_path}/{self.model_path}\"\n        \n        if os.path.exists(ft_pth):\n            # Remove the directory and its contents\n            shutil.rmtree(ft_pth)\n\n        # Recreate the directory\n        os.makedirs(ft_pth)\n\n\n        # Initialize the SFTTrainer\n        self._training(model, tokenizer, data, ft_pth, config)\n\n        # Clean up resources\n        del model\n        del tokenizer\n        gc.collect()\n        if torch.cuda.is_available():\n            torch.cuda.empty_cache()\n\n        return ft_pth\n        \n\n    \n    def _training(self, model, tokenizer, data, ft_pth, config):\n        \"\"\"\n        Fine-tune the model using the provided training data and configuration.\n        \"\"\"\n        trainer = SFTTrainer(\n            model=model,\n            tokenizer=tokenizer,\n            train_dataset=data,\n            args=SFTConfig(\n                packing=False,\n                dataset_num_proc=5,\n                dataset_text_field=\"text\",\n                learning_rate=config[\"learning_rate\"],\n                per_device_train_batch_size=8,\n                gradient_accumulation_steps=config[\"gradient_accumulation_steps\"],\n                warmup_steps=4,\n                num_train_epochs=config[\"epochs\"],\n                fp16=not is_bfloat16_supported(),\n                bf16=is_bfloat16_supported(),\n                logging_steps=1,\n                optim=\"adamw_8bit\",\n                weight_decay=config[\"weight_decay\"],\n                lr_scheduler_type=\"linear\",\n                output_dir=\"checkpoints\",\n                report_to=\"none\",\n                logging_dir=\"logs\",\n            ),\n        )\n        print(f\"Trainer: {trainer}\")\n    \n        # Train the model\n        trained = trainer.train()\n        print(trained)\n        trainer.save_model(ft_pth)\n        tokenizer.save_pretrained(ft_pth)\n    \n            \n\n    def fine_tune_model(self) -> Message:\n        \"\"\"\n        Execute the fine-tuning process and return the path to the fine-tuned model.\n\n        Returns:\n            Message: A message containing the path to the fine-tuned model.\n        \"\"\"\n        save_path = self.train_model()\n        return Message(text=save_path)", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "model_path": {"tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": true, "placeholder": "", "show": true, "name": "model_path", "value": "", "display_name": "Model Path", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "", "title_case": false, "type": "str", "_input_type": "MessageTextInput"}, "save_path": {"tool_mode": true, "trace_as_input": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "save_path", "value": "finetuned_model", "display_name": "Save Path", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Path to save the fine-tuned model.", "title_case": false, "type": "str", "_input_type": "MessageTextInput"}, "training_config": {"tool_mode": true, "trace_as_input": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "training_config", "value": "", "display_name": "Training Config", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Configuration for fine-tuning parameters.", "title_case": false, "type": "str", "_input_type": "MessageTextInput"}}, "description": "\n    Fine-tune a pre-trained language model using the provided prompts and training configuration.\n    The component supports LoRA (Low-Rank Adaptation) for efficient fine-tuning and saves the fine-tuned model to the specified path.\n    ", "icon": "code", "base_classes": ["Message"], "display_name": "Model Fine-Tuning", "documentation": "http://docs.langflow.org/components/custom", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Message"], "selected": "Message", "name": "fine_tuned_model_path", "hidden": null, "display_name": "Fine-Tuned Model Path", "method": "fine_tune_model", "value": "__UNDEFINED__", "cache": true, "required_inputs": null, "allows_loop": false}], "field_order": ["prompts", "model_path", "save_path", "training_config"], "beta": false, "legacy": false, "edited": true, "metadata": {}, "tool_mode": false, "lf_version": "1.1.3"}, "showNode": true, "type": "ModelFineTuning", "id": "ModelFineTuning-JkCoD"}, "selected": false, "measured": {"width": 320, "height": 574}, "dragging": false}, {"id": "ChatOutput-DUgnc", "type": "genericNode", "position": {"x": 2007.8720944785562, "y": 655.8280579249468}, "data": {"node": {"template": {"_type": "Component", "background_color": {"tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "background_color", "value": "", "display_name": "Background Color", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "The background color of the icon.", "title_case": false, "type": "str", "_input_type": "MessageTextInput"}, "chat_icon": {"tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "chat_icon", "value": "", "display_name": "Icon", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "The icon of the message.", "title_case": false, "type": "str", "_input_type": "MessageTextInput"}, "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.base.io.chat import ChatComponent\nfrom langflow.inputs import BoolInput\nfrom langflow.io import DropdownInput, MessageInput, MessageTextInput, Output\nfrom langflow.schema.message import Message\nfrom langflow.schema.properties import Source\nfrom langflow.utils.constants import (\n    MESSAGE_SENDER_AI,\n    MESSAGE_SENDER_NAME_AI,\n    MESSAGE_SENDER_USER,\n)\n\n\nclass ChatOutput(ChatComponent):\n    display_name = \"Chat Output\"\n    description = \"Display a chat message in the Playground.\"\n    icon = \"MessagesSquare\"\n    name = \"ChatOutput\"\n    minimized = True\n\n    inputs = [\n        MessageInput(\n            name=\"input_value\",\n            display_name=\"Text\",\n            info=\"Message to be passed as output.\",\n        ),\n        BoolInput(\n            name=\"should_store_message\",\n            display_name=\"Store Messages\",\n            info=\"Store the message in the history.\",\n            value=True,\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"sender\",\n            display_name=\"Sender Type\",\n            options=[MESSAGE_SENDER_AI, MESSAGE_SENDER_USER],\n            value=MESSAGE_SENDER_AI,\n            advanced=True,\n            info=\"Type of sender.\",\n        ),\n        MessageTextInput(\n            name=\"sender_name\",\n            display_name=\"Sender Name\",\n            info=\"Name of the sender.\",\n            value=MESSAGE_SENDER_NAME_AI,\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"session_id\",\n            display_name=\"Session ID\",\n            info=\"The session ID of the chat. If empty, the current session ID parameter will be used.\",\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"data_template\",\n            display_name=\"Data Template\",\n            value=\"{text}\",\n            advanced=True,\n            info=\"Template to convert Data to Text. If left empty, it will be dynamically set to the Data's text key.\",\n        ),\n        MessageTextInput(\n            name=\"background_color\",\n            display_name=\"Background Color\",\n            info=\"The background color of the icon.\",\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"chat_icon\",\n            display_name=\"Icon\",\n            info=\"The icon of the message.\",\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"text_color\",\n            display_name=\"Text Color\",\n            info=\"The text color of the name\",\n            advanced=True,\n        ),\n    ]\n    outputs = [\n        Output(\n            display_name=\"Message\",\n            name=\"message\",\n            method=\"message_response\",\n        ),\n    ]\n\n    def _build_source(self, id_: str | None, display_name: str | None, source: str | None) -> Source:\n        source_dict = {}\n        if id_:\n            source_dict[\"id\"] = id_\n        if display_name:\n            source_dict[\"display_name\"] = display_name\n        if source:\n            source_dict[\"source\"] = source\n        return Source(**source_dict)\n\n    async def message_response(self) -> Message:\n        source, icon, display_name, source_id = self.get_properties_from_source_component()\n        background_color = self.background_color\n        text_color = self.text_color\n        if self.chat_icon:\n            icon = self.chat_icon\n        message = self.input_value if isinstance(self.input_value, Message) else Message(text=self.input_value)\n        message.sender = self.sender\n        message.sender_name = self.sender_name\n        message.session_id = self.session_id\n        message.flow_id = self.graph.flow_id if hasattr(self, \"graph\") else None\n        message.properties.source = self._build_source(source_id, display_name, source)\n        message.properties.icon = icon\n        message.properties.background_color = background_color\n        message.properties.text_color = text_color\n        if self.session_id and isinstance(message, Message) and self.should_store_message:\n            stored_message = await self.send_message(\n                message,\n            )\n            self.message.value = stored_message\n            message = stored_message\n\n        self.status = message\n        return message\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "data_template": {"tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "data_template", "value": "{text}", "display_name": "Data Template", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "Template to convert Data to Text. If left empty, it will be dynamically set to the Data's text key.", "title_case": false, "type": "str", "_input_type": "MessageTextInput"}, "input_value": {"trace_as_input": true, "tool_mode": false, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "input_value", "value": "", "display_name": "Text", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Message to be passed as output.", "title_case": false, "type": "str", "_input_type": "MessageInput"}, "sender": {"tool_mode": false, "trace_as_metadata": true, "options": ["Machine", "User"], "options_metadata": [], "combobox": false, "dialog_inputs": {}, "required": false, "placeholder": "", "show": true, "name": "sender", "value": "Machine", "display_name": "Sender Type", "advanced": true, "dynamic": false, "info": "Type of sender.", "title_case": false, "type": "str", "_input_type": "DropdownInput"}, "sender_name": {"tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "sender_name", "value": "AI", "display_name": "Sender Name", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "Name of the sender.", "title_case": false, "type": "str", "_input_type": "MessageTextInput"}, "session_id": {"tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "session_id", "value": "", "display_name": "Session ID", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "The session ID of the chat. If empty, the current session ID parameter will be used.", "title_case": false, "type": "str", "_input_type": "MessageTextInput"}, "should_store_message": {"tool_mode": false, "trace_as_metadata": true, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "should_store_message", "value": true, "display_name": "Store Messages", "advanced": true, "dynamic": false, "info": "Store the message in the history.", "title_case": false, "type": "bool", "_input_type": "BoolInput"}, "text_color": {"tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "text_color", "value": "", "display_name": "Text Color", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "The text color of the name", "title_case": false, "type": "str", "_input_type": "MessageTextInput"}}, "description": "Display a chat message in the Playground.", "icon": "MessagesSquare", "base_classes": ["Message"], "display_name": "Chat Output", "documentation": "", "minimized": true, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Message"], "selected": "Message", "name": "message", "display_name": "Message", "method": "message_response", "value": "__UNDEFINED__", "cache": true, "allows_loop": false}], "field_order": ["input_value", "should_store_message", "sender", "sender_name", "session_id", "data_template", "background_color", "chat_icon", "text_color"], "beta": false, "legacy": false, "edited": false, "metadata": {}, "tool_mode": false}, "showNode": false, "type": "ChatOutput", "id": "ChatOutput-DUgnc"}, "selected": false, "measured": {"width": 192, "height": 66}, "dragging": false}, {"id": "TextInput-uweRT", "type": "genericNode", "position": {"x": 299.081131022081, "y": 909.9040412840338}, "data": {"node": {"template": {"_type": "Component", "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.base.io.text import TextComponent\nfrom langflow.io import MultilineInput, Output\nfrom langflow.schema.message import Message\n\n\nclass TextInputComponent(TextComponent):\n    \"\"\"\n    A component to capture and process text input from the Playground.\n    This component allows users to input text, which is then passed as a message for further processing.\n    \"\"\"\n\n    display_name = \"Prompt\"  # Display name for the component in the UI\n    description = \"Takes the prompt as input and pass it as a message.\"\n    icon = \"type\"  # Icon representing the component in the UI\n    name = \"TextInput\"  # Internal name of the component\n\n    # Define input fields\n    inputs = [\n        MultilineInput(\n            name=\"input_text\",  # Internal name for the input field\n            display_name=\"Input Text\",  # Display name for the input field\n            info=\"Enter the text to be processed.\",  # Help text for the input field\n        ),\n    ]\n\n    # Define output fields\n    outputs = [\n        Output(\n            display_name=\"Output Message\",  # Display name for the output\n            name=\"message_output\",  # Internal name for the output\n            method=\"generate_message\",  # Method to generate the output\n        ),\n    ]\n\n    def generate_message(self) -> Message:\n        \"\"\"\n        Generate a Message object containing the input text.\n\n        Returns:\n            Message: A Message object with the input text.\n        \"\"\"\n        return Message(text=self.input_text)", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "input_text": {"tool_mode": false, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "input_text", "value": " \"\"\" Your task is to determine whether the patient described in the note have depression or not   **Output Instructions:** provide your final answer (only the number \"1\" or \"0\") in this format **The final answer is: {{ 0 or 1}}**.      ---     ** Patient Note Summary:**     {}     Output the result (1 for depression present, or 0 otherwise) in this format **The final answer is: **.     \"\"\"", "display_name": "Input Text", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Enter the text to be processed.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}}, "description": "Takes the prompt as input and pass it as a message.", "icon": "type", "base_classes": ["Message"], "display_name": "Prompt", "documentation": "", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Message"], "selected": "Message", "name": "message_output", "hidden": null, "display_name": "Output Message", "method": "generate_message", "value": "__UNDEFINED__", "cache": true, "required_inputs": null, "allows_loop": false}], "field_order": ["input_text"], "beta": false, "legacy": false, "edited": true, "metadata": {}, "tool_mode": false, "lf_version": "1.1.3"}, "showNode": true, "type": "TextInput", "id": "TextInput-uweRT"}, "selected": false, "measured": {"width": 320, "height": 249}, "dragging": false}, {"id": "hyperparameter_config-cIW3u", "type": "genericNode", "position": {"x": 300.8679716494164, "y": 1239.9855302527137}, "data": {"node": {"template": {"_type": "Component", "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.base.io.text import TextComponent\nfrom langflow.io import MultilineInput, Output\nfrom langflow.schema.message import Message\n\n\nclass HyperparameterConfigInput(TextComponent):\n    \"\"\"\n    A component for inputting hyperparameter configuration for tuning.\n    \"\"\"\n    display_name = \"Hyperparameter Config\"\n    description = \"Input configuration for hyperparameter tuning.\"\n    icon = \"type\"\n    name = \"hyperparameter_config\"\n\n    inputs = [\n        MultilineInput(\n            name=\"config_input\",\n            display_name=\"Configuration\",\n            info=\"JSON-like configuration for hyperparameters.\",\n            value=\"\"\"{\n                \"learning_rate\": 10 ** random.uniform(-5, -3),\n                \"gradient_accumulation_steps\": random.choice([2, 4, 8, 16]),\n                \"epochs\": random.randint(1, 3),\n                \"weight_decay\": random.uniform(0.0, 0.2),\n                \"warmup_ratio\": random.uniform(0.0, 0.2)\n            }\"\"\"\n        ),\n    ]\n\n    outputs = [\n        Output(display_name=\"Configuration Message\", name=\"config_output\", method=\"generate_config_message\"),\n    ]\n\n    def generate_config_message(self) -> Message:\n        \"\"\"\n        Generates a message containing the hyperparameter configuration.\n        \"\"\"\n        return Message(\n            text=self.config_input,\n        )", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "config_input": {"tool_mode": false, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "config_input", "value": " {\n    \"learning_rate\": 4.6984521886770686e-05,\n    \"gradient_accumulation_steps\": 4,\n    \"epochs\": 2,\n    \"weight_decay\": 0.023938599209150646,\n    \"warmup_ratio\": 0.18764055202366062,\n}", "display_name": "Configuration", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "JSON-like configuration for hyperparameters.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}}, "description": "Input configuration for hyperparameter tuning.", "icon": "type", "base_classes": ["Message"], "display_name": "Hyperparameter Config", "documentation": "", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Message"], "selected": "Message", "name": "config_output", "hidden": null, "display_name": "Configuration Message", "method": "generate_config_message", "value": "__UNDEFINED__", "cache": true, "required_inputs": null, "allows_loop": false}], "field_order": ["config_input"], "beta": false, "legacy": false, "edited": true, "metadata": {}, "tool_mode": false, "lf_version": "1.1.3"}, "showNode": true, "type": "hyperparameter_config", "id": "hyperparameter_config-cIW3u"}, "selected": false, "measured": {"width": 320, "height": 229}, "dragging": false}], "edges": [{"source": "TextInput-135mX", "sourceHandle": "{œdataTypeœ:œTextInputœ,œidœ:œTextInput-135mXœ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}", "target": "PromptBuilder-5staL", "targetHandle": "{œfieldNameœ:œtokenizer_pathœ,œidœ:œPromptBuilder-5staLœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "data": {"targetHandle": {"fieldName": "tokenizer_path", "id": "PromptBuilder-5staL", "inputTypes": ["Message"], "type": "str"}, "sourceHandle": {"dataType": "TextInput", "id": "TextInput-135mX", "name": "text", "output_types": ["Message"]}}, "id": "reactflow__edge-TextInput-135mX{œdataTypeœ:œTextInputœ,œidœ:œTextInput-135mXœ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}-PromptBuilder-5staL{œfieldNameœ:œtokenizer_pathœ,œidœ:œPromptBuilder-5staLœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "animated": false, "className": ""}, {"source": "TextInput-uweRT", "sourceHandle": "{œdataTypeœ:œTextInputœ,œidœ:œTextInput-uweRTœ,œnameœ:œmessage_outputœ,œoutput_typesœ:[œMessageœ]}", "target": "PromptBuilder-5staL", "targetHandle": "{œfieldNameœ:œinput_templateœ,œidœ:œPromptBuilder-5staLœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "data": {"targetHandle": {"fieldName": "input_template", "id": "PromptBuilder-5staL", "inputTypes": ["Message"], "type": "str"}, "sourceHandle": {"dataType": "TextInput", "id": "TextInput-uweRT", "name": "message_output", "output_types": ["Message"]}}, "id": "reactflow__edge-TextInput-uweRT{œdataTypeœ:œTextInputœ,œidœ:œTextInput-uweRTœ,œnameœ:œmessage_outputœ,œoutput_typesœ:[œMessageœ]}-PromptBuilder-5staL{œfieldNameœ:œinput_templateœ,œidœ:œPromptBuilder-5staLœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "animated": false, "className": ""}, {"source": "ModelFineTuning-JkCoD", "sourceHandle": "{œdataTypeœ:œModelFineTuningœ,œidœ:œModelFineTuning-JkCoDœ,œnameœ:œfine_tuned_model_pathœ,œoutput_typesœ:[œMessageœ]}", "target": "ChatOutput-DUgnc", "targetHandle": "{œfieldNameœ:œinput_valueœ,œidœ:œChatOutput-DUgncœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "data": {"targetHandle": {"fieldName": "input_value", "id": "ChatOutput-DUgnc", "inputTypes": ["Message"], "type": "str"}, "sourceHandle": {"dataType": "ModelFineTuning", "id": "ModelFineTuning-JkCoD", "name": "fine_tuned_model_path", "output_types": ["Message"]}}, "id": "reactflow__edge-ModelFineTuning-JkCoD{œdataTypeœ:œModelFineTuningœ,œidœ:œModelFineTuning-JkCoDœ,œnameœ:œfine_tuned_model_pathœ,œoutput_typesœ:[œMessageœ]}-ChatOutput-DUgnc{œfieldNameœ:œinput_valueœ,œidœ:œChatOutput-DUgncœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "animated": false, "className": ""}, {"source": "DataExtractor-FSJDU", "sourceHandle": "{œdataTypeœ:œDataExtractorœ,œidœ:œDataExtractor-FSJDUœ,œnameœ:œpatient_notesœ,œoutput_typesœ:[œDataFrameœ]}", "target": "PromptBuilder-5staL", "targetHandle": "{œfieldNameœ:œnote_dataœ,œidœ:œPromptBuilder-5staLœ,œinputTypesœ:[œDataFrameœ],œtypeœ:œotherœ}", "data": {"targetHandle": {"fieldName": "note_data", "id": "PromptBuilder-5staL", "inputTypes": ["DataFrame"], "type": "other"}, "sourceHandle": {"dataType": "DataExtractor", "id": "DataExtractor-FSJDU", "name": "patient_notes", "output_types": ["DataFrame"]}}, "id": "reactflow__edge-DataExtractor-FSJDU{œdataTypeœ:œDataExtractorœ,œidœ:œDataExtractor-FSJDUœ,œnameœ:œpatient_notesœ,œoutput_typesœ:[œDataFrameœ]}-PromptBuilder-5staL{œfieldNameœ:œnote_dataœ,œidœ:œPromptBuilder-5staLœ,œinputTypesœ:[œDataFrameœ],œtypeœ:œotherœ}", "animated": false, "className": ""}, {"source": "DataExtractor-FSJDU", "sourceHandle": "{œdataTypeœ:œDataExtractorœ,œidœ:œDataExtractor-FSJDUœ,œnameœ:œdepression_labelsœ,œoutput_typesœ:[œDataFrameœ]}", "target": "PromptBuilder-5staL", "targetHandle": "{œfieldNameœ:œlabel_dataœ,œidœ:œPromptBuilder-5staLœ,œinputTypesœ:[œDataFrameœ],œtypeœ:œotherœ}", "data": {"targetHandle": {"fieldName": "label_data", "id": "PromptBuilder-5staL", "inputTypes": ["DataFrame"], "type": "other"}, "sourceHandle": {"dataType": "DataExtractor", "id": "DataExtractor-FSJDU", "name": "depression_labels", "output_types": ["DataFrame"]}}, "id": "reactflow__edge-DataExtractor-FSJDU{œdataTypeœ:œDataExtractorœ,œidœ:œDataExtractor-FSJDUœ,œnameœ:œdepression_labelsœ,œoutput_typesœ:[œDataFrameœ]}-PromptBuilder-5staL{œfieldNameœ:œlabel_dataœ,œidœ:œPromptBuilder-5staLœ,œinputTypesœ:[œDataFrameœ],œtypeœ:œotherœ}", "animated": false, "className": ""}, {"source": "TextInput-EUxil", "sourceHandle": "{œdataTypeœ:œTextInputœ,œidœ:œTextInput-EUxilœ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}", "target": "DataExtractor-FSJDU", "targetHandle": "{œfieldNameœ:œpatient_notes_pathœ,œidœ:œDataExtractor-FSJDUœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "data": {"targetHandle": {"fieldName": "patient_notes_path", "id": "DataExtractor-FSJDU", "inputTypes": ["Message"], "type": "str"}, "sourceHandle": {"dataType": "TextInput", "id": "TextInput-EUxil", "name": "text", "output_types": ["Message"]}}, "id": "reactflow__edge-TextInput-EUxil{œdataTypeœ:œTextInputœ,œidœ:œTextInput-EUxilœ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}-DataExtractor-FSJDU{œfieldNameœ:œpatient_notes_pathœ,œidœ:œDataExtractor-FSJDUœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "animated": false, "className": ""}, {"source": "TextInput-ZMwVx", "sourceHandle": "{œdataTypeœ:œTextInputœ,œidœ:œTextInput-ZMwVxœ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}", "target": "DataExtractor-FSJDU", "targetHandle": "{œfieldNameœ:œdepression_labels_pathœ,œidœ:œDataExtractor-FSJDUœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "data": {"targetHandle": {"fieldName": "depression_labels_path", "id": "DataExtractor-FSJDU", "inputTypes": ["Message"], "type": "str"}, "sourceHandle": {"dataType": "TextInput", "id": "TextInput-ZMwVx", "name": "text", "output_types": ["Message"]}}, "id": "reactflow__edge-TextInput-ZMwVx{œdataTypeœ:œTextInputœ,œidœ:œTextInput-ZMwVxœ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}-DataExtractor-FSJDU{œfieldNameœ:œdepression_labels_pathœ,œidœ:œDataExtractor-FSJDUœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "animated": false, "className": ""}, {"source": "PromptBuilder-5staL", "sourceHandle": "{œdataTypeœ:œPromptBuilderœ,œidœ:œPromptBuilder-5staLœ,œnameœ:œformatted_promptsœ,œoutput_typesœ:[œDataFrameœ]}", "target": "ModelFineTuning-JkCoD", "targetHandle": "{œfieldNameœ:œpromptsœ,œidœ:œModelFineTuning-JkCoDœ,œinputTypesœ:[œDataFrameœ],œtypeœ:œotherœ}", "data": {"targetHandle": {"fieldName": "prompts", "id": "ModelFineTuning-JkCoD", "inputTypes": ["DataFrame"], "type": "other"}, "sourceHandle": {"dataType": "PromptBuilder", "id": "PromptBuilder-5staL", "name": "formatted_prompts", "output_types": ["DataFrame"]}}, "id": "xy-edge__PromptBuilder-5staL{œdataTypeœ:œPromptBuilderœ,œidœ:œPromptBuilder-5staLœ,œnameœ:œformatted_promptsœ,œoutput_typesœ:[œDataFrameœ]}-ModelFineTuning-JkCoD{œfieldNameœ:œpromptsœ,œidœ:œModelFineTuning-JkCoDœ,œinputTypesœ:[œDataFrameœ],œtypeœ:œotherœ}", "animated": false, "className": ""}, {"source": "hyperparameter_config-cIW3u", "sourceHandle": "{œdataTypeœ:œhyperparameter_configœ,œidœ:œhyperparameter_config-cIW3uœ,œnameœ:œconfig_outputœ,œoutput_typesœ:[œMessageœ]}", "target": "ModelFineTuning-JkCoD", "targetHandle": "{œfieldNameœ:œtraining_configœ,œidœ:œModelFineTuning-JkCoDœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "data": {"targetHandle": {"fieldName": "training_config", "id": "ModelFineTuning-JkCoD", "inputTypes": ["Message"], "type": "str"}, "sourceHandle": {"dataType": "hyperparameter_config", "id": "hyperparameter_config-cIW3u", "name": "config_output", "output_types": ["Message"]}}, "id": "xy-edge__hyperparameter_config-cIW3u{œdataTypeœ:œhyperparameter_configœ,œidœ:œhyperparameter_config-cIW3uœ,œnameœ:œconfig_outputœ,œoutput_typesœ:[œMessageœ]}-ModelFineTuning-JkCoD{œfieldNameœ:œtraining_configœ,œidœ:œModelFineTuning-JkCoDœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "animated": false, "className": ""}, {"source": "TextInput-135mX", "sourceHandle": "{œdataTypeœ:œTextInputœ,œidœ:œTextInput-135mXœ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}", "target": "ModelFineTuning-JkCoD", "targetHandle": "{œfieldNameœ:œmodel_pathœ,œidœ:œModelFineTuning-JkCoDœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "data": {"targetHandle": {"fieldName": "model_path", "id": "ModelFineTuning-JkCoD", "inputTypes": ["Message"], "type": "str"}, "sourceHandle": {"dataType": "TextInput", "id": "TextInput-135mX", "name": "text", "output_types": ["Message"]}}, "id": "xy-edge__TextInput-135mX{œdataTypeœ:œTextInputœ,œidœ:œTextInput-135mXœ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}-ModelFineTuning-JkCoD{œfieldNameœ:œmodel_pathœ,œidœ:œModelFineTuning-JkCoDœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "animated": false, "className": ""}], "viewport": {"x": 282.63068313132646, "y": 21.557683830841626, "zoom": 0.43128380646753817}}}