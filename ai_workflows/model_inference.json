{"updated_at": "2025-03-17T15:16:04+00:00", "webhook": false, "description": "This flow takes the path of a trained model and a test dataset (comprising patient_notes and corresponding depression_labels) as inputs. It performs inference on the test dataset using the provided model, calculates evaluation metrics such as precision, recall, and accuracy, and returns the accuracy as output.", "icon": null, "locked": false, "name": "model_inference", "endpoint_name": null, "tags": null, "gradient": null, "id": "88c50903-a975-434f-8e58-9804fcce1c89", "is_component": false, "user_id": "852a84df-3c42-4c14-a7a3-a13e5c7b8489", "folder_id": "1b8531fb-dac2-4960-b8f7-a22ccb1403f1", "icon_bg_color": null, "data": {"nodes": [{"id": "model_inference-TnPMH", "type": "genericNode", "position": {"x": 942.3820293891763, "y": 8.764917825910828}, "data": {"node": {"template": {"_type": "Component", "patient_notes": {"tool_mode": false, "trace_as_metadata": true, "list": false, "list_add_label": "Add More", "trace_as_input": true, "required": true, "placeholder": "", "show": true, "name": "patient_notes", "value": "", "display_name": "Patient Notes", "advanced": false, "input_types": ["DataFrame"], "dynamic": false, "info": "A DataFrame containing patient notes.", "title_case": false, "type": "other", "_input_type": "DataFrameInput"}, "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.custom import Component\nfrom langflow.io import DataFrameInput, MultilineInput, MessageTextInput, Output\nfrom langflow.schema import DataFrame\nfrom tqdm import tqdm\nfrom unsloth import FastLanguageModel\nimport pandas as pd\nimport gc\nimport torch\n\n\nclass ModelInference(Component):\n    \"\"\"\n    A component to perform inference using a fine-tuned language model.\n    This component takes patient notes, formats them into prompts, and generates model predictions.\n    \"\"\"\n\n    display_name = \"Model Inference\"  # Display name for the component in the UI\n    description = \"\"\"\n    Perform inference using a fine-tuned language model.\n    This component formats patient notes into prompts, generates predictions, and returns the results.\n    \"\"\"\n    documentation: str = \"http://docs.langflow.org/components/custom\"  # Link to documentation\n    icon = \"code\"  # Icon representing the component in the UI\n    name = \"ModelInference\"  # Internal name of the component\n\n    # Define input fields\n    inputs = [\n        DataFrameInput(\n            name=\"patient_notes\",  # Internal name for the patient notes input\n            display_name=\"Patient Notes\",  # Display name for the patient notes input\n            required=True,  # This input is mandatory\n            info=\"A DataFrame containing patient notes.\",\n        ),\n        MultilineInput(\n            name=\"input_prompt_template\",  # Internal name for the input prompt template\n            display_name=\"Input Prompt Template\",  # Display name for the input prompt template\n            info=\"Template for the input prompt. Use `{}` as a placeholder for the patient note.\",\n        ),\n        MessageTextInput(\n            name=\"model_path\",  # Internal name for the model path input\n            display_name=\"Model Path\",  # Display name for the model path input\n            info=\"Path to the fine-tuned model.\",  # Help text for the input\n            value=\"finetuned_model\",  # Default value for the model path\n        ),\n    ]\n\n    # Define output fields\n    outputs = [\n        Output(\n            display_name=\"Model Predictions\",  # Display name for the output\n            name=\"model_predictions\",  # Internal name for the output\n            method=\"generate_predictions\",  # Method to generate the output\n        ),\n    ]\n\n    def _convert_label(self, output_text: str) -> int:\n        \"\"\"\n        Convert the model's output text into a binary label (0 or 1).\n\n        Args:\n            output_text (str): The model's output text.\n\n        Returns:\n            int: The converted label (0 or 1).\n        \"\"\"\n        # Extract the last part of the output text and check for binary labels\n        label = output_text.split(\":\")[-1].strip()\n        for char in label:\n            if char in {\"0\", \"1\"}:\n                return int(char)\n        return output_text  # Return the original text if no binary label is found\n\n    def _generate_predictions(self, model, tokenizer, notes: list, device: str) -> list:\n        \"\"\"\n        Generate predictions for the given patient notes using the fine-tuned model.\n\n        Args:\n            model: The fine-tuned language model.\n            tokenizer: The tokenizer for the model.\n            notes (list): A list of patient notes.\n            device (str): The device to run the model on (e.g., \"cuda\" or \"cpu\").\n\n        Returns:\n            list: A list of model predictions.\n        \"\"\"\n        predictions = []\n        for note in tqdm(notes, desc=\"Generating predictions\"):\n            # Format the input prompt using the patient note\n            prompt = self.input_prompt_template.format(note)\n            chat = [{\"role\": \"user\", \"content\": prompt}]\n            formatted_input = tokenizer.apply_chat_template(chat, tokenize=False)\n\n            # Tokenize the input and generate predictions\n            tokenized_input = tokenizer(formatted_input, padding=True, truncation=True, return_tensors=\"pt\").to(device)\n            input_length = tokenized_input[\"input_ids\"].shape[1]\n            output = model.generate(**tokenized_input, max_new_tokens=200)\n            generated_tokens = output[0, input_length:]\n            output_text = tokenizer.decode(generated_tokens, skip_special_tokens=True)\n\n            # Convert the output text to a label and add to predictions\n            predictions.append(output_text)\n        return predictions\n\n    def generate_predictions(self) -> DataFrame:\n        \"\"\"\n        Perform inference using the fine-tuned model and return the predictions.\n\n        Returns:\n            DataFrame: A DataFrame containing the model's predictions.\n        \"\"\"\n        # Set the device (GPU if available, otherwise CPU)\n        device = \"cuda\" if torch.cuda.is_available() else \"cpu\"\n\n        # Clear GPU memory if a model is already loaded\n        if \"model\" in globals():\n            del model\n        if torch.cuda.is_available():\n            torch.cuda.empty_cache()\n\n        # Load the fine-tuned model and tokenizer\n        model, tokenizer = FastLanguageModel.from_pretrained(\n            self.model_path,\n            load_in_4bit=True,\n            device_map=\"auto\",\n            max_seq_length=17000\n        )\n        model = FastLanguageModel.for_inference(model)\n\n        # Extract patient notes and generate predictions\n        patient_notes = self.patient_notes[\"note\"]\n        predictions = self._generate_predictions(model, tokenizer, patient_notes, device)\n\n        # Clean up resources\n        del model\n        del tokenizer\n        gc.collect()\n        if torch.cuda.is_available():\n            torch.cuda.empty_cache()\n\n        # Return the predictions as a DataFrame\n        return DataFrame(pd.DataFrame(predictions, columns=[\"model_output\"]))", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "input_prompt_template": {"tool_mode": false, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "input_prompt_template", "value": "", "display_name": "Input Prompt Template", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Template for the input prompt. Use `{}` as a placeholder for the patient note.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}, "model_path": {"tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "model_path", "value": "", "display_name": "Model Path", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Path to the fine-tuned model.", "title_case": false, "type": "str", "_input_type": "MessageTextInput"}}, "description": "\n    Perform inference using a fine-tuned language model.\n    This component formats patient notes into prompts, generates predictions, and returns the results.\n    ", "icon": "code", "base_classes": ["DataFrame"], "display_name": "Model Inference", "documentation": "http://docs.langflow.org/components/custom", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": true, "outputs": [{"types": ["DataFrame"], "selected": "DataFrame", "name": "model_predictions", "hidden": null, "display_name": "Model Predictions", "method": "generate_predictions", "value": "__UNDEFINED__", "cache": true, "required_inputs": null, "allows_loop": false}], "field_order": ["patient_notes", "input_prompt_template", "model_path"], "beta": false, "legacy": false, "edited": true, "metadata": {}, "tool_mode": false, "lf_version": "1.1.3"}, "showNode": true, "type": "ModelInference", "id": "model_inference-TnPMH"}, "selected": false, "measured": {"width": 320, "height": 452}, "dragging": false}, {"id": "DataExtractor-jXECy", "type": "genericNode", "position": {"x": 537.547724416075, "y": 14.603310261472146}, "data": {"node": {"template": {"_type": "Component", "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.custom import Component\nfrom langflow.io import MultilineInput, Output\nfrom langflow.schema import DataFrame\nimport pandas as pd\nimport os\n\n\nclass DataExtractor(Component):\n    \"\"\"\n    A component to extract and process patient notes (X) and depression labels (Y) from CSV files.\n    This component reads the input files, extracts the relevant columns, and outputs them as DataFrames.\n    \"\"\"\n\n    display_name = \"Data Parser\"  # Display name for the component in the UI\n    description = \"\"\"\n    Extract patient notes (X) and depression labels (Y) from CSV files.\n    The component reads the input files, processes them, and outputs the extracted data as DataFrames.\n    \"\"\"\n    documentation: str = \"http://docs.langflow.org/components/custom\"  # Link to documentation\n    icon = \"code\"  # Icon representing the component in the UI\n    name = \"DataExtractor\"  # Internal name of the component\n\n    # Define input fields\n    inputs = [\n        MultilineInput(\n            name=\"patient_notes_path\",  # Internal name for the patient notes file path\n            display_name=\"Patient Notes Path\",  # Display name for the patient notes file path\n            required=True,  # This input is mandatory\n            info=\"Path to the CSV file containing patient notes.\",\n        ),\n        MultilineInput(\n            name=\"depression_labels_path\",  # Internal name for the depression labels file path\n            display_name=\"Depression Labels Path\",  # Display name for the depression labels file path\n            required=True,  # This input is mandatory\n            info=\"Path to the CSV file containing depression labels.\",\n        ),\n    ]\n\n    # Define output fields\n    outputs = [\n        Output(\n            display_name=\"Patient Notes\",  # Display name for the patient notes output\n            name=\"patient_notes\",  # Internal name for the patient notes output\n            method=\"extract_patient_notes\",  # Method to generate the output\n        ),\n        Output(\n            display_name=\"Depression Labels\",  # Display name for the depression labels output\n            name=\"depression_labels\",  # Internal name for the depression labels output\n            method=\"extract_depression_labels\",  # Method to generate the output\n        ),\n    ]\n\n    def _read_csv_file(self, file_path: str) -> pd.DataFrame:\n        \"\"\"\n        Read a CSV file and return its contents as a DataFrame.\n\n        Args:\n            file_path (str): Path to the CSV file.\n\n        Returns:\n            pd.DataFrame: DataFrame containing the file data.\n\n        Raises:\n            ValueError: If the file cannot be read.\n        \"\"\"\n        try:\n            return pd.read_csv(file_path)\n        except Exception as e:\n            raise ValueError(f\"Failed to read the file at {file_path}: {e}\")\n\n    def extract_patient_notes(self) -> DataFrame:\n        \"\"\"\n        Extract patient notes from the input CSV file.\n\n        Returns:\n            DataFrame: A DataFrame containing the patient notes.\n        \"\"\"\n        df = self._read_csv_file(self.patient_notes_path)\n        return DataFrame({\"note\": df[\"note\"]})  # Extract the \"note\" column\n\n    def extract_depression_labels(self) -> DataFrame:\n        \"\"\"\n        Extract depression labels from the input CSV file.\n\n        Returns:\n            DataFrame: A DataFrame containing the depression labels.\n        \"\"\"\n        df = self._read_csv_file(self.depression_labels_path)\n        return DataFrame({\"label\": df[\"label\"]})  # Extract the \"label\" column", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "depression_labels_path": {"tool_mode": false, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": true, "placeholder": "", "show": true, "name": "depression_labels_path", "value": "", "display_name": "Depression Labels Path", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Path to the CSV file containing depression labels.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}, "patient_notes_path": {"tool_mode": false, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": true, "placeholder": "", "show": true, "name": "patient_notes_path", "value": "", "display_name": "Patient Notes Path", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Path to the CSV file containing patient notes.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}}, "description": "\n    Extract patient notes (X) and depression labels (Y) from CSV files.\n    The component reads the input files, processes them, and outputs the extracted data as DataFrames.\n    ", "icon": "code", "base_classes": ["DataFrame"], "display_name": "Data Parser", "documentation": "http://docs.langflow.org/components/custom", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": true, "outputs": [{"types": ["DataFrame"], "selected": "DataFrame", "name": "patient_notes", "hidden": null, "display_name": "Patient Notes", "method": "extract_patient_notes", "value": "__UNDEFINED__", "cache": true, "required_inputs": null, "allows_loop": false}, {"types": ["DataFrame"], "selected": "DataFrame", "name": "depression_labels", "hidden": null, "display_name": "Depression Labels", "method": "extract_depression_labels", "value": "__UNDEFINED__", "cache": true, "required_inputs": null, "allows_loop": false}], "field_order": ["patient_notes_path", "depression_labels_path"], "beta": false, "legacy": false, "edited": true, "metadata": {}, "tool_mode": false, "lf_version": "1.1.3"}, "showNode": true, "type": "DataExtractor", "id": "DataExtractor-jXECy"}, "selected": false, "measured": {"width": 320, "height": 456}, "dragging": false}, {"id": "CustomComponent-czkiY", "type": "genericNode", "position": {"x": 1352.2164920318344, "y": 79.50874907326579}, "data": {"node": {"template": {"_type": "Component", "model_predictions": {"tool_mode": false, "trace_as_metadata": true, "list": false, "list_add_label": "Add More", "trace_as_input": true, "required": true, "placeholder": "", "show": true, "name": "model_predictions", "value": "", "display_name": "Model Predictions", "advanced": false, "input_types": ["DataFrame"], "dynamic": false, "info": "A DataFrame containing the model's predictions.", "title_case": false, "type": "other", "_input_type": "DataFrameInput"}, "true_labels": {"tool_mode": false, "trace_as_metadata": true, "list": false, "list_add_label": "Add More", "trace_as_input": true, "required": true, "placeholder": "", "show": true, "name": "true_labels", "value": "", "display_name": "True Labels", "advanced": false, "input_types": ["DataFrame"], "dynamic": false, "info": "A DataFrame containing the true labels.", "title_case": false, "type": "other", "_input_type": "DataFrameInput"}, "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.custom import Component\nfrom langflow.io import DataFrameInput, Output\nfrom langflow.schema import Data\nimport matplotlib.pyplot as plt\nfrom sklearn.metrics import (\n    accuracy_score,\n    precision_score,\n    recall_score,\n    f1_score,\n    confusion_matrix,\n    ConfusionMatrixDisplay,\n    classification_report,\n)\n\nimport os\n\n\nclass EvaluateModel(Component):\n    \"\"\"\n    A component to evaluate the performance of a classification model.\n    This component computes metrics such as accuracy, precision, recall, F1-score,\n    and generates a confusion matrix and classification report.\n    \"\"\"\n\n    display_name = \"Model Evaluation\"  # Display name for the component in the UI\n    description = \"\"\"\n    Evaluate the performance of a model by comparing true labels with model predictions.\n    This component computes metrics such as accuracy, precision, recall, F1-score, and generates a confusion matrix.\n    \"\"\"\n    documentation: str = \"http://docs.langflow.org/components/custom\"  # Link to documentation\n    icon = \"code\"  # Icon representing the component in the UI\n    name = \"EvaluateModel\"  # Internal name of the component\n\n    # Define input fields\n    inputs = [\n        DataFrameInput(\n            name=\"true_labels\",  # Internal name for the true labels input\n            display_name=\"True Labels\",  # Display name for the true labels input\n            required=True,  # This input is mandatory\n            info=\"A DataFrame containing the true labels.\",\n        ),\n        DataFrameInput(\n            name=\"model_predictions\",  # Internal name for the model predictions input\n            display_name=\"Model Predictions\",  # Display name for the model predictions input\n            required=True,  # This input is mandatory\n            info=\"A DataFrame containing the model's predictions.\",\n        ),\n    ]\n\n    # Define output fields\n    outputs = [\n        Output(\n            display_name=\"Evaluation Results\",  # Display name for the output\n            name=\"evaluation_results\",  # Internal name for the output\n            method=\"evaluate_model\",  # Method to generate the output\n        ),\n    ]\n\n    def _convert_predictions(self, predictions: list) -> list:\n        \"\"\"\n        Convert model predictions into a consistent format (0 or 1).\n        Predictions that cannot be converted are treated as unsure.\n\n        Args:\n            predictions (list): A list of model predictions.\n\n        Returns:\n            list: A list of converted predictions (0 or 1).\n        \"\"\"\n        converted = []\n        \n        for pred in predictions:\n            \n            try:\n                # Extract the last part of the prediction string and convert to integer\n                c = pred.split(\":\")[-1].strip()\n                for char in c:\n                    if char in {\"0\", \"1\"}:\n                        label = int(char)\n                        converted.append(label)\n                        break\n            except (ValueError, AttributeError):\n                # If conversion fails, treat as unsure (0)\n                converted.append(0)\n        return converted\n\n    def evaluate_model(self) -> Data:\n        \"\"\"\n        Evaluate the model's performance by comparing true labels with predictions.\n\n        Returns:\n            Data: A Data object containing evaluation metrics, classification report,\n                  confusion matrix, and unsure predictions.\n        \"\"\"\n        # Extract true labels and model predictions\n        true_labels = list(self.true_labels[\"label\"])\n        model_predictions = list(self.model_predictions[\"model_output\"])\n\n        # Convert predictions to a consistent format\n        converted_predictions = self._convert_predictions(model_predictions)\n\n        # Compute evaluation metrics\n        accuracy = accuracy_score(true_labels, converted_predictions)\n        precision = precision_score(true_labels, converted_predictions)\n        recall = recall_score(true_labels, converted_predictions)\n        f1 = f1_score(true_labels, converted_predictions)\n        conf_matrix = confusion_matrix(true_labels, converted_predictions)\n        report = classification_report(true_labels, converted_predictions)\n\n        # Generate and save the confusion matrix plot\n        cm_display = ConfusionMatrixDisplay(confusion_matrix=conf_matrix, display_labels=[0, 1])\n        cm_display.plot(cmap=\"Blues\")\n        if not os.path.exists(\"report\"):\n            os.mkdir(\"report\")\n        \n        plt.title(f\"confusion Matrix\\nAccuracy: {accuracy:.2f}\")\n        plt.savefig(\"report/confusion_matrix.png\", bbox_inches=\"tight\", pad_inches=0.5)\n        plt.show()\n\n        # Prepare the evaluation results\n        evaluation_results = {\n            \"accuracy\": accuracy,\n            \"precision\": precision,\n            \"recall\": recall,\n            \"f1\": f1,\n            \"classification_report\": report,\n            \"confusion_matrix\": conf_matrix.tolist(),  # Convert numpy array to list for serialization\n            \"true_labels\": true_labels,\n            \"predictions\": converted_predictions,\n        }\n\n        # Return the results as a Data object\n        return Data(data=evaluation_results)", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}}, "description": "\n    Evaluate the performance of a model by comparing true labels with model predictions.\n    This component computes metrics such as accuracy, precision, recall, F1-score, and generates a confusion matrix.\n    ", "icon": "code", "base_classes": ["Data"], "display_name": "Model Evaluation", "documentation": "http://docs.langflow.org/components/custom", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Data"], "selected": "Data", "name": "evaluation_results", "hidden": null, "display_name": "Evaluation Results", "method": "evaluate_model", "value": "__UNDEFINED__", "cache": true, "required_inputs": null, "allows_loop": false}], "field_order": ["true_labels", "model_predictions"], "beta": false, "legacy": false, "edited": true, "metadata": {}, "tool_mode": false, "lf_version": "1.1.3"}, "showNode": true, "type": "EvaluateModel", "id": "CustomComponent-czkiY"}, "selected": false, "measured": {"width": 320, "height": 371}, "dragging": false}, {"id": "ParseData-5Ywdi", "type": "genericNode", "position": {"x": 1725.6500709754432, "y": 79.45389512370323}, "data": {"node": {"template": {"_type": "Component", "data": {"tool_mode": false, "trace_as_metadata": true, "list": true, "list_add_label": "Add More", "trace_as_input": true, "required": true, "placeholder": "", "show": true, "name": "data", "value": "", "display_name": "Data", "advanced": false, "input_types": ["Data"], "dynamic": false, "info": "The data to convert to text.", "title_case": false, "type": "other", "_input_type": "DataInput"}, "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.custom import Component\nfrom langflow.helpers.data import data_to_text, data_to_text_list\nfrom langflow.io import DataInput, MultilineInput, Output, StrInput\nfrom langflow.schema import Data\nfrom langflow.schema.message import Message\n\n\nclass ParseDataComponent(Component):\n    display_name = \"Data to Message\"\n    description = \"Convert Data objects into Messages using any {field_name} from input data.\"\n    icon = \"message-square\"\n    name = \"ParseData\"\n\n    inputs = [\n        DataInput(name=\"data\", display_name=\"Data\", info=\"The data to convert to text.\", is_list=True, required=True),\n        MultilineInput(\n            name=\"template\",\n            display_name=\"Template\",\n            info=\"The template to use for formatting the data. \"\n            \"It can contain the keys {text}, {data} or any other key in the Data.\",\n            value=\"{text}\",\n            required=True,\n        ),\n        StrInput(name=\"sep\", display_name=\"Separator\", advanced=True, value=\"\\n\"),\n    ]\n\n    outputs = [\n        Output(\n            display_name=\"Message\",\n            name=\"text\",\n            info=\"Data as a single Message, with each input Data separated by Separator\",\n            method=\"parse_data\",\n        ),\n        Output(\n            display_name=\"Data List\",\n            name=\"data_list\",\n            info=\"Data as a list of new Data, each having `text` formatted by Template\",\n            method=\"parse_data_as_list\",\n        ),\n    ]\n\n    def _clean_args(self) -> tuple[list[Data], str, str]:\n        data = self.data if isinstance(self.data, list) else [self.data]\n        template = self.template\n        sep = self.sep\n        return data, template, sep\n\n    def parse_data(self) -> Message:\n        data, template, sep = self._clean_args()\n        result_string = data_to_text(template, data, sep)\n        self.status = result_string\n        return Message(text=result_string)\n\n    def parse_data_as_list(self) -> list[Data]:\n        data, template, _ = self._clean_args()\n        text_list, data_list = data_to_text_list(template, data)\n        for item, text in zip(data_list, text_list, strict=True):\n            item.set_text(text)\n        self.status = data_list\n        return data_list\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "sep": {"tool_mode": false, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "sep", "value": "\n", "display_name": "Separator", "advanced": true, "dynamic": false, "info": "", "title_case": false, "type": "str", "_input_type": "StrInput"}, "template": {"tool_mode": false, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": true, "placeholder": "", "show": true, "name": "template", "value": "{accuracy}", "display_name": "Template", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "The template to use for formatting the data. It can contain the keys {text}, {data} or any other key in the Data.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}}, "description": "Convert Data objects into Messages using any {field_name} from input data.", "icon": "message-square", "base_classes": ["Data", "Message"], "display_name": "Data to Message", "documentation": "", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Message"], "selected": "Message", "name": "text", "display_name": "Message", "method": "parse_data", "value": "__UNDEFINED__", "cache": true, "allows_loop": false, "tool_mode": true}, {"types": ["Data"], "selected": "Data", "name": "data_list", "display_name": "Data List", "method": "parse_data_as_list", "value": "__UNDEFINED__", "cache": true, "allows_loop": false, "tool_mode": true}], "field_order": ["data", "template", "sep"], "beta": false, "legacy": false, "edited": false, "metadata": {}, "tool_mode": false, "lf_version": "1.1.3"}, "showNode": true, "type": "ParseData", "id": "ParseData-5Ywdi"}, "selected": true, "measured": {"width": 320, "height": 341}, "dragging": false}, {"id": "ChatOutput-1M74S", "type": "genericNode", "position": {"x": 2129.495846932144, "y": 287.3496994738004}, "data": {"node": {"template": {"_type": "Component", "background_color": {"tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "background_color", "value": "", "display_name": "Background Color", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "The background color of the icon.", "title_case": false, "type": "str", "_input_type": "MessageTextInput"}, "chat_icon": {"tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "chat_icon", "value": "", "display_name": "Icon", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "The icon of the message.", "title_case": false, "type": "str", "_input_type": "MessageTextInput"}, "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.base.io.chat import ChatComponent\nfrom langflow.inputs import BoolInput\nfrom langflow.io import DropdownInput, MessageInput, MessageTextInput, Output\nfrom langflow.schema.message import Message\nfrom langflow.schema.properties import Source\nfrom langflow.utils.constants import (\n    MESSAGE_SENDER_AI,\n    MESSAGE_SENDER_NAME_AI,\n    MESSAGE_SENDER_USER,\n)\n\n\nclass ChatOutput(ChatComponent):\n    display_name = \"Chat Output\"\n    description = \"Display a chat message in the Playground.\"\n    icon = \"MessagesSquare\"\n    name = \"ChatOutput\"\n    minimized = True\n\n    inputs = [\n        MessageInput(\n            name=\"input_value\",\n            display_name=\"Text\",\n            info=\"Message to be passed as output.\",\n        ),\n        BoolInput(\n            name=\"should_store_message\",\n            display_name=\"Store Messages\",\n            info=\"Store the message in the history.\",\n            value=True,\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"sender\",\n            display_name=\"Sender Type\",\n            options=[MESSAGE_SENDER_AI, MESSAGE_SENDER_USER],\n            value=MESSAGE_SENDER_AI,\n            advanced=True,\n            info=\"Type of sender.\",\n        ),\n        MessageTextInput(\n            name=\"sender_name\",\n            display_name=\"Sender Name\",\n            info=\"Name of the sender.\",\n            value=MESSAGE_SENDER_NAME_AI,\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"session_id\",\n            display_name=\"Session ID\",\n            info=\"The session ID of the chat. If empty, the current session ID parameter will be used.\",\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"data_template\",\n            display_name=\"Data Template\",\n            value=\"{text}\",\n            advanced=True,\n            info=\"Template to convert Data to Text. If left empty, it will be dynamically set to the Data's text key.\",\n        ),\n        MessageTextInput(\n            name=\"background_color\",\n            display_name=\"Background Color\",\n            info=\"The background color of the icon.\",\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"chat_icon\",\n            display_name=\"Icon\",\n            info=\"The icon of the message.\",\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"text_color\",\n            display_name=\"Text Color\",\n            info=\"The text color of the name\",\n            advanced=True,\n        ),\n    ]\n    outputs = [\n        Output(\n            display_name=\"Message\",\n            name=\"message\",\n            method=\"message_response\",\n        ),\n    ]\n\n    def _build_source(self, id_: str | None, display_name: str | None, source: str | None) -> Source:\n        source_dict = {}\n        if id_:\n            source_dict[\"id\"] = id_\n        if display_name:\n            source_dict[\"display_name\"] = display_name\n        if source:\n            source_dict[\"source\"] = source\n        return Source(**source_dict)\n\n    async def message_response(self) -> Message:\n        source, icon, display_name, source_id = self.get_properties_from_source_component()\n        background_color = self.background_color\n        text_color = self.text_color\n        if self.chat_icon:\n            icon = self.chat_icon\n        message = self.input_value if isinstance(self.input_value, Message) else Message(text=self.input_value)\n        message.sender = self.sender\n        message.sender_name = self.sender_name\n        message.session_id = self.session_id\n        message.flow_id = self.graph.flow_id if hasattr(self, \"graph\") else None\n        message.properties.source = self._build_source(source_id, display_name, source)\n        message.properties.icon = icon\n        message.properties.background_color = background_color\n        message.properties.text_color = text_color\n        if self.session_id and isinstance(message, Message) and self.should_store_message:\n            stored_message = await self.send_message(\n                message,\n            )\n            self.message.value = stored_message\n            message = stored_message\n\n        self.status = message\n        return message\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "data_template": {"tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "data_template", "value": "{text}", "display_name": "Data Template", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "Template to convert Data to Text. If left empty, it will be dynamically set to the Data's text key.", "title_case": false, "type": "str", "_input_type": "MessageTextInput"}, "input_value": {"trace_as_input": true, "tool_mode": false, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "input_value", "value": "", "display_name": "Text", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Message to be passed as output.", "title_case": false, "type": "str", "_input_type": "MessageInput"}, "sender": {"tool_mode": false, "trace_as_metadata": true, "options": ["Machine", "User"], "options_metadata": [], "combobox": false, "dialog_inputs": {}, "required": false, "placeholder": "", "show": true, "name": "sender", "value": "Machine", "display_name": "Sender Type", "advanced": true, "dynamic": false, "info": "Type of sender.", "title_case": false, "type": "str", "_input_type": "DropdownInput"}, "sender_name": {"tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "sender_name", "value": "AI", "display_name": "Sender Name", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "Name of the sender.", "title_case": false, "type": "str", "_input_type": "MessageTextInput"}, "session_id": {"tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "session_id", "value": "", "display_name": "Session ID", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "The session ID of the chat. If empty, the current session ID parameter will be used.", "title_case": false, "type": "str", "_input_type": "MessageTextInput"}, "should_store_message": {"tool_mode": false, "trace_as_metadata": true, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "should_store_message", "value": true, "display_name": "Store Messages", "advanced": true, "dynamic": false, "info": "Store the message in the history.", "title_case": false, "type": "bool", "_input_type": "BoolInput"}, "text_color": {"tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "text_color", "value": "", "display_name": "Text Color", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "The text color of the name", "title_case": false, "type": "str", "_input_type": "MessageTextInput"}}, "description": "Display a chat message in the Playground.", "icon": "MessagesSquare", "base_classes": ["Message"], "display_name": "Chat Output", "documentation": "", "minimized": true, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Message"], "selected": "Message", "name": "message", "display_name": "Message", "method": "message_response", "value": "__UNDEFINED__", "cache": true, "allows_loop": false, "tool_mode": true}], "field_order": ["input_value", "should_store_message", "sender", "sender_name", "session_id", "data_template", "background_color", "chat_icon", "text_color"], "beta": false, "legacy": false, "edited": false, "metadata": {}, "tool_mode": false}, "showNode": false, "type": "ChatOutput", "id": "ChatOutput-1M74S"}, "selected": false, "measured": {"width": 192, "height": 66}, "dragging": false}, {"id": "TextInput-yOcIj", "type": "genericNode", "position": {"x": 54.421292416437126, "y": 526.9515277314282}, "data": {"node": {"template": {"_type": "Component", "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.base.io.text import TextComponent\nfrom langflow.io import MultilineInput, Output\nfrom langflow.schema.message import Message\n\n\nclass TextInputComponent(TextComponent):\n    display_name = \"Prompt\"\n    description = \"Get text inputs from the Playground.\"\n    icon = \"type\"\n    name = \"TextInput\"\n\n    inputs = [\n        MultilineInput(\n            name=\"input_value\",\n            display_name=\"Text\",\n            info=\"Text to be passed as input.\",\n        ),\n    ]\n    outputs = [\n        Output(display_name=\"Message\", name=\"text\", method=\"text_response\"),\n    ]\n\n    def text_response(self) -> Message:\n        return Message(\n            text=self.input_value,\n        )\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "input_value": {"tool_mode": false, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "input_value", "value": " Your task is to determine whether the patient described in the note have depression or not  \n **Output Instructions:** provide your final answer (only the number \"1\" or \"0\") in this format **The final answer is: {{ 0 or 1}}**.      \n---    \n** Patient Note Summary:**   \n  {}    \n___\n Output the result (1 for depression present, or 0 otherwise) in this format **The final answer is: **.", "display_name": "Text", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Text to be passed as input.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}}, "description": "Get text inputs from the Playground.", "icon": "type", "base_classes": ["Message"], "display_name": "Prompt", "documentation": "", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": true, "outputs": [{"types": ["Message"], "selected": "Message", "name": "text", "hidden": null, "display_name": "Message", "method": "text_response", "value": "__UNDEFINED__", "cache": true, "required_inputs": null, "allows_loop": false}], "field_order": ["input_value"], "beta": false, "legacy": false, "edited": true, "metadata": {}, "tool_mode": false, "lf_version": "1.1.3"}, "showNode": true, "type": "TextInput", "id": "TextInput-yOcIj"}, "selected": false, "measured": {"width": 320, "height": 229}, "dragging": false}, {"id": "TextInput-Hp49c", "type": "genericNode", "position": {"x": 52.467468055362616, "y": -64.7365861711136}, "data": {"node": {"template": {"_type": "Component", "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.base.io.text import TextComponent\nfrom langflow.io import MultilineInput, Output\nfrom langflow.schema.message import Message\n\n\nclass TextInputComponent(TextComponent):\n    \"\"\"\n    A component to capture and process text input from the Playground.\n    This component allows users to input text, which is then passed as a message for further processing.\n    \"\"\"\n\n    display_name = \"Patient Note\"  # Display name for the component in the UI\n    description = \"\"\"\n    Capture text input from the Playground and pass it as a message.\n    This component is useful for testing or processing user-provided text.\n    \"\"\"\n    icon = \"type\"  # Icon representing the component in the UI\n    name = \"TextInput\"  # Internal name of the component\n\n    # Define input fields\n    inputs = [\n        MultilineInput(\n            name=\"input_text\",  # Internal name for the input field\n            display_name=\"Input Text\",  # Display name for the input field\n            info=\"Enter the text to be processed.\",  # Help text for the input field\n        ),\n    ]\n\n    # Define output fields\n    outputs = [\n        Output(\n            display_name=\"Output Message\",  # Display name for the output\n            name=\"output_message\",  # Internal name for the output\n            method=\"generate_message\",  # Method to generate the output\n        ),\n    ]\n\n    def generate_message(self) -> Message:\n        \"\"\"\n        Generate a Message object containing the input text.\n\n        Returns:\n            Message: A Message object with the input text.\n        \"\"\"\n        return Message(text=self.input_text)", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "input_text": {"tool_mode": false, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "input_text", "value": "datasets/train_test_split/x_test.csv", "display_name": "Input Text", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Enter the text to be processed.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}}, "description": "\n    Capture text input from the Playground and pass it as a message.\n    This component is useful for testing or processing user-provided text.\n    ", "icon": "type", "base_classes": ["Message"], "display_name": "Patient Note", "documentation": "", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": true, "outputs": [{"types": ["Message"], "selected": "Message", "name": "output_message", "hidden": null, "display_name": "Output Message", "method": "generate_message", "value": "__UNDEFINED__", "cache": true, "required_inputs": null, "allows_loop": false}], "field_order": ["input_text"], "beta": false, "legacy": false, "edited": true, "metadata": {}, "tool_mode": false, "lf_version": "1.1.3"}, "showNode": true, "type": "TextInput", "id": "TextInput-Hp49c"}, "selected": false, "measured": {"width": 320, "height": 307}, "dragging": false}, {"id": "TextInput-iJHmK", "type": "genericNode", "position": {"x": 51.97375686848551, "y": 270.**********}, "data": {"node": {"template": {"_type": "Component", "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.base.io.text import TextComponent\nfrom langflow.io import MultilineInput, Output\nfrom langflow.schema.message import Message\n\n\nclass TextInputComponent(TextComponent):\n    display_name = \"Depression labels\"\n    description = \"Get text inputs from the Playground.\"\n    icon = \"type\"\n    name = \"TextInput\"\n\n    inputs = [\n        MultilineInput(\n            name=\"input_value\",\n            display_name=\"Text\",\n            info=\"Text to be passed as input.\",\n        ),\n    ]\n    outputs = [\n        Output(display_name=\"Message\", name=\"text\", method=\"text_response\"),\n    ]\n\n    def text_response(self) -> Message:\n        return Message(\n            text=self.input_value,\n        )\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "input_value": {"tool_mode": false, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "input_value", "value": "datasets/train_test_split/y_test.csv", "display_name": "Text", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Text to be passed as input.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}}, "description": "Get text inputs from the Playground.", "icon": "type", "base_classes": ["Message"], "display_name": "Depression labels", "documentation": "", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": true, "outputs": [{"types": ["Message"], "selected": "Message", "name": "text", "hidden": null, "display_name": "Message", "method": "text_response", "value": "__UNDEFINED__", "cache": true, "required_inputs": null, "allows_loop": false}], "field_order": ["input_value"], "beta": false, "legacy": false, "edited": true, "metadata": {}, "tool_mode": false, "lf_version": "1.1.3"}, "showNode": true, "type": "TextInput", "id": "TextInput-iJHmK"}, "selected": false, "measured": {"width": 320, "height": 229}, "dragging": false}, {"id": "TextInput-gvImP", "type": "genericNode", "position": {"x": 61.4052736640468, "y": 791.7334090552484}, "data": {"node": {"template": {"_type": "Component", "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.base.io.text import TextComponent\nfrom langflow.io import MultilineInput, Output\nfrom langflow.schema.message import Message\n\n\nclass TextInputComponent(TextComponent):\n    display_name = \"ft_model_path\"\n    description = \"Get text inputs from the Playground.\"\n    icon = \"type\"\n    name = \"TextInput\"\n\n    inputs = [\n        MultilineInput(\n            name=\"input_value\",\n            display_name=\"Text\",\n            info=\"Text to be passed as input.\",\n        ),\n    ]\n    outputs = [\n        Output(display_name=\"Message\", name=\"text\", method=\"text_response\"),\n    ]\n\n    def text_response(self) -> Message:\n        return Message(\n            text=self.input_value,\n        )\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "input_value": {"tool_mode": false, "trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "list_add_label": "Add More", "required": false, "placeholder": "", "show": true, "name": "input_value", "value": "finetuned_model/models/unsloth/Meta-Llama-3.1-8B-Instruct-bnb-4bit", "display_name": "Text", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Text to be passed as input.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}}, "description": "Get text inputs from the Playground.", "icon": "type", "base_classes": ["Message"], "display_name": "ft_model_path", "documentation": "", "minimized": false, "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": true, "outputs": [{"types": ["Message"], "selected": "Message", "name": "text", "hidden": null, "display_name": "Message", "method": "text_response", "value": "__UNDEFINED__", "cache": true, "required_inputs": null, "allows_loop": false}], "field_order": ["input_value"], "beta": false, "legacy": false, "edited": true, "metadata": {}, "tool_mode": false, "lf_version": "1.1.3"}, "showNode": true, "type": "TextInput", "id": "TextInput-gvImP"}, "selected": false, "measured": {"width": 320, "height": 229}, "dragging": false}], "edges": [{"source": "ParseData-5Ywdi", "sourceHandle": "{œdataTypeœ:œParseDataœ,œidœ:œParseData-5Ywdiœ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}", "target": "ChatOutput-1M74S", "targetHandle": "{œfieldNameœ:œinput_valueœ,œidœ:œChatOutput-1M74Sœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "data": {"targetHandle": {"fieldName": "input_value", "id": "ChatOutput-1M74S", "inputTypes": ["Message"], "type": "str"}, "sourceHandle": {"dataType": "ParseData", "id": "ParseData-5Ywdi", "name": "text", "output_types": ["Message"]}}, "id": "reactflow__edge-ParseData-5Ywdi{œdataTypeœ:œParseDataœ,œidœ:œParseData-5Ywdiœ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}-ChatOutput-1M74S{œfieldNameœ:œinput_valueœ,œidœ:œChatOutput-1M74Sœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "animated": false, "className": "", "selected": false}, {"source": "TextInput-Hp49c", "sourceHandle": "{œdataTypeœ:œTextInputœ,œidœ:œTextInput-Hp49cœ,œnameœ:œoutput_messageœ,œoutput_typesœ:[œMessageœ]}", "target": "DataExtractor-jXECy", "targetHandle": "{œfieldNameœ:œpatient_notes_pathœ,œidœ:œDataExtractor-jXECyœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "data": {"targetHandle": {"fieldName": "patient_notes_path", "id": "DataExtractor-jXECy", "inputTypes": ["Message"], "type": "str"}, "sourceHandle": {"dataType": "TextInput", "id": "TextInput-Hp49c", "name": "output_message", "output_types": ["Message"]}}, "id": "xy-edge__TextInput-Hp49c{œdataTypeœ:œTextInputœ,œidœ:œTextInput-Hp49cœ,œnameœ:œoutput_messageœ,œoutput_typesœ:[œMessageœ]}-DataExtractor-jXECy{œfieldNameœ:œpatient_notes_pathœ,œidœ:œDataExtractor-jXECyœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "animated": false, "className": ""}, {"source": "TextInput-iJHmK", "sourceHandle": "{œdataTypeœ:œTextInputœ,œidœ:œTextInput-iJHmKœ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}", "target": "DataExtractor-jXECy", "targetHandle": "{œfieldNameœ:œdepression_labels_pathœ,œidœ:œDataExtractor-jXECyœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "data": {"targetHandle": {"fieldName": "depression_labels_path", "id": "DataExtractor-jXECy", "inputTypes": ["Message"], "type": "str"}, "sourceHandle": {"dataType": "TextInput", "id": "TextInput-iJHmK", "name": "text", "output_types": ["Message"]}}, "id": "xy-edge__TextInput-iJHmK{œdataTypeœ:œTextInputœ,œidœ:œTextInput-iJHmKœ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}-DataExtractor-jXECy{œfieldNameœ:œdepression_labels_pathœ,œidœ:œDataExtractor-jXECyœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "animated": false, "className": ""}, {"source": "CustomComponent-czkiY", "sourceHandle": "{œdataTypeœ:œEvaluateModelœ,œidœ:œCustomComponent-czkiYœ,œnameœ:œevaluation_resultsœ,œoutput_typesœ:[œDataœ]}", "target": "ParseData-5Ywdi", "targetHandle": "{œfieldNameœ:œdataœ,œidœ:œParseData-5Ywdiœ,œinputTypesœ:[œDataœ],œtypeœ:œotherœ}", "data": {"targetHandle": {"fieldName": "data", "id": "ParseData-5Ywdi", "inputTypes": ["Data"], "type": "other"}, "sourceHandle": {"dataType": "EvaluateModel", "id": "CustomComponent-czkiY", "name": "evaluation_results", "output_types": ["Data"]}}, "id": "xy-edge__CustomComponent-czkiY{œdataTypeœ:œEvaluateModelœ,œidœ:œCustomComponent-czkiYœ,œnameœ:œevaluation_resultsœ,œoutput_typesœ:[œDataœ]}-ParseData-5Ywdi{œfieldNameœ:œdataœ,œidœ:œParseData-5Ywdiœ,œinputTypesœ:[œDataœ],œtypeœ:œotherœ}", "animated": false, "className": ""}, {"source": "DataExtractor-jXECy", "sourceHandle": "{œdataTypeœ:œDataExtractorœ,œidœ:œDataExtractor-jXECyœ,œnameœ:œdepression_labelsœ,œoutput_typesœ:[œDataFrameœ]}", "target": "CustomComponent-czkiY", "targetHandle": "{œfieldNameœ:œtrue_labelsœ,œidœ:œCustomComponent-czkiYœ,œinputTypesœ:[œDataFrameœ],œtypeœ:œotherœ}", "data": {"targetHandle": {"fieldName": "true_labels", "id": "CustomComponent-czkiY", "inputTypes": ["DataFrame"], "type": "other"}, "sourceHandle": {"dataType": "DataExtractor", "id": "DataExtractor-jXECy", "name": "depression_labels", "output_types": ["DataFrame"]}}, "id": "xy-edge__DataExtractor-jXECy{œdataTypeœ:œDataExtractorœ,œidœ:œDataExtractor-jXECyœ,œnameœ:œdepression_labelsœ,œoutput_typesœ:[œDataFrameœ]}-CustomComponent-czkiY{œfieldNameœ:œtrue_labelsœ,œidœ:œCustomComponent-czkiYœ,œinputTypesœ:[œDataFrameœ],œtypeœ:œotherœ}", "animated": false, "className": ""}, {"source": "DataExtractor-jXECy", "sourceHandle": "{œdataTypeœ:œDataExtractorœ,œidœ:œDataExtractor-jXECyœ,œnameœ:œpatient_notesœ,œoutput_typesœ:[œDataFrameœ]}", "target": "model_inference-TnPMH", "targetHandle": "{œfieldNameœ:œpatient_notesœ,œidœ:œmodel_inference-TnPMHœ,œinputTypesœ:[œDataFrameœ],œtypeœ:œotherœ}", "data": {"targetHandle": {"fieldName": "patient_notes", "id": "model_inference-TnPMH", "inputTypes": ["DataFrame"], "type": "other"}, "sourceHandle": {"dataType": "DataExtractor", "id": "DataExtractor-jXECy", "name": "patient_notes", "output_types": ["DataFrame"]}}, "id": "xy-edge__DataExtractor-jXECy{œdataTypeœ:œDataExtractorœ,œidœ:œDataExtractor-jXECyœ,œnameœ:œpatient_notesœ,œoutput_typesœ:[œDataFrameœ]}-model_inference-TnPMH{œfieldNameœ:œpatient_notesœ,œidœ:œmodel_inference-TnPMHœ,œinputTypesœ:[œDataFrameœ],œtypeœ:œotherœ}", "animated": false, "className": ""}, {"source": "TextInput-yOcIj", "sourceHandle": "{œdataTypeœ:œTextInputœ,œidœ:œTextInput-yOcIjœ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}", "target": "model_inference-TnPMH", "targetHandle": "{œfieldNameœ:œinput_prompt_templateœ,œidœ:œmodel_inference-TnPMHœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "data": {"targetHandle": {"fieldName": "input_prompt_template", "id": "model_inference-TnPMH", "inputTypes": ["Message"], "type": "str"}, "sourceHandle": {"dataType": "TextInput", "id": "TextInput-yOcIj", "name": "text", "output_types": ["Message"]}}, "id": "xy-edge__TextInput-yOcIj{œdataTypeœ:œTextInputœ,œidœ:œTextInput-yOcIjœ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}-model_inference-TnPMH{œfieldNameœ:œinput_prompt_templateœ,œidœ:œmodel_inference-TnPMHœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "animated": false, "className": ""}, {"source": "model_inference-TnPMH", "sourceHandle": "{œdataTypeœ:œModelInferenceœ,œidœ:œmodel_inference-TnPMHœ,œnameœ:œmodel_predictionsœ,œoutput_typesœ:[œDataFrameœ]}", "target": "CustomComponent-czkiY", "targetHandle": "{œfieldNameœ:œmodel_predictionsœ,œidœ:œCustomComponent-czkiYœ,œinputTypesœ:[œDataFrameœ],œtypeœ:œotherœ}", "data": {"targetHandle": {"fieldName": "model_predictions", "id": "CustomComponent-czkiY", "inputTypes": ["DataFrame"], "type": "other"}, "sourceHandle": {"dataType": "ModelInference", "id": "model_inference-TnPMH", "name": "model_predictions", "output_types": ["DataFrame"]}}, "id": "xy-edge__model_inference-TnPMH{œdataTypeœ:œModelInferenceœ,œidœ:œmodel_inference-TnPMHœ,œnameœ:œmodel_predictionsœ,œoutput_typesœ:[œDataFrameœ]}-CustomComponent-czkiY{œfieldNameœ:œmodel_predictionsœ,œidœ:œCustomComponent-czkiYœ,œinputTypesœ:[œDataFrameœ],œtypeœ:œotherœ}", "animated": false, "className": ""}, {"source": "TextInput-gvImP", "sourceHandle": "{œdataTypeœ:œTextInputœ,œidœ:œTextInput-gvImPœ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}", "target": "model_inference-TnPMH", "targetHandle": "{œfieldNameœ:œmodel_pathœ,œidœ:œmodel_inference-TnPMHœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "data": {"targetHandle": {"fieldName": "model_path", "id": "model_inference-TnPMH", "inputTypes": ["Message"], "type": "str"}, "sourceHandle": {"dataType": "TextInput", "id": "TextInput-gvImP", "name": "text", "output_types": ["Message"]}}, "id": "xy-edge__TextInput-gvImP{œdataTypeœ:œTextInputœ,œidœ:œTextInput-gvImPœ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}-model_inference-TnPMH{œfieldNameœ:œmodel_pathœ,œidœ:œmodel_inference-TnPMHœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "animated": false, "className": ""}], "viewport": {"x": 215.39942561526573, "y": 36.568975390845196, "zoom": 0.6125473171564803}}}