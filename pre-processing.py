# +
# Import necessary libraries
import pandas as pd

# Define paths to the datasets
data_path = "./datasets/1.4/"
phenotype_path = "./datasets/1.20.03/"

# +
# Load the datasets
notes_df = pd.read_csv(f"{data_path}NOTEEVENTS.csv", low_memory=False)
phenotype_df = pd.read_csv(f"{phenotype_path}ACTdb102003.csv")


# Merge the notes dataframe with the phenotype dataframe on common columns
merged_df = pd.merge(
    notes_df, phenotype_df, on=["HADM_ID", "ROW_ID", "SUBJECT_ID"], how="inner"
)

# Select relevant columns for further analysis
columns_to_keep = [
    "ROW_ID",
    "SUBJECT_ID",
    "HADM_ID",
    "CHARTDATE",
    "CATEGORY",
    "DESCRIPTION",
    "TEXT",
    "DEPRESSION",
]
filtered_df = merged_df[columns_to_keep]

# Remove duplicate entries based on the "TEXT" column and reset the index
filtered_df = filtered_df.drop_duplicates(subset=["TEXT"]).reset_index(drop=True)

# Drop unnecessary columns
filtered_df.drop(
    columns=["SUBJECT_ID", "HADM_ID", "CHARTDATE", "CATEGORY", "DESCRIPTION"],
    inplace=True,
)


# Save the filtered dataframe to a CSV file
filtered_df.to_csv("./datasets/non_conflict_depression.csv", index=False)

# Display the filtered dataframe
filtered_df

# Display the count of each value in the "DEPRESSION" column
filtered_df["DEPRESSION"].value_counts()

# List of columns to check in the phenotype dataframe
phenotype_columns = [
    "ADVANCED.CANCER",
    "ADVANCED.HEART.DISEASE",
    "ADVANCED.LUNG.DISEASE",
    "ALCOHOL.ABUSE",
    "CHRONIC.NEUROLOGICAL.DYSTROPHIES",
    "CHRONIC.PAIN.FIBROMYALGIA",
    "DEMENTIA",
    "DEPRESSION",
    "DEVELOPMENTAL.DELAY.RETARDATION",
    "NON.ADHERENCE",
    "OBESITY",
    "OTHER.SUBSTANCE.ABUSE",
    "SCHIZOPHRENIA.AND.OTHER.PSYCHIATRIC.DISORDERS",
]

# Check the value counts for each phenotype column
for column in phenotype_columns:
    print(phenotype_df[column].value_counts())


# Optional: Create a balanced dataset by sampling
# balanced_df = filtered_df[filtered_df['DEPRESSION'] == 0].sample(n=200, random_state=42)
# balanced_df = pd.concat([filtered_df[filtered_df['DEPRESSION'] == 1], balanced_df])
# balanced_df.to_csv("./../datasets/balance_non_conflict.csv", index=False)

# Print the value counts for the balanced dataframe
# print(balanced_df['DEPRESSION'].value_counts())
# balanced_df
# -
