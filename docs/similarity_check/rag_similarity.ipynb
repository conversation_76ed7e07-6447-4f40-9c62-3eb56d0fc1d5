{"cells": [{"cell_type": "code", "execution_count": 4, "id": "005eceb4", "metadata": {}, "outputs": [], "source": ["# result_path = \"./agents/result/llama3.3:70b_20250519_165647.pickle\"\n", "# df = pd.read_pickle(result_path)\n", "# chart = df[\"note\"][1]\n", "chart = \"\"\"\n", "Admission Date:  [**2195-7-4**]              Discharge Date:   [**2195-7-14**]\n", "\n", "Date of Birth:  [**2122-10-10**]             Sex:   M\n", "\n", "Service: MEDICINE\n", "\n", "Allergies:\n", "Penicillins\n", "\n", "Attending:[**First Name3 (LF) 2901**]\n", "Chief Complaint:\n", "PEA Arrest at [**Hospital **] transferred to [**Hospital1 18**]\n", "\n", "Major Surgical or Invasive Procedure:\n", "Cardiac Catheterization\n", "\n", "History of Present Illness:\n", "<PERSON>t is a 72 y/o male with PMHx of CAD, CHF, PVD with stent leg (?\n", "L or R), HTN, DM2, carotid artery disease, who was transferred\n", "to [**Hospital1 18**] from OSH after a PEA arrest. Per family, patient was at\n", "home at kitchen table when he experienced sudden onset chest\n", "pain and family called EMS. <PERSON><PERSON> went into PEA arrest and was\n", "given 1 round of epi and atropine and then had ROSC. OSH was\n", "worried about STEMI (for unclear reasons at this point) and\n", "their lab was unavailable so patient was transferred over to\n", "[**Hospital1 18**] where he went to cath lab. The RCA was totally occluded\n", "but collaterals from Left were noted. RHC showed elevated wedge\n", "(30s), RA pressures 15, PAP 60. Echo in lab showed moderate MR,\n", "mild AI, preserved LV, RV function. Calcified AV. Received lasix\n", "IV in lab 80 mg. LV gram showed mild MR, inferiobasal HK with EF\n", "about 45%. AS study showed moderate-severe AS with valve area\n", "1.0. Chronic occlusion of RCA and 80% prox LAD with good flow\n", "through LAD and LCx. No intervention performed. PA pressure at\n", "80 and wedge pressures a 30-35\n", "Patient was transferred up to CCU and additional history was\n", "performed. Per daughter and wife, patient was experiencing on\n", "and off chest pains since [**Month (only) 404**] with associated L arm\n", "numbness. Daughter would see him lefting his left arm up with\n", "the right as he couldn't move it voluntarily during these\n", "episodes. Also having severe SOB, especially while sleeping-\n", "severe orthopnea per daughter, also significant bilateral\n", "pitting edema. <PERSON><PERSON> had recently increased his lasix dose from\n", "20mg to 40mg daily. He also recently went to an urgent care\n", "clinic 4 days prior to presentation and was given Azithromycin\n", "for presumed CAP.\n", "Art line placed upon transfer to CCU. Hypotensive to Systolic\n", "80s-90s when sleeping and after fentanyl injections but\n", "increases upon awakening. Patient -1 L since cath lab. Started\n", "on lasix gtt at 5/hr\n", ".\n", "REVIEW OF SYSTEMS\n", "On review of systems, s/he denies any prior history of stroke,\n", "TIA, deep venous thrombosis, pulmonary embolism, bleeding at the\n", "time of surgery, myalgias, joint pains, cough, hemoptysis, black\n", "stools or red stools. S/he denies recent fevers, chills or\n", "rigors. S/he denies exertional buttock or calf pain. All of the\n", "other review of systems were negative.\n", ".\n", "Cardiac review of systems is notable for absence of chest pain,\n", "dyspnea on exertion, paroxysmal nocturnal dyspnea, orthopnea,\n", "ankle edema, palpitations, syncope or presyncope.\n", "\n", "\n", "Past Medical History:\n", "1. CARDIAC RISK FACTORS: +Diabetes, +Dyslipidemia, +Hypertension\n", "\n", "2. CARDIAC HISTORY:\n", "-CABG:\n", "-PERCUTANEOUS CORONARY INTERVENTIONS:\n", "-PACING/ICD:\n", "CHF per family- on lasix home dose 40 mg daily\n", "PVD- <PERSON><PERSON> in leg (unclear R or L)\n", "Bilateral carotid artery disease\n", "? COPD- was told by PCP\n", "3. OTHER PAST MEDICAL HISTORY:\n", ". Benign neck tumors\n", "\n", "\n", "Social History:\n", " SOCIAL HISTORY\n", "-Tobacco history: 1 ppd for many years, current smoker\n", "-ETOH: unknown\n", "-Illicit drugs: none\n", "\n", "\n", "\n", "Family History:\n", "FAMILY HISTORY:\n", "Father and 2 brothers with CAD\n", "\n", "\n", "Physical Exam:\n", "Admnission\n", "GENERAL: Intubated, sedated in NAD. Following commands.\n", "HEENT: NCAT. Sclera anicteric. PERRL, EOMI. Conjunctiva were\n", "pink, no pallor or cyanosis of the oral mucosa. No xanthalesma.\n", "\n", "NECK: Supple with no significant JVD\n", "CARDIAC: PMI located in 5th intercostal space, midclavicular\n", "line. RR, normal S1, S2. 3/6 systolic murmer heard over aortic\n", "and tricuspid areas. No thrills, lifts. No S3 or S4.\n", "LUNGS: No chest wall deformities, scoliosis or kyphosis. Resp\n", "were unlabored, no accessory muscle use. CTAB, no crackles,\n", "wheezes or rhonchi anteriorly\n", "ABDOMEN: Soft, non-tender, mildly distended. No HSM or\n", "tenderness. Abd aorta not enlarged by palpation. No abdominial\n", "bruits.\n", "EXTREMITIES: 2+ pitting edema bilaterally\n", "SKIN: No stasis dermatitis, ulcers, scars, or xanthomas.\n", "PULSES:\n", "Right: Carotid 2+ Femoral 2+ Popliteal 2+ DP 1+ PT 1+\n", "Left: Carotid 2+ Femoral 2+ Popliteal 2+ DP 1+ PT 1+\n", "\n", "Discharge Summary\n", "\n", "\n", "Pertinent Results:\n", "[**2195-7-4**] 08:42PM   WBC-16.8* RBC-4.01* HGB-11.3* HCT-36.2*\n", "MCV-90 MCH-28.1 MCHC-31.1 RDW-15.8*\n", "[**2195-7-4**] 08:42PM   NEUTS-90.0* LYMPHS-4.5* MONOS-4.3 EOS-0.8\n", "BASOS-0.4\n", "[**2195-7-4**] 08:40PM   HGB-11.4* calcHCT-34 O2 SAT-97\n", "[**2195-7-4**] 08:42PM   PT-11.7 PTT-60.4* INR(PT)-1.1\n", "[**2195-7-4**] 08:42PM   CK(CPK)-70\n", "[**2195-7-4**] 08:42PM   CK(CPK)-70\n", "[**2195-7-4**] 08:42PM   estGFR-Using this\n", "[**2195-7-4**] 08:42PM   GLUCOSE-285* UREA N-25* CREAT-1.3* SODIUM-139\n", "POTASSIUM-5.2* CHLORIDE-99 TOTAL CO2-30 ANION GAP-15\n", "\n", "FINDINGS:  On the right side the known occlusion of the internal\n", "carotid\n", "artery is confirmed.  On left side a mild amount of calcified\n", "plaque is seen\n", "in the common and internal carotid arteries.\n", "\n", "On the right side peak systolic velocities were 24 cm/sec for\n", "the common\n", "carotid artery and 203 cm/sec for the external carotid artery.\n", "\n", "No ICA/CCA ratio could be determined on the right side due to\n", "complete\n", "occlusion of the internal carotid artery.\n", "\n", "On the left side peak systolic velocities were 75 cm/sec for the\n", "proximal\n", "internal carotid artery, 106 cm/sec for the mid internal carotid\n", "artery, 100\n", "cm/sec for the distal internal carotid artery and 55 cm/sec for\n", "the common\n", "carotid artery.  The left ICA/CCA ratio was 2.0.\n", "\n", "Vertebral arteries presented antegrade flow.\n", "\n", "COMPARISON:  None available.\n", "\n", "IMPRESSION:\n", "1.  Occlusion of the right internal carotid artery.\n", "2.  Less than 40% stenosis of the left internal carotid artery.\n", "\n", "DR. [**First Name (STitle) **] [**Name (STitle) **]\n", "Approved: <PERSON><PERSON> [**2195-7-13**] 10:58 PM\n", "\n", "CTA LOWER EXTREMITIES:\n", "IMPRESSION:\n", "1.  Filling defect in a moderate segment of the right\n", "superficial femoral\n", "artery may represent thrombus versus dissection.  This was\n", "discussed with Dr.\n", "[**First Name4 (NamePattern1) **] [**Last Name (NamePattern1) **] by phone at 10:20 a.m. on [**2195-7-5**] after\n", "attending review.\n", "2.  Occluded dorsalis pedis and plantar arteries in the right\n", "foot.\n", "3.  Significant atherosclerotic disease in the lower extremity\n", "vasculature as\n", "described above.\n", "4.  <PERSON><PERSON><PERSON><PERSON><PERSON> without diverticulitis.\n", "\n", "The study and the report were reviewed by the staff radiologist.\n", "\n", "\n", "DR. [**First Name11 (Name Pattern1) **] [**Initial (NamePattern1) **] [**Last Name (NamePattern4) **]\n", "DR. [**First Name11 (Name Pattern1) 8711**] [**Initial (NamePattern1) **] [**Last Name (NamePattern4) **]\n", "Approved: SUN [**2195-7-5**] 8:53 PM\n", "\n", "\n", "Brief Hospital Course:\n", "72 y/o male with PMHx of Ischemic cardiomyopathy, severe AS,\n", "Severe <PERSON> and other medical comorbidities admitted after PEA\n", "arrest with CHF exacerbation s/p diuresis and hemodynamic\n", "optimization\n", "\n", "# PUMP: ECHO shows LVEF of 45% with mild MR and AS.  The patient\n", "was aggressively diuresed and put on nitro gtt titrated to\n", "appropriate BPs while in the ICU.  He was optimized\n", "hemodynamically with captopril and metoprolol tartrate.  These\n", "medications were then switched to Lisinopril 10 mg daily and PO\n", "Lasix as well as Sprironolactone.  Patient was over net 6.5 L\n", "out, on nasal cannula and discharge.\n", "\n", "# CORONARIES: The patient was originally billed as a STEMI when\n", "transferred to [**Hospital1 18**] and sent directly to cath lab where 80%\n", "occlusion of LAD, 40% LCx, and 100% RCA although with adequate\n", "collaterals were found.  Given the collaterals supported\n", "adequate flow, the CAD was thought to be chronic in nature and\n", "no intervention was performed in cath lab.  ST depressions noted\n", "on EKGs from OSH. The patient was placed on heparin. The patient\n", "refused CABG but will discuss the issue further with his\n", "outpatient cardiologist. Carotid ultrasound was obtained for\n", "pre-op evaluation should the patient decide to pursue coronary\n", "artery bypass. The right carotid was found to be occluded and\n", "the left 40% stenosed. Aspirin and atorvastatin were continued.\n", "The patient's heparin was discontinued on [**7-11**] as the patient's\n", "clinical status improved. The patient did not have any\n", "remarkable arrhythmias secondary to condition during this\n", "hospital stay.\n", "\n", "# PERIPHERAL VASCULAR DISEASE: A CTA of the lower extremities\n", "was done, showing the following:\n", "1.  Filling defect in a moderate segment of the right\n", "superficial femoral\n", "artery may represent thrombus versus dissection.  This was\n", "discussed with Dr.\n", "[**First Name4 (NamePattern1) **] [**Last Name (NamePattern1) **] by phone at 10:20 a.m. on [**2195-7-5**] after\n", "attending review.\n", "2.  Occluded dorsalis pedis and plantar arteries in the right\n", "foot.\n", "3.  Significant atherosclerotic disease in the lower extremity\n", "vasculature as\n", "described above.\n", "4.  <PERSON><PERSON><PERSON><PERSON><PERSON> without diverticulitis.\n", "\n", "On the night of admission his right foot was noted to be cool to\n", "touch without an appreciation for a dorsalis pedis pulse.\n", "Vascular surgery was consulted which led to the CTA of the LE as\n", "above.  A heparin drip was started.  The vascular surgery team\n", "did not recommend intervention as he is a poor surgical\n", "candidate and the operation would be major.  His right lower\n", "extremity became warmer over the next few days and a pulse\n", "returned without clinical consequence to his R foot.\n", "\n", "Inactive issues:\n", "DM2: The patient has known non-insulin dependent diabetes\n", "mellitus, type 2. His metformin was discontinued on admission to\n", "the hospital and his glucose was kept under good control with\n", "insulin.\n", "\n", "HTN: The patient's ACE inhibitor was continued. His metoprolol\n", "was increased to 125 mg [**Hospital1 **].\n", "\n", "Transitional Issues:\n", "Severe three vessel disease: The patient deferred CABG while in\n", "hospital but should discuss the issue further with his\n", "outpatient cardiologist.\n", "\n", "Diabetes: The patient will be discharged on his pre-admission\n", "dose of metformin without any insulin.\n", "\n", "Medications on Admission:\n", "Preadmissions medications listed are incomplete and require\n", "futher investigation.  Information was obtained from\n", "Family/Caregiver.\n", "1. MetFORMIN (Glucophage) 1000 mg PO BID\n", "2. Lisinopril 40 mg PO DAILY\n", "Hold if SBP <90\n", "3. Metoprolol Tartrate Dose is Unknown  PO BID\n", "4. Furosemide 40 mg PO DAILY\n", "5. Atorvastatin 20 mg PO DAILY\n", "6. Aspirin 325 mg PO DAILY\n", "7. Azithromycin 250 mg PO Q24H\n", "8. DiphenhydrAMINE 50 mg PO HS:PRN Insomnia\n", "\n", "\n", "Discharge Medications:\n", "1. Aspirin 325 mg PO DAILY\n", "2. Atorvastatin 20 mg PO DAILY\n", "3. Lisinopril 10 mg PO DAILY\n", "Please hold for SBP <95\n", "4. Metoprolol Tartrate 125 mg PO BID\n", "Hold if SBP <90, HR<55\n", "5. DiphenhydrAMINE 50 mg PO HS:PRN Insomnia\n", "6. Furosemide 40 mg PO DAILY\n", "7. MetFORMIN (Glucophage) 1000 mg PO BID\n", "\n", "\n", "Discharge Disposition:\n", "Extended Care\n", "\n", "Facility:\n", "[**Hospital 582**] Healthcare\n", "\n", "Discharge Diagnosis:\n", "#Acute on Chronic Diastolic Heart Failure with a preserved EF of\n", "45%\n", "#Severe 3-vessel Coronary Artery Disease with a 100% occlusion\n", "of the RCA (with collaterals) and an 80% occlusion of his LAD\n", "that is not amenable to percutaneous intervention\n", "#Severe Aortic Stenosis with a gradient of 40mmHg and a valve\n", "area of 1.0sq-cm\n", "#Peripheral Vascular Disease\n", "#Hypertension\n", "\n", "\n", "Discharge Condition:\n", "Patient is alert and oriented x 3 and in good mental state. His\n", "discharge condition is requiring 2L of O2 by NC which was his\n", "home regimen prior to coming in.\n", "Mental Status: Clear and coherent.\n", "Level of Consciousness: Alert and interactive.\n", "Ambulatory Status: _____________---______\n", "\n", "\n", "Discharge Instructions:\n", "Dear Mr [**Known lastname 111747**],\n", "\n", "You came to the hospital after a cardiac arrest at an outside\n", "hospital with congestive heart failure and intubated (tube in\n", "your airway). We gave you lasix (a water pill) and helped you\n", "diurese off the extra fluid. You improved with the diuresis and\n", "also with control of your blood pressure using Metoprolol\n", "Tartrate 125mg twice a day and decreased your previous dose of\n", "Lisinopril to 10mg daily. The Metoprolol was higher than your\n", "home dose because we wanted to tightly control your heart rate\n", "given your severe aortic stenosis. As you were being treated for\n", "your heart failure, we were able to wean you off the ventilator\n", "that you came to [**Hospital3 **] requiring and eventually extubate\n", "it.  You will be discharged on Metoprolol Tartrate 125 mg [**Hospital1 **],\n", "Lasix 20 mg PO daily, Spironolactone 12.5 mg daily (new\n", "medication), and Lisinopril 10 mg.  These are for your\n", "congestive heart failure and blood pressure.\n", "\n", "For your coronary artery disease, we put you on medical\n", "management including <PERSON><PERSON><PERSON> 325mg, Atorvastatin 20mg, and the\n", "above doses of your Metoprolol and Lisinopril.\n", "\n", "After your cardiac catherization, you developed significant\n", "ischemia in your right foot. You had lost pulses and it was\n", "becoming cold. CT scan showed a possible dissection of your R\n", "femoral artery. We consulted vascular surgery who suggested we\n", "put you on a Heparin drip (a blood thinner) and your pulses\n", "returned and your foot became warm and pink. We removed the\n", "He<PERSON>in on [**7-11**] with a plan of continuing to examine the foot to\n", "ensure adequate blood flow.\n", "\n", "After your catherization, you also developed a hematoma in your\n", "right thigh. The source was unsure at first with an unidentified\n", "reason for your hemoglobin dropping. Later, with a CT scan, your\n", "thigh was found to have a 10x10cm hematoma. You were transfused\n", "with a total of 4 units of Red Blood Cells and your hemoglobin\n", "was checked to make sure that you no longer were bleeding. Your\n", "Heparin was also adjusted at that time. Your blood counts have\n", "recovered from that episode.\n", "\n", "You have significant coronary artery disease and valve disease\n", "that we believe cannot be treated appropriately with medicines\n", "alone. We have recommended to receive open heart surgery to help\n", "treat these problems. [**Name (NI) **] refused these procedures on multiple\n", "occasions. A carotid artery ultrasound (a non-invasive test\n", "looking at the blood vessels in your neck) was done showing\n", "complete blockage of your right carotid artery and a 40%\n", "narrowing of your left.\n", "\n", "It was a pleasure taking care of you during your admission to\n", "the hospital.\n", "\n", "\n", "Followup Instructions:\n", "Name: [**Last Name (LF) **],[**First Name7 (NamePattern1) 819**] [**Initials (NamePattern4) **] [**Last Name (NamePattern4) **]\n", "Location: [**Hospital 46644**] MEDICAL ASSOCIATES\n", "Address: ONE PARKWAY, [**Location (un) **],[**Numeric Identifier 75553**]\n", "Phone: [**Telephone/Fax (1) 86181**]\n", "Appt:  [**7-20**] at 11:45am\n", "\n", "\n", "                             [**First Name11 (<PERSON> Pattern1) **] [**Last Name (NamePattern1) 2908**] MD, [**MD Number(3) 2909**]\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 14, "id": "1a6525cc", "metadata": {}, "outputs": [], "source": ["from sentence_transformers import SentenceTransformer\n", "import numpy as np\n", "from sklearn.metrics.pairwise import cosine_similarity\n", "import re\n", "\n", "def check_text_presence(query, document, threshold=0.8, window_size=100, stride=50):\n", "    \"\"\"\n", "    Check if query or very similar text is present in the document.\n", "    \n", "    Args:\n", "        query: The text to search for\n", "        document: The large text to search within\n", "        threshold: Similarity threshold (0-1) where 1 is exact match\n", "        window_size: Words per chunk when breaking down document\n", "        stride: How many words to move the window each time\n", "        \n", "    Returns:\n", "        dict: Results including whether text is present, similarity score, and matching text\n", "    \"\"\"\n", "    # Load sentence transformer model\n", "    model = SentenceTransformer('all-MiniLM-L6-v2')\n", "    \n", "    # Get embedding for the query\n", "    query_embedding = model.encode([query])\n", "    \n", "    # Split document into words\n", "    words = re.findall(r'\\S+|\\n', document)\n", "    \n", "    # Create chunks with overlapping windows\n", "    chunks = []\n", "    for i in range(0, len(words), stride):\n", "        if i + window_size <= len(words):\n", "            chunk = ' '.join(words[i:i+window_size])\n", "            chunks.append((chunk, i))\n", "    \n", "    if not chunks:\n", "        return {\"present\": False, \"score\": 0, \"match_text\": None, \"position\": None}\n", "    \n", "    # Get embeddings for all chunks\n", "    chunk_texts = [c[0] for c in chunks]\n", "    chunk_embeddings = model.encode(chunk_texts)\n", "    \n", "    # Calculate similarity scores\n", "    similarities = cosine_similarity(query_embedding, chunk_embeddings)[0]\n", "    \n", "    # Find best matching chunk\n", "    best_idx = np.argmax(similarities)\n", "    best_score = similarities[best_idx]\n", "    best_chunk = chunks[best_idx]\n", "    \n", "    # Determine if similar text is present based on threshold\n", "    is_present = best_score >= threshold\n", "    \n", "    return {\n", "        \"present\": is_present,\n", "        \"score\": float(best_score),\n", "        \"match_text\": best_chunk[0] if is_present else None,\n", "        \"position\": best_chunk[1] if is_present else None\n", "    }"]}, {"cell_type": "code", "execution_count": 16, "id": "7067a595", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Query: 'AI has changed'\n", "Present: <PERSON><PERSON><PERSON>\n", "Similarity score: 0.0000\n", "\n", "Query: 'AI is used in robotics and manufacturing'\n", "Present: <PERSON><PERSON><PERSON>\n", "Similarity score: 0.0000\n", "\n", "Query: 'Natural language processing enables computers to understand human language'\n", "Present: <PERSON><PERSON><PERSON>\n", "Similarity score: 0.0000\n"]}], "source": ["# Example usage\n", "document = \"\"\"\n", "Artificial intelligence has transformed many industries over the past decade.\n", "Machine learning models can now recognize patterns in data with remarkable accuracy.\n", "The applications of deep learning extend from healthcare to autonomous vehicles.\n", "Natural language processing enables computers to understand and generate human language.\n", "Recent advancements in neural networks have led to significant breakthroughs in AI research.\n", "\"\"\"\n", "\n", "# Test queries\n", "queries = [\n", "    \"AI has changed\", \n", "    \"AI is used in robotics and manufacturing\",\n", "    \"Natural language processing enables computers to understand human language\"\n", "]\n", "\n", "for query in queries:\n", "    result = check_text_presence(query, document, threshold=0.85)\n", "    print(f\"\\nQuery: '{query}'\")\n", "    print(f\"Present: {result['present']}\")\n", "    print(f\"Similarity score: {result['score']:.4f}\")\n", "    if result['present']:\n", "        print(f\"Matching text: '{result['match_text']}'\")\n", "        print(f\"Position (word index): {result['position']}\")"]}, {"cell_type": "code", "execution_count": null, "id": "0e0a57f6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".conda", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}