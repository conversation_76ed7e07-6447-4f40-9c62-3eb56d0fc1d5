{"cells": [{"cell_type": "code", "execution_count": 1, "id": "59ac640b", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "sys.path.append(os.path.abspath(\"../../../\"))"]}, {"cell_type": "code", "execution_count": 2, "id": "a093664e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["llama_new_context_with_model: n_ctx_per_seq (2048) < n_ctx_train (131072) -- the full capacity of the model will not be utilized\n", "/home/<USER>/miniconda3/envs/.conda/lib/python3.11/site-packages/llama_cpp/llama.py:1237: RuntimeWarning: Detected duplicate leading \"<|begin_of_text|>\" in prompt, this will likely reduce response quality, consider removing it...\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Generated output:\n", "{\"chain_of_thought\":[{\"evidence\":\"I am suffering from depression\",\"reasoning\":\"The patient explicitly mentions suffering from depression, which is a direct statement of the condition.\",\"result\":\"points to depression\"}],\"conclusion\":{\"evidence_depression\":[\"I am suffering from depression\"],\"reasoning_depression\":[\"The patient's direct statement of suffering from depression indicates a high level of confidence in the diagnosis.\"],\"confidence_depression\":0.95,\"code_depression\":[],\"has_depression\":\"yes\"}}\n"]}], "source": ["import logging\n", "from llama_cpp import Llama, LlamaGrammar\n", "import json\n", "from pydantic import BaseModel\n", "from typing import List\n", "from utils.prompt_schema import *\n", "\n", "# Define your model path\n", "model_path = \"../ollama/gguf_model/Llama-3.1-8B-Instruct-q4.gguf\"\n", "\n", "# Load your GGUF model\n", "model = Llama(\n", "    model_path=model_path,\n", "        n_ctx=2048, verbose=False\n", "    )\n", "\n", "# Convert the schema to a JSON string\n", "schema_json_str = json.dumps(DiagnosisSchema.model_json_schema())\n", "\n", "# Create grammar from JSON schema\n", "grammar = LlamaGrammar.from_json_schema(schema_json_str)\n", "\n", "# Your prompt\n", "prompt = \"\"\"<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n", "\n", "Cutting Knowledge Date: December 2023\n", "Today Date: 26 Jul 2024\n", "\n", "You are an expert in medical chart review. Your task is to carefully analyze a patient chart and determine whether the patient has a diagnosis of depression (excluding bipolar disorder, schizophrenia, or schizoaffective disorder).  This can be represented in the text either with words that closely map to ICD-9 or ICD-10 depression category or an explicit mention in context that the patient has a specific code. 1. Check for the presence of any of these ICD-9 codes (represented with regular expressions): 296.2[0-4], 296.3[0-4], 296.5[0-4], 296.82, 311, 298.0. 2. Check for the presence of any of these ICD-10 codes (represented with regular expressions): F31.[3-5]*, F32.[0-3], F32.89, F32.9, F33.[012389], F25.1. 3. Check for the presence of any of these bipolar disorder, schizophrenia, or schizoaffective disorder ICD-9 codes (295.[012345689]*, 295.7*) or ICD-10 codes (F20*, F25*) which would rule out depression:  Please generate output strictly in JSON format according to the provided template. Ensure the output consists solely of valid JSON without any explanatory text or comments outside the JSON structure.  For each distinct element of evidence you find, populate the chain_of_thought array with verbatim exact short extracts from the chart. For each element of evidence give your reasoning for why the evidence is informative on the diagnosis of depression, and place the result of your reasoning in the result field. After finishing generating the chain_of_thought array, synthesize all of the evidence to populate the conclusion elements as directed. Only conclude \"has_depression\":\"yes\" if you find direct evidence of diagnosis of depression with at least 50 percent certainty, that is, confidence_depression must be >= 50.  Strictly follow the specific instructions within each set of square brackets [] to determine the appropriate value for each variable.<|eot_id|><|start_header_id|>user<|end_header_id|>\n", "\n", "Here is the patient chart for analysis: I am suffering from depression<|eot_id|><|start_header_id|>JSON<|end_header_id|>\n", "\n", "{\"chain_of_thought\":[{\"evidence\":\"[<PERSON><PERSON><PERSON><PERSON> extract from the chart.]\",\"reasoning\":\"[Short explantion based on the relevance of the evidence to the presence or absence of depression.]\",\"result\":\"[Statement about whether the evidence points to depression or not.]\"}],\"conclusion\":{\"evidence_depression\":\"[Synthesis of multiple verbatim chart extracts of evidence for or against depression.]\",\"reasoning_depression\":\"[Explanation of your level of confidence that the patient has a diagnosis of depression]\",\"confidence_depression\":\"[What is the confidence of depression? Give a single number ranging 0.0 to 1.0 where 0.0 is you are certain there is not depression and 1.0 is you are certain there is depression.]\",\"code_depression\":\"[One or more depression-relevant comma-delimited ICD-9 or ICD-10 codes explicitly listed in the chart.]\",\"has_depression\":\"[yes or no based on whether the patient has a depression diagnosis with more than 50 percent certainty]\"}}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n", "\n", "\"\"\"\n", "\n", "# Use the model directly, not the generator\n", "output = model(\n", "    prompt,\n", "    max_tokens=512,\n", "    grammar=grammar\n", ")\n", "\n", "print(\"Generated output:\")\n", "print(output['choices'][0]['text'])"]}, {"cell_type": "code", "execution_count": 6, "id": "a2c1bb24", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generated output:\n", "{'id': 'cmpl-ecacd0c7-0ba5-4e7e-9bab-9025793b1419', 'object': 'text_completion', 'created': **********, 'model': '../ollama/gguf_model/Llama-3.1-8B-Instruct-q4.gguf', 'choices': [{'text': '{\"chain_of_thought\":[{\"evidence\":\"I am suffering from depression\",\"reasoning\":\"The patient explicitly mentions suffering from depression, which is a direct statement of the condition.\",\"result\":\"points to depression\"}],\"conclusion\":{\"evidence_depression\":[\"I am suffering from depression\"],\"reasoning_depression\":[\"The patient\\'s direct statement of suffering from depression indicates a high level of confidence in the diagnosis.\"],\"confidence_depression\":0.95,\"code_depression\":[],\"has_depression\":\"yes\"}}', 'index': 0, 'logprobs': None, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 707, 'completion_tokens': 103, 'total_tokens': 810}}\n"]}, {"data": {"text/plain": ["{'text': '{\"chain_of_thought\":[{\"evidence\":\"I am suffering from depression\",\"reasoning\":\"The patient explicitly mentions suffering from depression, which is a direct statement of the condition.\",\"result\":\"points to depression\"}],\"conclusion\":{\"evidence_depression\":[\"I am suffering from depression\"],\"reasoning_depression\":[\"The patient\\'s direct statement of suffering from depression indicates a high level of confidence in the diagnosis.\"],\"confidence_depression\":0.95,\"code_depression\":[],\"has_depression\":\"yes\"}}',\n", " 'index': 0,\n", " 'logprobs': None,\n", " 'finish_reason': 'stop'}"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["print(\"Generated output:\")\n", "print(output)\n", "output['choices'][0]['text']\n", "\n", "json.loads(output['choices'][0]['text'])\n", "output['choices'][0]"]}, {"cell_type": "code", "execution_count": 19, "id": "e97abf50", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generated output:\n", "{\"chain_of_thought\": [{\"evidence\": \"I am suffering from depression\", \"reasoning\": \"The patient explicitly states they are suffering from depression, which is a direct indicator of the diagnosis.\", \"result\": \"yes\"}], \"conclusion\": {\"evidence_depression\": [\"I am suffering from depression\"], \"reasoning_depression\": [\"The patient explicitly states they are suffering from depression, which is a direct indicator of the diagnosis.\"], \"confidence_depression\": 100, \"code_depression\": [\"F32.9\"], \"has_depression\": \"yes\"}}\n"]}], "source": ["messages = [\n", "    {\"role\": \"system\", \"content\": prompt.split(\"\\n\\nHere is the patient chart\")[0]},\n", "    {\"role\": \"user\", \"content\": \"Here is the patient chart for analysis: I am suffering from depression\"}\n", "]\n", "\n", "# Use the chat completion method instead\n", "output = model.create_chat_completion(\n", "    messages, \n", "    grammar=grammar\n", ")\n", "\n", "print(\"Generated output:\")\n", "print(output['choices'][0]['message']['content'])"]}, {"cell_type": "code", "execution_count": null, "id": "19cc451d", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'chain_of_thought': [{'evidence': 'I am suffering from depression',\n", "   'reasoning': 'The patient explicitly states they are suffering from depression, which is a direct indicator of the diagnosis.',\n", "   'result': 'yes'}],\n", " 'conclusion': {'evidence_depression': ['I am suffering from depression'],\n", "  'reasoning_depression': ['The patient explicitly states they are suffering from depression, which is a direct indicator of the diagnosis.'],\n", "  'confidence_depression': 100,\n", "  'code_depression': ['F32.9'],\n", "  'has_depression': 'yes'}}"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m<PERSON><PERSON> crashed while executing code in the current cell or a previous cell. \n", "\u001b[1;31m<PERSON><PERSON>se review the code in the cell(s) to identify a possible cause of the failure. \n", "\u001b[1;31mClick <a href='https://aka.ms/vscodeJupyterKernelCrash'>here</a> for more info. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["json.loads(output['choices'][0][\"message\"]['content'])"]}], "metadata": {"kernelspec": {"display_name": ".conda", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}