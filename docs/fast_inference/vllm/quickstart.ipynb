{"cells": [{"cell_type": "code", "execution_count": 1, "id": "343da9ab", "metadata": {}, "outputs": [], "source": ["from pydantic import BaseModel\n", "import sys\n", "import os\n", "sys.path.append(os.path.abspath(\"../../../\"))\n", "import json\n", "import pprint\n", "from utils.prompt_schema import *\n", "\n", "# from vllm import LLM, SamplingParams\n", "# from vllm.sampling_params import GuidedDecodingParams\n", "\n", "# model_path = \"meta-llama/Llama-3.1-8B-Instruct\"\n", "\n", "# # Initialize the vLLM model\n", "# llm = LLM(model=model_path)\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "id": "f63c5ede", "metadata": {}, "outputs": [], "source": ["\n", "\n", "\n", "\n", "# # Define the Pydantic model for the JSON schema\n", "# class CarDescription(BaseModel):\n", "#     brand: str\n", "#     model: str\n", "#     how: str\n", "\n", "# Generate the JSON schema from the Pydantic model\n", "# json_schema = DiagnosisSchema.model_json_schema()\n", "# json_schema = CarDescription.model_json_schema()\n", "# print(json_schema)\n", "# # Configure guided decoding with the JSON schema\n", "# guided_params = GuidedDecodingParams(json=json_schema)\n", "# sampling_params = SamplingParams(\n", "#     guided_decoding=guided_params,\n", "#     temperature=0.0,  # Greedy sampling for deterministic output\n", "#     max_tokens=512,   # Allow sufficient tokens for JSON output\n", "#     top_p=1.0,        # Disable nucleus sampling\n", "#     top_k=-1          # Disable top-k sampling\n", "# )\n", "\n", "# prompt = \"What is your car ?\"\n", "# outputs = llm.generate(prompt, sampling_params)\n", "# output_text = outputs[0].outputs[0].text\n", "# print(output_text)\n", "# # json_data = json.loads(output_text)\n", "# # print(json_data)"]}, {"cell_type": "code", "execution_count": null, "id": "f0b544a8", "metadata": {}, "outputs": [], "source": ["# This needs to be to run it \n", "# python -m vllm.entrypoints.openai.api_server     --model meta-llama/Llama-3.1-8B-Instruct     --served-model-name llama-3-8b-instruct\n", "\n", "from openai import OpenAI\n", "from pydantic import BaseModel\n", "\n", "\n", "client = OpenAI(base_url=\"http://localhost:8000/v1\", api_key=\"dummy\")\n", "\n", "response = client.chat.completions.create(\n", "    model=\"llama-3-8b-instruct\",\n", "    messages=[{\"role\": \"user\", \"content\": \"How are you ?\"}],\n", "    extra_body={\"guided_json\": DiagnosisSchema.model_json_schema()}\n", ")\n", "\n", "print(response.choices[0].message.content)  # JSON string"]}, {"cell_type": "code", "execution_count": null, "id": "1c92f2f9", "metadata": {}, "outputs": [], "source": ["# from vllm import LLM, SamplingParams\n", "# from vllm.sampling_params import GuidedDecodingParams\n", "# from pydantic import BaseModel\n", "# import json\n", "\n", "# # 2. Create LLM instance\n", "# llm = LLM(\"meta-llama/Llama-3.1-8B-Instruct\", guided_decoding_backend=\"xgrammar\", hf_token=\"*************************************\",enforce_eager=True)\n", "\n", "# # 3. Prepare sampling params with guided decoding\n", "# sampling_params = SamplingParams(\n", "#     temperature=0.0,  # Set to 0 for deterministic output\n", "#     top_p=1.0,\n", "#     max_tokens=500,\n", "#     guided_decoding=GuidedDecodingParams(\n", "#         json=DiagnosisSchema.model_json_schema(),\n", "#         backend=\"xgrammar\",\n", "#         whitespace_pattern=r\"\\s*\"  # Handle whitespace properly\n", "#     )\n", "# )\n", "\n", "# # 4. Generate with constraint\n", "# prompt = \"\"\"Sing a song for me, please\"\"\"\n", "# outputs = llm.generate(prompt, sampling_params)\n", "# print(outputs[0].outputs[0].text)"]}, {"cell_type": "code", "execution_count": null, "id": "48def86a", "metadata": {}, "outputs": [], "source": ["# prompt = \"\"\"How is it going ?\"\"\"\n", "# outputs = llm.generate(prompt, sampling_params)\n", "# print(outputs[0].outputs[0].text)"]}, {"cell_type": "code", "execution_count": null, "id": "4db2eac9", "metadata": {}, "outputs": [], "source": ["from vllm import LLM, SamplingParams\n", "from vllm.sampling_params import GuidedDecodingParams\n", "from pydantic import BaseModel\n", "\n", "# Define your schema\n", "class ResponseStructure(BaseModel):\n", "    status: str\n", "    data: dict\n", "    error: str = None\n", "    \n", "# Convert to JSON schema\n", "json_schema = ResponseStructure.model_json_schema()\n", "\n", "# Create guided decoding parameters with your JSON schema\n", "guided_decoding_params = GuidedDecodingParams(json=json_schema)\n", "\n", "# Pass these parameters to SamplingParams\n", "sampling_params = SamplingParams(\n", "    temperature=0.1,  # Low temperature for more deterministic output\n", "    guided_decoding=guided_decoding_params  # Use the guided_decoding parameter\n", ")\n", "\n", "# Initialize the model\n", "llm = LLM(model=\"../ollama/meta-llama/Llama-3.1-8B-Instruct\")\n", "\n", "# Run inference\n", "prompt = \"How are you ?\"\n", "outputs = llm.generate(prompt, sampling_params)\n", "\n", "# Extract the JSON output\n", "json_response = outputs[0].outputs[0].text\n", "print(json_response)"]}, {"cell_type": "code", "execution_count": null, "id": "24e8378c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".conda", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}