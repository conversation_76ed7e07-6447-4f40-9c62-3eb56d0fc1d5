{"cells": [{"cell_type": "code", "execution_count": 1, "id": "a1cb86f4", "metadata": {}, "outputs": [], "source": ["import os \n", "import sys\n", "sys.path.append(os.path.abspath(\"../../../\"))\n", "from utils.prompt_schema import DiagnosisSchema"]}, {"cell_type": "code", "execution_count": 2, "id": "2cb1a9ee", "metadata": {}, "outputs": [], "source": ["import ollama\n", "\n", "import json\n", "from transformers import BitsAndBytesConfig,AutoTokenizer,AutoModel"]}, {"cell_type": "code", "execution_count": 3, "id": "e5f6f81b", "metadata": {}, "outputs": [], "source": ["# schema = {\n", "#     \"type\": \"object\",\n", "#     \"properties\": {\n", "#         \"name\": {\"type\": \"string\"},\n", "#         \"age\": {\"type\": \"integer\"},\n", "#         \"hobbies\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}},\n", "#         \"address\": {\n", "#             \"type\": \"object\",\n", "#             \"properties\": {\n", "#                 \"street\": {\"type\": \"string\"},\n", "#                 \"city\": {\"type\": \"string\"},\n", "#                 \"zip\": {\"type\": \"string\"}\n", "#             },\n", "#             \"required\": [\"street\", \"city\", \"zip\"]  # Force these sub-fields\n", "#         }\n", "#     },\n", "#     \"required\": [\"name\", \"age\", \"hobbies\", \"address\"]  # Force all top fields\n", "# }\n", "\n", "# data = {\n", "#     \"model\": \"llama3:latest\",\n", "#     \"prompt\": \"ANY PROMPT HERE - SCHEMA WILL OVERRIDE IT\",  # Prompt becomes irrelevant\n", "#     # \"template\": template,\n", "#     \"format\": DiagnosisSchema.model_json_schema(),  # Dual enforcement\n", "#     \"stream\": <PERSON><PERSON><PERSON>,\n", "#     \"options\": {\n", "#         \"temperature\": 0.1, \n", "#         'num_gpu': 1\n", "#     }\n", "# }\n", "\n", "# url = \"http://localhost:11434/api/generate\"\n", "\n", "# for i in range(10):\n", "#     response = requests.post(url, json=data)\n", "#     json_output = json.loads(response.json()[\"response\"])\n", "#     print(json_output)"]}, {"cell_type": "code", "execution_count": 4, "id": "1c838525", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'chain_of_thought': [], 'conclusion': {'evidence_depression': ['anything_will_work'], 'reasoning_depression': ['nothing_is_good_enough'], 'confidence_depression': 0.5, 'code_depression': ['any_code_will_do'], 'has_depression': 'yes'}}\n", "{'chain_of_thought': [], 'conclusion': {'evidence_depression': [\"I'm not sure what you mean by 'anything will work' but I'll take it as a prompt to explore the concept of flexibility and adaptability in problem-solving. Let's see where this takes us...\"], 'reasoning_depression': [], 'confidence_depression': 0.5, 'code_depression': [\"# Anything will work\\n\\n# This is a placeholder for any solution that might be found.\\n\\n# Let's see what we can come up with...\"], 'has_depression': 'yes'}}\n", "{'chain_of_thought': [], 'conclusion': {'evidence_depression': ['nothing', 'nothing'], 'reasoning_depression': ['nothing', 'nothing'], 'confidence_depression': 0.5, 'code_depression': ['nothing', 'nothing'], 'has_depression': 'yes'}}\n", "{'chain_of_thought': [{'evidence': 'Anything will work', 'reasoning': \"I'm not sure what you mean by this, but I'll take a shot. Are you suggesting that any solution or approach can be effective? That's an interesting perspective.\", 'result': \"The result is that we're open to exploring different options and finding the best fit for our situation.\"}], 'conclusion': {'evidence_depression': [\"I think I understand what you mean now. You're saying that when faced with a problem, we should be willing to try different things until we find one that works. That's a great attitude to have!\", 'result_of_conclusion'], 'reasoning_depression': ['This approach can be helpful because it allows us to adapt and adjust as needed.'], 'confidence_depression': 0.8, 'code_depression': ['', ''], 'has_depression': 'no'}}\n", "{'chain_of_thought': [], 'conclusion': {'evidence_depression': [\"I'm not sure what you mean by 'anything will work'.\", \"Is it a phrase that's been stuck in your head?\", 'Do you want to talk about something specific?'], 'reasoning_depression': [\"Maybe you're feeling overwhelmed and anything seems like an option.\", 'Are you looking for a way out or a solution?', 'Have you tried talking to someone about it?'], 'confidence_depression': 0.5, 'code_depression': ['A', 'B', 'C'], 'has_depression': 'yes'}}\n", "{'chain_of_thought': [], 'conclusion': {'evidence_depression': [\"Anythin' will wark\"], 'reasoning_depression': [], 'confidence_depression': 0.5, 'code_depression': [], 'has_depression': 'yes'}}\n", "{'chain_of_thought': [{'evidence': 'anything', 'reasoning': 'will_work', 'result': 'uncertainty'}], 'conclusion': {'evidence_depression': ['nothing', 'everything'], 'reasoning_depression': ['nothing', 'everything'], 'confidence_depression': 0.5, 'code_depression': ['nothing', 'everything'], 'has_depression': 'yes'}}\n", "{'chain_of_thought': [], 'conclusion': {'evidence_depression': ['nothing', 'nothing', 'nothing'], 'reasoning_depression': ['nothing', 'nothing', 'nothing'], 'confidence_depression': 0.5, 'code_depression': ['nothing', 'nothing', 'nothing'], 'has_depression': 'yes'}}\n", "{'chain_of_thought': [], 'conclusion': {'evidence_depression': ['anything'], 'reasoning_depression': ['will_work'], 'confidence_depression': 0.5, 'code_depression': ['#anything_will_work'], 'has_depression': 'yes'}}\n", "{'chain_of_thought': [], 'conclusion': {'evidence_depression': ['anything_will_work'], 'reasoning_depression': ['anything_will_work'], 'confidence_depression': 0.5, 'code_depression': ['anything_will_work'], 'has_depression': 'yes'}}\n"]}], "source": ["for i in range(10):\n", "    response = ollama.generate(\n", "        model=\"llama3:latest\", \n", "        prompt=\"Anything will work\",\n", "        format=DiagnosisSchema.model_json_schema(),\n", "        options={\n", "            \"temperature\": 0.3\n", "        }\n", "    )\n", "\n", "    json_output = json.loads(response.response)\n", "    print(json_output)"]}, {"cell_type": "markdown", "id": "785b6e0d", "metadata": {}, "source": ["## Description\n", "    Loading a model with the llama.cpp and running it. It also takes a lot of time so I skip this approach and started using the ollama above approach.\n", "    I keep the code here only for reference."]}, {"cell_type": "code", "execution_count": 5, "id": "118d664f", "metadata": {}, "outputs": [], "source": ["# import json\n", "# from pydantic import BaseModel, Field\n", "# from llama_cpp import LlamaGrammar  # Adjust if you use a different import path\n", "\n", "# # 1. Define your schema\n", "# class PersonSchema(BaseModel):\n", "#     name: str \n", "#     age: int \n", "#     email: str \n", "\n", "# # 2. Get JSON Schema and convert to JSON string\n", "# json_schema = json.dumps(PersonSchema.model_json_schema())\n", "\n", "# # 3. Use the schema with LlamaGrammar\n", "# grammar = LlamaGrammar.from_json_schema(json_schema)\n", "\n", "# print(grammar)\n", "\n", "\n", "# llm = Llama(\n", "#     model_path=\"./model.gguf\",\n", "#     n_gpu_layers=-1,\n", "# )\n", "\n", "# prompt = \"\"\"Return information about a person in this exact JSON format:\n", "# {\n", "#     \"name\": \"string\",\n", "#     \"age\": integer,\n", "#     \"email\": \"string\"\n", "# }\"\"\"\n", "\n", "# # Generate response with grammar constraint\n", "# response = llm.create_completion(\n", "#     prompt,\n", "#     # grammar=grammar, \n", "#     max_tokens=200,\n", "#     temperature=0.7,\n", "# )\n", "\n", "# type(response[\"choices\"])\n", "# response[\"choices\"][0][\"text\"]\n", "# # json.loads(response[\"choices\"][0][\"text\"])\n"]}, {"cell_type": "markdown", "id": "d941518d", "metadata": {}, "source": ["## Loading and Saving of hugging face model"]}, {"cell_type": "code", "execution_count": 6, "id": "32cc5ebc", "metadata": {}, "outputs": [], "source": ["\n", "# LOCAL_SAVE_DIR = \"./llama-3.1-8b-instruct\"\n", "\n", "# # Load model directly\n", "# from transformers import AutoTokenizer, AutoModelForCausalLM\n", "\n", "# tokenizer = AutoTokenizer.from_pretrained(\"meta-llama/Llama-3.1-8B-Instruct\")\n", "# model = AutoModelForCausalLM.from_pretrained(\"meta-llama/Llama-3.1-8B-Instruct\")\n", "\n", "# model.save_pretrained(LOCAL_SAVE_DIR)\n", "# tokenizer.save_pretrained(LOCAL_SAVE_DIR)"]}, {"cell_type": "markdown", "id": "cb1520d3", "metadata": {}, "source": ["## Ollama chat template"]}, {"cell_type": "code", "execution_count": 7, "id": "3f6836d8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'chain_of_thought': [{'evidence': 'anything', 'reasoning': 'will work', 'result': 'valid'}], 'conclusion': {'evidence_depression': ['anything'], 'reasoning_depression': ['will work'], 'confidence_depression': 0.5, 'code_depression': ['anything will work', 'anythig will work'], 'has_depression': 'yes'}}\n", "{'chain_of_thought': [{'evidence': 'anything', 'reasoning': 'will_work', 'result': 'flexible_mindset'}], 'conclusion': {'evidence_depression': ['nothing_is_impossible'], 'reasoning_depression': ['when_anything_can_work'], 'confidence_depression': 0.8, 'code_depression': ['#anything_will_work'], 'has_depression': 'yes'}}\n"]}], "source": ["for i in range(2):\n", "    response = ollama.chat(\n", "        model=\"llama3:latest\",\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": \"Anything will work\"},\n", "            {\"role\": \"user\", \"content\": \"Anything will work\"},\n", "            \n", "        ],\n", "        format=DiagnosisSchema.model_json_schema(),\n", "        options={\n", "            \"temperature\": 0.3,\n", "            'num_gpu': 1\n", "        }\n", "    )\n", "    \n", "    json_output = json.loads(response['message']['content'])\n", "    print(json_output)"]}, {"cell_type": "code", "execution_count": 8, "id": "cddec5d9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'chain_of_thought': [{'evidence': 'Anything will work', 'reasoning': 'This phrase suggests a lack of specificity or clarity, implying that any approach or solution is acceptable.', 'result': 'The outcome may be uncertain or unpredictable, as the effectiveness of the chosen method is not guaranteed.'}], 'conclusion': {'evidence_depression': ['Anything will work'], 'reasoning_depression': ['This phrase suggests a lack of specificity or clarity, implying that any approach or solution is acceptable.'], 'confidence_depression': 0.5, 'code_depression': ['The outcome may be uncertain or unpredictable, as the effectiveness of the chosen method is not guaranteed.'], 'has_depression': 'yes'}}\n", "{'chain_of_thought': [], 'conclusion': {'evidence_depression': ['anything_will_work'], 'reasoning_depression': ['nothing_to_loose', 'no_expectations'], 'confidence_depression': 0.5, 'code_depression': ['python'], 'has_depression': 'yes'}}\n"]}], "source": ["for i in range(2):\n", "    response = ollama.chat(\n", "        model=\"llama3:latest\",\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": \"Anything will work\"},\n", "            {\"role\": \"user\", \"content\": \"Anything will work\"},\n", "            \n", "        ],\n", "        format=DiagnosisSchema.model_json_schema(),\n", "        options={\n", "            \"temperature\": 0.3,\n", "            'num_gpu': 1,\n", "            \"num_thread\":8,\n", "            \"num_batch\":512,\n", "            \"num_ctx\": 20000,\n", "            'nmap': True,\n", "            'f16-Kv': True } \n", "    )\n", "    \n", "    json_output = json.loads(response['message']['content'])\n", "    print(json_output)"]}, {"cell_type": "code", "execution_count": null, "id": "ffe9727c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".conda", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}