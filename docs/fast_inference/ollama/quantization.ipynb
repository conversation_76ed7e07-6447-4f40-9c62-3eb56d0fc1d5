{"cells": [{"cell_type": "code", "execution_count": 1, "id": "14d20c60", "metadata": {}, "outputs": [], "source": ["import os\n", "from transformers import BitsAndBytesConfig, AutoTokenizer, AutoModel, AutoModelForCausalLM"]}, {"cell_type": "code", "execution_count": 2, "id": "699eea7c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["../../../model/Qwen/Qwen2-7B-Instruct\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Sliding Window Attention is enabled but not implemented for `sdpa`; unexpected results may be encountered.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ccca9e128bf84a89bb9f4d7e6addd1d9", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading checkpoint shards:   0%|          | 0/4 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["('../../../model/Qwen/Qwen2-7B-Instruct/tokenizer_config.json',\n", " '../../../model/Qwen/Qwen2-7B-Instruct/special_tokens_map.json',\n", " '../../../model/Qwen/Qwen2-7B-Instruct/vocab.json',\n", " '../../../model/Qwen/Qwen2-7B-Instruct/merges.txt',\n", " '../../../model/Qwen/Qwen2-7B-Instruct/added_tokens.json',\n", " '../../../model/Qwen/Qwen2-7B-Instruct/tokenizer.json')"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["quantization_config=BitsAndBytesConfig(load_in_4bit=True)\n", "# model_name = \"meta-llama/Llama-3.1-8B-Instruct\"\n", "model_name = \"Qwen/Qwen2-7B-Instruct\"\n", "LOCAL_SAVE_DIR = f\"../../../model/{model_name}\"\n", "print(LOCAL_SAVE_DIR)\n", "tokenizer = AutoTokenizer.from_pretrained(model_name, token=\"*************************************\")\n", "model = AutoModelForCausalLM.from_pretrained(model_name, token=\"*************************************\")\n", "\n", "os.makedirs(model_name, exist_ok=True)\n", "model.save_pretrained(LOCAL_SAVE_DIR)\n", "tokenizer.save_pretrained(LOCAL_SAVE_DIR)"]}, {"cell_type": "markdown", "id": "518eb903", "metadata": {}, "source": ["\n", "### Common quantization options (choose one):\n", "- `Q4_0` - small, very high quality loss\n", "- `Q4_1` - small, substantial quality loss\n", "- `Q5_0` - medium, balanced quality/size\n", "- `Q5_1` - medium, less quality loss than Q5_0\n", "- `Q2_K` - smallest, extreme quality loss\n", "- `Q3_K` - tiny, very high quality loss\n", "- `Q4_K` - recommended small size (alias for Q4_K_M)\n", "- `Q5_K` - recommended medium size (alias for Q5_K_M)\n", "- `Q6_K` - very large, extremely high quality\n", "\n", "For most use cases, `Q4_K_M` provides a good balance between size and quality.\n", "Link for the list of quantanize \n", "https://github.com/ggml-org/llama.cpp/blob/master/examples/quantize/quantize.cpp"]}, {"cell_type": "code", "execution_count": 3, "id": "c14670d7", "metadata": {}, "outputs": [], "source": ["# ! python ./llama.cpp/convert_hf_to_gguf.py ./meta-llama/  --outfile./gguf_model/Llama-3.1-8B-Instruct-bnb-8bit.gguf\n", "# ./llama.cpp/build/bin/llama-quantize  ./gguf_model/Llama-3.1-8B-Instruct.gguf ./gguf_model/Llama-3.1-8B-Instruct-q4.gguf Q4_K_M\n", "# ollama create <model_name> -f ./fast_inference/rajesh_test_model.Modelfile"]}], "metadata": {"kernelspec": {"display_name": ".conda", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}