## Description
    This folder  explores optimized methods for running Hugging Face models with accelerated inference speeds using native Hugging Face libraries. While solutions like Ollama (which leverages llama.cpp) currently set the benchmark for fast local inference, this project aims to achieve comparable or better performance using Hugging Face's ecosystem.

## Key Focus Areas:
- Benchmarking inference speeds against Ollama's performance
- Implementing Hugging Face-native optimization techniques
- Exploring quantization, flash attention, and other acceleration methods
- Comparing performance with outlier libraries
- Developing reproducible optimization pipelines