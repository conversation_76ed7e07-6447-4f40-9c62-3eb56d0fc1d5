{"cells": [{"cell_type": "code", "execution_count": null, "id": "14d20c60", "metadata": {}, "outputs": [], "source": ["import os\n", "import torch\n", "from transformers import AutoTokenizer, AutoModelForCausalLM\n", "from utils.model_convert import convert_hf_to_ollama"]}, {"cell_type": "code", "execution_count": null, "id": "62f318d4", "metadata": {}, "outputs": [], "source": ["hf_token = \"*************************************\"\n", "gwd = os.getcwd()\n", "# hf_model_name = \"Qwen/Qwen3-14B\"\n", "# local_model = f\"./model/{hf_model_name}\"\n", "\n", "hf_model_name = \"meta-llama/Llama-3.1-8b-Instruct-treatment\"\n", "local_model = f\"./model/dpo_model/{hf_model_name}\"\n"]}, {"cell_type": "code", "execution_count": null, "id": "e8b0a60c", "metadata": {}, "outputs": [], "source": ["# try :\n", "#     tokenizer = AutoTokenizer.from_pretrained(local_model)    \n", "# except: \n", "#     tokenizer = AutoTokenizer.from_pretrained(hf_model_name, token=hf_token)\n", "#     model = AutoModelForCausalLM.from_pretrained(\n", "#         hf_model_name,\n", "#         device_map=\"auto\",\n", "#         token=hf_token\n", "#     )\n", "#     tokenizer.save_pretrained(local_model)\n", "#     model.save_pretrained(local_model)\n"]}, {"cell_type": "markdown", "id": "518eb903", "metadata": {}, "source": ["\n", "### Common quantization options (choose one):\n", "- `Q4_0` - small, very high quality loss\n", "- `Q4_1` - small, substantial quality loss\n", "- `Q5_0` - medium, balanced quality/size\n", "- `Q5_1` - medium, less quality loss than Q5_0\n", "- `Q2_K` - smallest, extreme quality loss\n", "- `Q3_K` - tiny, very high quality loss\n", "- `Q4_K` - recommended small size (alias for Q4_K_M)\n", "- `Q5_K` - recommended medium size (alias for Q5_K_M)\n", "- `Q6_K` - very large, extremely high quality\n", "\n", "For most use cases, `Q4_K_M` provides a good balance between size and quality.\n", "Link for the list of quantanize \n", "https://github.com/ggml-org/llama.cpp/blob/master/examples/quantize/quantize.cpp"]}, {"cell_type": "code", "execution_count": null, "id": "60ab4580", "metadata": {}, "outputs": [], "source": ["# model_brand = hf_model_name.split(\"/\")[0]\n", "# model_name =hf_model_name.split(\"/\")[-1]\n", "# current_dir = os.getcwd()\n", "# gguf_dir = os.path.join(current_dir, f\"model/gguf/{model_name}.gguf\")\n", "# quantize_dir = os.path.join(current_dir, f\"model/gguf/{model_name}-q4-k-m.gguf\")\n", "\n", "# !python ./llama.cpp/convert_hf_to_gguf.py {local_model}  --outfile  {gguf_dir}\n", "# os.chdir(\"./llama.cpp/build/bin\")\n", "# !./llama-quantize {gguf_dir} {quantize_dir} Q4_K_M\n", "# os.chdir(current_dir)\n", "# !echo \"FROM {quantize_dir}\" > ./Modelfile\n", "# !ollama create {model_brand}:{model_name}  -f ./Modelfile"]}], "metadata": {"kernelspec": {"display_name": ".conda", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}