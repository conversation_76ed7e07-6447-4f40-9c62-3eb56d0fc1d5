import os
import sys
import json
import pandas as pd
import torch
import numpy as np
from datasets import Dataset
from transformers import (
    AutoModelForCausalLM,
    AutoTokenizer,
    BitsAndBytesConfig,
)
from peft import LoraConfig, PeftModel, prepare_model_for_kbit_training
from trl import ORPOConfig, ORPOTrainer, setup_chat_format
from datasets import load_dataset
from sklearn.metrics import accuracy_score
from tqdm import tqdm

# Check GPU availability
print(f"CUDA available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"GPU count: {torch.cuda.device_count()}")
    print(f"GPU name: {torch.cuda.get_device_name(0)}")
    print(f"GPU memory: {torch.cuda.get_device_properties(0).total_memory / (1024**3):.2f} GB")

# Model and dataset configuration
base_model = "meta-llama/Meta-Llama-3-8B-Instruct"
new_model = "OrpoLlama-3-8B"
token = "*************************************"
dataset_name = "mlabonne/orpo-dpo-mix-40k"

# Create evaluation function
def evaluate_model(model, tokenizer, eval_dataset, num_samples=100):
    """
    Evaluate model on a dataset, returning accuracy metrics
    """
    model.eval()
    correct_predictions = 0
    total_samples = min(num_samples, len(eval_dataset))
    
    results = {
        "chosen_better": 0,
        "rejected_better": 0,
        "tie": 0,
        "avg_chosen_score": 0,
        "avg_rejected_score": 0,
    }
    
    with torch.no_grad():
        for i in tqdm(range(total_samples), desc="Evaluating"):
            sample = eval_dataset[i]
            
            # Get chosen and rejected responses
            chosen_input = sample["chosen"]
            rejected_input = sample["rejected"]
            
            # Calculate scores for chosen and rejected responses
            chosen_inputs = tokenizer(chosen_input, return_tensors="pt").to(model.device)
            rejected_inputs = tokenizer(rejected_input, return_tensors="pt").to(model.device)
            
            chosen_loss = model(**chosen_inputs, labels=chosen_inputs["input_ids"]).loss.item()
            rejected_loss = model(**rejected_inputs, labels=rejected_inputs["input_ids"]).loss.item()
            
            # Convert losses to preference scores (lower loss = higher preference)
            chosen_score = -chosen_loss
            rejected_score = -rejected_loss
            
            # Update results
            results["avg_chosen_score"] += chosen_score
            results["avg_rejected_score"] += rejected_score
            
            if chosen_score > rejected_score:
                results["chosen_better"] += 1
            elif rejected_score > chosen_score:
                results["rejected_better"] += 1
            else:
                results["tie"] += 1
    
    # Calculate averages
    results["avg_chosen_score"] /= total_samples
    results["avg_rejected_score"] /= total_samples
    results["preference_accuracy"] = results["chosen_better"] / total_samples
    results["total_samples"] = total_samples
    
    return results

# Main execution code
def main():
    # Load dataset
    dataset = load_dataset(dataset_name, split="all")
    dataset = dataset.shuffle(seed=42).select(range(1000))  # Only use 1000 samples for demo
    
    # QLoRA config
    bnb_config = BitsAndBytesConfig(
        load_in_4bit=True,
        bnb_4bit_quant_type="nf4",
        bnb_4bit_use_double_quant=True,
    )
    
    # LoRA config
    peft_config = LoraConfig(
        r=16,
        lora_alpha=32,
        lora_dropout=0.05,
        bias="none",
        task_type="CAUSAL_LM",
        target_modules=['up_proj', 'down_proj', 'gate_proj', 'k_proj', 'q_proj', 'v_proj', 'o_proj']
    )
    
    # Load tokenizer and format chat template
    tokenizer = AutoTokenizer.from_pretrained(base_model, token=token)
    
    # Format dataset with chat template
    def format_chat_template(row):
        # Check if input is already formatted as a conversation
        if isinstance(row["chosen"], list) and all(isinstance(x, dict) for x in row["chosen"]):
            # Already formatted as a conversation, direct application
            chosen_messages = row["chosen"]
            rejected_messages = row["rejected"]
        else:
            # Simple text format - wrap in system message + user query format
            chosen_messages = [
                {"role": "user", "content": row["chosen"]}
            ]
            rejected_messages = [
                {"role": "user", "content": row["rejected"]}
            ]
            
        # Apply tokenizer's chat template if available, otherwise create a basic format
        try:
            row["chosen"] = tokenizer.apply_chat_template(chosen_messages, tokenize=False)
            row["rejected"] = tokenizer.apply_chat_template(rejected_messages, tokenize=False)
        except Exception as e:
            print(f"Error applying chat template: {e}")
            # Fallback format if chat template is not available
            row["chosen"] = "\n".join([f"{msg['role']}: {msg['content']}" for msg in chosen_messages])
            row["rejected"] = "\n".join([f"{msg['role']}: {msg['content']}" for msg in rejected_messages])
            
        return row
    
    dataset = dataset.map(format_chat_template, num_proc=os.cpu_count())
    dataset = dataset.train_test_split(test_size=0.1, seed=42)  # Use 10% as test set
    
    print(f"Evaluation dataset size: {len(dataset['test'])}")
    
    # === BEFORE FINE-TUNING: EVALUATE BASE MODEL ===
    print("=== Evaluating Base Model ===")
    base_model_for_eval = AutoModelForCausalLM.from_pretrained(
        base_model,
        device_map="auto",
        torch_dtype=torch.float16,
        token=token
    )
    
    # Don't call setup_chat_format again if chat template is already set
    if not hasattr(tokenizer, 'chat_template') or tokenizer.chat_template is None:
        base_model_for_eval, tokenizer = setup_chat_format(base_model_for_eval, tokenizer)
    
    base_results = evaluate_model(base_model_for_eval, tokenizer, dataset["test"], num_samples=50)
    print("Base Model Results:", json.dumps(base_results, indent=2))
    
    # Delete base model to free up memory
    del base_model_for_eval
    torch.cuda.empty_cache()
    
    # === TRAIN WITH ORPO ===
    print("=== Training with ORPO ===")
    
    # Check available GPU memory
    if torch.cuda.is_available():
        free_memory = torch.cuda.get_device_properties(0).total_memory - torch.cuda.memory_allocated(0)
        print(f"Free GPU memory: {free_memory / (1024**3):.2f} GB")
    
    # Adjust bnb_config based on available memory
    use_4bit = True
    try:
        model = AutoModelForCausalLM.from_pretrained(
            base_model,
            quantization_config=bnb_config if use_4bit else None,
            device_map="auto",
            token=token,
            low_cpu_mem_usage=True,
        )
    except ValueError as e:
        print(f"Error loading model with 4-bit quantization: {e}")
        print("Falling back to 8-bit quantization")
        use_4bit = False
        bnb_config = BitsAndBytesConfig(
            load_in_8bit=True,
            bnb_8bit_use_double_quant=True,
        )
        try:
            model = AutoModelForCausalLM.from_pretrained(
                base_model,
                quantization_config=bnb_config,
                device_map="auto",
                token=token,
                low_cpu_mem_usage=True,
            )
        except ValueError as e:
            print(f"Error loading model with 8-bit quantization: {e}")
            print("Falling back to CPU or mixed precision training")
            model = AutoModelForCausalLM.from_pretrained(
                base_model,
                torch_dtype=torch.float16,
                device_map={"": "cpu"}, # Start on CPU
                token=token,
                low_cpu_mem_usage=True,
            )
            
    # Setup chat format if not already set        
    if not hasattr(tokenizer, 'chat_template') or tokenizer.chat_template is None:
        model, tokenizer = setup_chat_format(model, tokenizer)
        
    if use_4bit:
        model = prepare_model_for_kbit_training(model)
    
    orpo_args = ORPOConfig(
        learning_rate=8e-6,
        lr_scheduler_type="linear",
        max_length=1024,
        max_prompt_length=512,
        beta=0.1,
        per_device_train_batch_size=1,  # Reduced batch size
        per_device_eval_batch_size=1,   # Reduced batch size
        gradient_accumulation_steps=8,  # Increased to compensate for smaller batch size
        optim="paged_adamw_8bit",
        num_train_epochs=1,
        eval_steps=0.2,
        logging_steps=1,
        warmup_steps=10,
        output_dir="./results/",
    )
    
    trainer = ORPOTrainer(
        model=model,
        args=orpo_args,
        train_dataset=dataset["train"],
        eval_dataset=dataset["test"],
        peft_config=peft_config,
        processing_class=tokenizer,
    )
    
    trainer.train()
    trainer.save_model(new_model)
    
    # Delete trained model to free up memory
    del model
    torch.cuda.empty_cache()
    
    # === AFTER FINE-TUNING: EVALUATE FINE-TUNED MODEL ===
    print("=== Evaluating Fine-tuned Model ===")
    
    # Load the base model in fp16
    base_fp16_model = AutoModelForCausalLM.from_pretrained(
        base_model,
        low_cpu_mem_usage=True,
        return_dict=True,
        torch_dtype=torch.float16,
        device_map="auto",
        token=token
    )
    base_fp16_model, tokenizer = setup_chat_format(base_fp16_model, tokenizer)
    
    # Merge adapter with base model
    fine_tuned_model = PeftModel.from_pretrained(base_fp16_model, new_model)
    fine_tuned_model = fine_tuned_model.merge_and_unload()
    
    # Evaluate fine-tuned model
    fine_tuned_results = evaluate_model(fine_tuned_model, tokenizer, dataset["test"], num_samples=50)
    print("Fine-tuned Model Results:", json.dumps(fine_tuned_results, indent=2))
    
    # Compare results
    print("\n=== Comparison ===")
    print(f"Base Model Preference Accuracy: {base_results['preference_accuracy']:.4f}")
    print(f"Fine-tuned Model Preference Accuracy: {fine_tuned_results['preference_accuracy']:.4f}")
    print(f"Improvement: {fine_tuned_results['preference_accuracy'] - base_results['preference_accuracy']:.4f}")
    
    # Save results to file
    comparison = {
        "base_model": base_results,
        "fine_tuned_model": fine_tuned_results,
        "improvement": fine_tuned_results['preference_accuracy'] - base_results['preference_accuracy']
    }
    
    with open("orpo_comparison_results.json", "w") as f:
        json.dump(comparison, f, indent=2)
    
    print("Results saved to orpo_comparison_results.json")
    
    # Generate human-readable report
    markdown_report = f"""
# ORPO Fine-tuning Performance Comparison

## Base Model vs. Fine-tuned Model

| Metric | Base Model | Fine-tuned Model | Improvement |
|--------|------------|-----------------|-------------|
| Preference Accuracy | {base_results['preference_accuracy']:.4f} | {fine_tuned_results['preference_accuracy']:.4f} | {fine_tuned_results['preference_accuracy'] - base_results['preference_accuracy']:.4f} |
| Avg Chosen Score | {base_results['avg_chosen_score']:.4f} | {fine_tuned_results['avg_chosen_score']:.4f} | {fine_tuned_results['avg_chosen_score'] - base_results['avg_chosen_score']:.4f} |
| Avg Rejected Score | {base_results['avg_rejected_score']:.4f} | {fine_tuned_results['avg_rejected_score']:.4f} | {fine_tuned_results['avg_rejected_score'] - base_results['avg_rejected_score']:.4f} |
| Chosen Better | {base_results['chosen_better']} | {fine_tuned_results['chosen_better']} | {fine_tuned_results['chosen_better'] - base_results['chosen_better']} |
| Rejected Better | {base_results['rejected_better']} | {fine_tuned_results['rejected_better']} | {base_results['rejected_better'] - fine_tuned_results['rejected_better']} |
| Ties | {base_results['tie']} | {fine_tuned_results['tie']} | {fine_tuned_results['tie'] - base_results['tie']} |

## Interpretation

The preference accuracy indicates how often the model assigns a higher score to the human-preferred response compared to the rejected response. A higher preference accuracy suggests the model is better aligned with human preferences.

The ORPO fine-tuning process {"improved" if comparison["improvement"] > 0 else "did not improve"} the model's alignment with human preferences by {abs(comparison["improvement"]):.4f} or {abs(comparison["improvement"]*100):.2f}%.
"""
    
    with open("orpo_comparison_report.md", "w") as f:
        f.write(markdown_report)
    
    print("Detailed report saved to orpo_comparison_report.md")

if __name__ == "__main__":
    main()