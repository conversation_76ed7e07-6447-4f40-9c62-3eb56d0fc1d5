{"cells": [{"cell_type": "code", "execution_count": null, "id": "0182694c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".conda", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}