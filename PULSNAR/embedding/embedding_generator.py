#!/usr/bin/env python3
"""
Medical Text Embedding Generator
Generates embeddings for medical text data using SentenceTransformers
With incremental saving and resumable processing
"""

import argparse
import os
import pandas as pd
import numpy as np
from tqdm import tqdm
from sentence_transformers import SentenceTransformer
import pickle
from pathlib import Path
import logging
import sys
from typing import Optional, List, Dict, Any, Tuple
import transformers

# Setup the logging for the transformer so that it doesnot log the Batches processing
transformers.logging.set_verbosity_error()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MedicalTextEmbeddingGenerator:
    """Main class for generating embeddings from medical text data"""
    
    def __init__(self, model_name: str = 'Qwen/Qwen3-Embedding-0.6B', 
                 batch_size: int = 1,
                 subject_id_col: str = 'SUBJECT_ID', text_col: str = 'TEXT'):
        """
        Initialize the embedding generator
        
        Args:
            model_name (str): Name of the SentenceTransformer model
            batch_size (int): Batch size for processing
            subject_id_col (str): Name of subject ID column
            text_col (str): Name of text column
        """
        self.model_name = model_name
        self.batch_size = batch_size
        self.subject_id_col = subject_id_col
        self.text_col = text_col
        self.model = None
        
        # Check CUDA device from environment
        cuda_device = os.environ.get("CUDA_VISIBLE_DEVICES", "0")
        logger.info(f'Using CUDA_VISIBLE_DEVICES: {cuda_device}')
        
    def load_model(self):
        """Load the SentenceTransformer model"""
        logger.info(f"Loading model: {self.model_name}...")
        try:
            self.model = SentenceTransformer(self.model_name)
            logger.info("Model loaded successfully")
        except Exception as e:
            logger.error(f"Failed to load model: {str(e)}")
            raise
    
    def load_data(self, input_path: str) -> pd.DataFrame:
        """
        Load input dataframe
        
        Args:
            input_path (str): Path to input pickle file
            
        Returns:
            pd.DataFrame: Loaded dataframe
        """
        logger.info(f"Loading data from: {input_path}")
        
        if not Path(input_path).exists():
            raise FileNotFoundError(f"Input file not found: {input_path}")
        
        try:
            df = pd.read_pickle(input_path)
            logger.info(f"Loaded dataframe with {len(df)} rows")
            
            # Validate required columns exist
            required_cols = [self.subject_id_col, self.text_col]
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                raise ValueError(f"Missing required columns: {missing_cols}")
            
            return df
            
        except Exception as e:
            logger.error(f"Failed to load data: {str(e)}")
            raise
    
    def initialize_dataframe_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Initialize embedding columns in dataframe if they don't exist
        
        Args:
            df (pd.DataFrame): Input dataframe
            
        Returns:
            pd.DataFrame: DataFrame with initialized columns
        """
        logger.info("Initializing embedding columns...")
        
        if "individual_embeddings" not in df.columns:
            df["individual_embeddings"] = [None] * len(df)
            logger.info("Added individual_embeddings column")
            
        if "average_embedding" not in df.columns:
            df["average_embedding"] = [None] * len(df)
            logger.info("Added average_embedding column")
            
        return df
    
    def load_existing_embeddings(self, individual_file: str, average_file: str, 
                                progress_file: str) -> Tuple[Dict, Dict, set]:
        """
        Load existing embeddings and progress from files
        
        Args:
            individual_file (str): Path to individual embeddings file
            average_file (str): Path to average embeddings file
            progress_file (str): Path to progress file
            
        Returns:
            tuple: (individual_embeddings_dict, average_embeddings_dict, processed_subject_ids)
        """
        # Load existing individual embeddings
        try:
            with open(individual_file, 'rb') as f:
                individual_embeddings_dict = pickle.load(f)
            logger.info(f"Loaded {len(individual_embeddings_dict)} existing individual embeddings")
        except FileNotFoundError:
            individual_embeddings_dict = {}
            logger.info("Starting fresh - no existing individual embeddings found")
        
        # Load existing average embeddings
        try:
            with open(average_file, 'rb') as f:
                average_embeddings_dict = pickle.load(f)
            logger.info(f"Loaded {len(average_embeddings_dict)} existing average embeddings")
        except FileNotFoundError:
            average_embeddings_dict = {}
            logger.info("Starting fresh - no existing average embeddings found")
        
        # Load progress
        try:
            with open(progress_file, 'rb') as f:
                processed_subject_ids = pickle.load(f)
            logger.info(f"Resuming - already processed {len(processed_subject_ids)} SUBJECT_IDs")
        except FileNotFoundError:
            processed_subject_ids = set()
            logger.info("Starting from the beginning")
        
        return individual_embeddings_dict, average_embeddings_dict, processed_subject_ids
    
    def process_text_list_in_batches(self, text_list: List[str]) -> np.ndarray:
        """
        Process a list of texts in batches and return all embeddings
        
        Args:
            text_list (List[str]): List of texts to process
            
        Returns:
            np.ndarray: Array of embeddings
        """
        try:
            all_embeddings = []
            for i in range(0, len(text_list), self.batch_size):
                batch = text_list[i:i + self.batch_size]
                batch_embeddings = self.model.encode(batch)
                all_embeddings.extend(batch_embeddings)
            return np.array(all_embeddings)
            
        except Exception as e:
            logger.warning(f"Batch processing failed, trying with reduced batch size: {str(e)}")
            try:
                all_embeddings = []
                reduced_batch_size = max(1, self.batch_size // 2)
                for i in range(0, len(text_list), reduced_batch_size):
                    batch = text_list[i:i + reduced_batch_size]
                    batch_embeddings = self.model.encode(batch)
                    all_embeddings.extend(batch_embeddings)
                return np.array(all_embeddings)
                
            except Exception as e2:
                logger.error(f"Reduced batch processing also failed: {str(e2)}")
                # Get embedding dimension and return fallback embeddings
                try:
                    sample_embedding = self.model.encode(["sample"])
                    embedding_dim = len(sample_embedding[0])
                    fallback_embeddings = [[-1] * embedding_dim for _ in text_list]
                    logger.warning(f"Using fallback embeddings with dimension {embedding_dim}")
                    return np.array(fallback_embeddings)
                except Exception as e3:
                    logger.error(f"Even fallback failed: {str(e3)}")
                    # Use default dimension
                    fallback_embeddings = [[-1] * 768 for _ in text_list]
                    logger.warning("Using default 768-dimensional fallback embeddings")
                    return np.array(fallback_embeddings)
    
    def save_embeddings_incrementally(self, subject_id: Any, individual_emb: List, 
                                    average_emb: List, individual_file: str, 
                                    average_file: str, progress_file: str,
                                    individual_embeddings_dict: Dict, 
                                    average_embeddings_dict: Dict,
                                    processed_subject_ids: set):
        """
        Save embeddings incrementally for the current subject
        
        Args:
            subject_id: Subject ID
            individual_emb: Individual embeddings
            average_emb: Average embedding
            individual_file: Path to individual embeddings file
            average_file: Path to average embeddings file
            progress_file: Path to progress file
            individual_embeddings_dict: Dictionary of individual embeddings
            average_embeddings_dict: Dictionary of average embeddings
            processed_subject_ids: Set of processed subject IDs
        """
        # Update dictionaries
        individual_embeddings_dict[subject_id] = individual_emb
        average_embeddings_dict[subject_id] = average_emb
        
        # Save dictionaries
        with open(individual_file, 'wb') as f:
            pickle.dump(individual_embeddings_dict, f)
        
        with open(average_file, 'wb') as f:
            pickle.dump(average_embeddings_dict, f)
        
        # Save progress
        processed_subject_ids.add(subject_id)
        with open(progress_file, 'wb') as f:
            pickle.dump(processed_subject_ids, f)
    
    def process_single_text(self, text: Any) -> Tuple[List, List]:
        """
        Process a single text entry and return individual and average embeddings
        
        Args:
            text: Text data (can be list, string, or other)
            
        Returns:
            tuple: (individual_embeddings, average_embedding)
        """
        if isinstance(text, list) and len(text) > 0:
            # Process list of texts
            text_embeddings = self.process_text_list_in_batches(text)
            individual_emb = text_embeddings.tolist()
            avg_embedding = np.mean(text_embeddings, axis=0)
            average_emb = avg_embedding.tolist()
            
        elif isinstance(text, str):
            # Process single string
            embedding = self.model.encode([text])[0]
            individual_emb = [embedding.tolist()]
            average_emb = embedding.tolist()
            
        else:
            # Handle empty or None values
            embedding_dim = self.model.get_sentence_embedding_dimension()
            zero_embedding = np.zeros(embedding_dim)
            individual_emb = [zero_embedding.tolist()]
            average_emb = zero_embedding.tolist()
        
        return individual_emb, average_emb
    
    def generate_embeddings(self, input_path: str, output_path: str, 
                          output_dir: str = "./") -> pd.DataFrame:
        """
        Main method to generate embeddings
        
        Args:
            input_path (str): Path to input pickle file
            output_path (str): Path to output pickle file
            output_dir (str): Directory for intermediate files
            
        Returns:
            pd.DataFrame: DataFrame with embeddings
        """
        logger.info("Starting embedding generation process")
        
        # Load model
        self.load_model()
        
        # Load data
        df = self.load_data(input_path)
        df = self.initialize_dataframe_columns(df)
        
        # Setup file paths for incremental saving
        model_suffix = self.model_name.split('/')[-1].replace('-', '_').replace('.', '_')
        individual_file = Path(output_dir) / f'individual_embeddings_{model_suffix}.pkl'
        average_file = Path(output_dir) / f'average_embeddings_{model_suffix}.pkl'
        progress_file = Path(output_dir) / f'progress_{model_suffix}.pkl'
        
        # Load existing embeddings
        individual_embeddings_dict, average_embeddings_dict, processed_subject_ids = \
            self.load_existing_embeddings(individual_file, average_file, progress_file)
        
        # Process each row
        logger.info("Processing embeddings...")
        for idx, row in tqdm(df.iterrows(), total=len(df), desc="Processing rows"):
            subject_id = row[self.subject_id_col]
            
            # Skip if already processed
            if subject_id in processed_subject_ids:
                continue
            
            text = row[self.text_col]
            
            # Process text and get embeddings
            individual_emb, average_emb = self.process_single_text(text)
            
            # Save incrementally
            self.save_embeddings_incrementally(
                subject_id, individual_emb, average_emb,
                individual_file, average_file, progress_file,
                individual_embeddings_dict, average_embeddings_dict,
                processed_subject_ids
            )
        
        # Merge embeddings back into dataframe
        logger.info("Merging embeddings back into dataframe...")
        for idx, row in df.iterrows():
            subject_id = row[self.subject_id_col]
            if subject_id in individual_embeddings_dict:
                df.at[idx, "individual_embeddings"] = individual_embeddings_dict[subject_id]
            if subject_id in average_embeddings_dict:
                df.at[idx, "average_embedding"] = average_embeddings_dict[subject_id]
        
        # Save final dataframe
        logger.info(f"Saving final dataframe to: {output_path}")
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        df.to_pickle(output_path)
        
        # Log final statistics
        logger.info(f"Saved dataframe with {len(df)} rows")
        logger.info(f"Individual embeddings column contains lists of embeddings for each text element")
        logger.info(f"Average embedding column contains single averaged embedding per row")
        logger.info(f"Used batch processing with batch size: {self.batch_size}")
        logger.info("Processing completed with incremental saving")

        return df


def main(input_path, output_path, model_name, batch_size, output_dir, subject_id_col, text_col, verbose) -> None:
    """
    Main pipeline for generating embeddings from medical text data.
    
    Args:
        input_path (str): Path to input pickle file
        output_path (str): Path to output pickle file
        model_name (str): Name of the SentenceTransformer model
        batch_size (int): Batch size for processing
        output_dir (str): Directory for intermediate files
        subject_id_col (str): Name of subject ID column
        text_col (str): Name of text column
        verbose (bool): Enable verbose logging
    """
    # Set logging level
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        # Initialize generator
        generator = MedicalTextEmbeddingGenerator(
            model_name=model_name,
            batch_size=batch_size,
            subject_id_col=subject_id_col,
            text_col=text_col
        )
        
        # Generate embeddings
        result_df = generator.generate_embeddings(
            input_path=input_path,
            output_path=output_path,
            output_dir=output_dir
        )
        
        logger.info("Successfully completed embedding generation!")
        
    except Exception as e:
        logger.error(f"Embedding generation failed: {str(e)}")
        sys.exit(1)


def parse_arguments() -> argparse.Namespace:
    """
    Parse command line arguments.
    
    Returns:
        argparse.Namespace: Parsed arguments
    """
    parser = argparse.ArgumentParser(
        description="Generate embeddings for medical text data using SentenceTransformers",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    # File paths
    parser.add_argument(
        '--input-path',
        type=str,
        default='./PULSNAR/result/icd_note_word.pkl',
        help='Path to input pickle file'
    )
    
    parser.add_argument(
        '--output-path',
        type=str,
        default='./PULSNAR/result/icd_with_embeddings.pkl',
        help='Path to output pickle file'
    )
    
    parser.add_argument(
        '--output-dir',
        type=str,
        default='./PULSNAR/result',
        help='Directory for intermediate files'
    )
    
    # Model configuration
    parser.add_argument(
        '--model-name',
        type=str,
        default='Qwen/Qwen3-Embedding-0.6B',
        help='Name of the SentenceTransformer model'
    )
    
    parser.add_argument(
        '--batch-size',
        type=int,
        default=1,
        help='Batch size for processing'
    )
    
    # Column configuration
    parser.add_argument(
        '--subject-id-col',
        type=str,
        default='SUBJECT_ID',
        help='Name of subject ID column'
    )
    
    parser.add_argument(
        '--text-col',
        type=str,
        default='TEXT',
        help='Name of text column'
    )
    
    # Other options
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    
    return parser.parse_args()


if __name__ == "__main__":
    # Parse command line arguments
    args = parse_arguments()
    
    # Run main embedding generation pipeline
    main(
        input_path=args.input_path,
        output_path=args.output_path,
        model_name=args.model_name,
        batch_size=args.batch_size,
        output_dir=args.output_dir,
        subject_id_col=args.subject_id_col,
        text_col=args.text_col,
        verbose=args.verbose
    )