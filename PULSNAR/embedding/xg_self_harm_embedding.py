import os
os.environ["CUDA_VISIBLE_DEVICES"]="1"
print(f'CUDA_VISIBLE_DEVICES {os.environ["CUDA_VISIBLE_DEVICES"]}')
import re
import sys
import numpy as np
import pandas as pd
from tqdm import tqdm
from sklearn.model_selection import StratifiedKFold
from sklearn.preprocessing import MultiLabelBinarizer
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score,confusion_matrix
import xgboost as xgb
np.random.seed(42)
f_dir = "fold_covariate"
# Create fold directory if it doesn't exist
os.makedirs(f_dir, exist_ok=True)
def evaluate(y_true, y_pred, save_path,title="Covariates embedding", plot_cm=True):
    conf_matrix = confusion_matrix(y_true, y_pred)
    # Reorder confusion matrix to have positive class first
    reordered_conf_matrix = np.array(
        [
            [conf_matrix[1, 0], conf_matrix[1, 1]],
            [conf_matrix[0, 0], conf_matrix[0, 1]],
        ]
    )
    cm_df = pd.DataFrame(
        reordered_conf_matrix, index=["1", "0"], columns=["0", "1"]
    )
    if plot_cm:
        plt.figure(figsize=(8, 6))
        sns.heatmap(
            cm_df,
            annot=True,
            fmt="d",
            cmap="Blues",
            cbar=True,
            linewidths=1,
            linecolor="black",
        )
        plt.ylabel("True Label")
        plt.xlabel("Predicted Label")
        plt.title(title)
        plt.savefig(save_path, bbox_inches="tight", dpi=300)
        print(f"Confusion matrix saved to {save_path}")

        # plt.show()
        plt.close()

file_path = "./embedding_0.6b.pkl"
df = pd.read_pickle(file_path)


# Print class distribution before balancing
print(f"Original class distribution:")
print(f"Class 0: {sum(df['labels'] == 0)}")
print(f"Class 1: {sum(df['labels'] == 1)}")
average_embedding = df["average_embedding"].to_list()
# Prepare data for modeling
x = np.asarray(average_embedding)
y = np.array(df["labels"])

# Calculate scale_pos_weight for class imbalance
scale_pos_weight = (len(y) - sum(y)) / sum(y)
print(f"Scale pos weight: {scale_pos_weight:.2f}")

# Define XGBoost classifier
model = xgb.XGBClassifier(
    objective='binary:logistic',
    n_estimators=200,
    max_depth=6,  
    learning_rate=0.01,
    reg_alpha=0.1,
    reg_lambda=0.1,
    random_state=42,
    scale_pos_weight=scale_pos_weight  
)

# Perform 5-fold cross-validation
print("\nPerforming 5-fold cross-validation...")
skf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)

# Store results for each fold
cv_results = {
    'accuracy': [],
    'precision': [],
    'recall': [],
    'f1': [],
}

# List to store all fold results for combined output
all_fold_results = []

fold = 1
for train_idx, val_idx in tqdm(skf.split(x, y), total=5, desc="Cross-validation folds"):
    
    X_train_fold, X_val_fold = x[train_idx], x[val_idx]
    y_train_fold, y_val_fold = y[train_idx], y[val_idx]
    
    # Train model
    model.fit(X_train_fold, y_train_fold)
    
    # Make predictions
    y_pred = model.predict(X_val_fold)
    y_pred_proba = model.predict_proba(X_val_fold)
    
    # Create DataFrame for this fold's results
    fold_df = pd.DataFrame({
        'SUBJECT_ID': df.iloc[val_idx]['SUBJECT_ID'],
        'labels': y_val_fold,
        'pred': y_pred,
        'prob_1': y_pred_proba[:, 1], # Probability of class 1
    })

    all_fold_results.append(fold_df)    
    fold += 1

# Combine all fold results into a single DataFrame
df_combined = pd.concat(all_fold_results, ignore_index=True)
df_combined_path = os.path.join(f_dir, "combined_5fold_results.csv")
df_combined.to_csv(df_combined_path, index=False)

# Calculate metrics
evaluate(y_true=df_combined["labels"], y_pred=df_combined["pred"], save_path=f"{f_dir}/cm.png")
accuracy = accuracy_score(y_true=df_combined["labels"], y_pred=df_combined["pred"])
precision = precision_score(y_true=df_combined["labels"], y_pred=df_combined["pred"])
recall = recall_score(y_true=df_combined["labels"], y_pred=df_combined["pred"])
f1 = f1_score(y_true=df_combined["labels"], y_pred=df_combined["pred"])
print(f"Accuracy: {accuracy:.4f}")
print(f"Precision: {precision:.4f}")
print(f"Recall: {recall:.4f}")
print(f"F1-score: {f1:.4f}")

print(f"\nCombined results from all 5 folds saved to {df_combined_path}")
