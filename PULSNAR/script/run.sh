python PULSNAR/encoding/process_medical_data.py \
    --diagnosis-path "./datasets/1.4/DIAGNOSES_ICD.csv" \
    --notes-path "./datasets/1.4/NOTEEVENTS.csv" \
    --output-path "./PULSNAR/result/icd_note_word.pkl" \
    --random-seed 45 \
    --subject-id-col "SUBJECT_ID" \
    --icd-code-col "ICD9_CODE" \
    --text-col "TEXT" \
    --labels-col "labels" \
    --icd-encoding-col "self_harm_icd_encod" \
    --verbose

export CUDA_VISIBLE_DEVICES=1

python PULSNAR/embedding/embedding_generator.py \
    --input-path "./PULSNAR/result/icd_note_word_small.pkl" \
    --output-path "./PULSNAR/result/icd_with_embeddings_qwen_0.6b_small.pkl" \
    --model-name "Qwen/Qwen3-Embedding-0.6B" \
    --batch-size 10 \
    --output-dir "./PULSNAR/result" \
    --subject-id-col "SUBJECT_ID" \
    --text-col "TEXT" \
    --verbose

python PULSNAR/balance/embedding_ml_processor.py \
    --embedding-file "./PULSNAR/result/embedding_0.6b.pkl" \
    --icd-file "./PULSNAR/result/icd_note_word.pkl" \
    --output-dir "PULSNAR/balance/IOData/MLData" \
    --subject-id-col "SUBJECT_ID" \
    --embedding-col "average_embedding" \
    --onehot-col "self_harm_icd_encod" \
    --labels-col "labels" \
    --feature-type "icd_code"


python PULSNAR/balance/balance_pulsnar_code.py


