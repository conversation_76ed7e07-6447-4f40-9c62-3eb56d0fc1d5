import os
import pickle
import pandas as pd
import numpy as np
import argparse
from typing import Dict, <PERSON><PERSON>, Optional


def load_embedding_data(file_path: str, subject_id_col: str = "SUBJECT_ID", 
                       embedding_col: str = "average_embedding") -> Tuple[pd.DataFrame, Dict]:
    """
    Load embedding data from pickle file and create subject ID to embedding mapping.
    
    Args:
        file_path (str): Path to the embedding pickle file
        subject_id_col (str): Name of the subject ID column
        embedding_col (str): Name of the embedding column
        
    Returns:
        Tuple[pd.DataFrame, Dict]: DataFrame with embedding data and mapping dictionary
    """
    print(f"Loading embedding data from: {file_path}")
    df = pd.read_pickle(file_path)
    
    # Validate required columns exist
    required_cols = [subject_id_col, embedding_col]
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        raise ValueError(f"Missing required columns in embedding file: {missing_cols}")
    
    encoding_map = dict(zip(df[subject_id_col], df[embedding_col]))
    print(f"Loaded {len(df)} embedding records")
    return df, encoding_map


def process_icd_data(icd_file_path: str, encoding_map: Dict, subject_id_col: str, embedding_col: str, onehot_col: str, feature_type: str) -> pd.DataFrame:
    """
    Process ICD note data by mapping embeddings and combining with one-hot encodings.
    
    Args:
        icd_file_path (str): Path to the ICD note word pickle file
        encoding_map (Dict): Mapping from SUBJECT_ID to average embedding
        subject_id_col (str): Name of the subject ID column
        embedding_col (str): Name of the embedding column to create
        onehot_col (str): Name of the one-hot encoding column
        feature_type (str): Type of features to create ('icd_code', 'embedding', 'combined')
        
    Returns:
        pd.DataFrame: Processed DataFrame with combined features
    """
    print(f"Loading ICD data from: {icd_file_path}")
    df2 = pd.read_pickle(icd_file_path)
    
    # Validate required columns exist based on feature type
    required_cols = [subject_id_col]
    if feature_type in ['icd_code', 'combined']:
        required_cols.append(onehot_col)
    
    missing_cols = [col for col in required_cols if col not in df2.columns]
    if missing_cols:
        raise ValueError(f"Missing required columns in ICD file: {missing_cols}")
    
    # Map embeddings to subjects if needed
    if feature_type in ['embedding', 'combined']:
        df2[embedding_col] = None
        df2[embedding_col] = df2[embedding_col].fillna(df2[subject_id_col].map(encoding_map))
        
        # Remove rows without embeddings
        initial_count = len(df2)
        df2 = df2[~df2[embedding_col].isna()].reset_index(drop=True).copy()
        final_count = len(df2)
        print(f"Filtered ICD data: {initial_count} -> {final_count} records")
        
        # Convert embeddings to numpy arrays
        df2[embedding_col] = df2[embedding_col].apply(lambda row: np.array(row))
    
    # Create features based on feature_type
    if feature_type == 'icd_code':
        print("Creating ICD code features...")
        df2['features'] = df2[onehot_col].apply(lambda x: np.array(x))
    elif feature_type == 'embedding':
        print("Creating embedding features...")
        df2['features'] = df2[embedding_col]
    elif feature_type == 'combined':
        print("Creating combined features...")
        df2['features'] = df2.apply(
            lambda row: np.concatenate([row[embedding_col], row[onehot_col]]), 
            axis=1
        )
    else:
        raise ValueError(f"Invalid feature_type: {feature_type}. Must be 'icd_code', 'embedding', or 'combined'")
    
    print(f"Feature type: {feature_type}")
    print(f"Using columns: {subject_id_col}, {embedding_col if feature_type != 'icd_code' else 'N/A'}, {onehot_col if feature_type != 'embedding' else 'N/A'}")
    return df2


def prepare_ml_data(df_embeddings: pd.DataFrame, df_processed: pd.DataFrame,
                   labels_col: str = "labels",
                   features_col: str = "features") -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    Prepare features, labels, and person IDs for machine learning.
    
    Args:
        df_embeddings (pd.DataFrame): DataFrame with embedding data containing labels
        df_processed (pd.DataFrame): Processed DataFrame with features
        labels_col (str): Name of the labels column
        features_col (str): Name of the features column
        
    Returns:
        Tuple[np.ndarray, np.ndarray, np.ndarray]: Features, labels, and person IDs
    """
    print("Preparing ML data arrays...")
    
    # Validate required columns exist
    if labels_col not in df_embeddings.columns:
        raise ValueError(f"Missing labels column '{labels_col}' in embedding DataFrame")
    if features_col not in df_processed.columns:
        raise ValueError(f"Missing features column '{features_col}' in processed DataFrame")
    
    # Extract features
    features = np.array(df_processed[features_col].to_list())
    
    # Extract labels from embedding DataFrame
    labels = np.array(df_embeddings[labels_col].to_list())
    
    # Create person IDs (assuming sequential numbering)
    persons = np.array([x for x in range(len(df_embeddings))])
    
    print(f"Features shape: {features.shape}")
    print(f"Labels shape: {labels.shape}")
    print(f"Persons shape: {persons.shape}")
    
    return features, labels, persons


def save_ml_data(features: np.ndarray, labels: np.ndarray, persons: np.ndarray, 
                 output_dir: str) -> None:
    """
    Save ML data arrays to pickle files.
    
    Args:
        features (np.ndarray): Feature array
        labels (np.ndarray): Label array
        persons (np.ndarray): Person ID array
        output_dir (str): Output directory path
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    print(f"Saving ML data to: {output_dir}")
    
    # Save features
    features_path = os.path.join(output_dir, "features.pkl")
    with open(features_path, 'wb') as f:
        pickle.dump(features, f)
    print(f"Saved features to: {features_path}")
    
    # Save labels
    labels_path = os.path.join(output_dir, "labels.pkl")
    with open(labels_path, 'wb') as f:
        pickle.dump(labels, f)
    print(f"Saved labels to: {labels_path}")
    
    # Save persons
    persons_path = os.path.join(output_dir, "persons.pkl")
    with open(persons_path, 'wb') as f:
        pickle.dump(persons, f)
    print(f"Saved persons to: {persons_path}")


def main(embedding_file: str, icd_file: str, output_dir: str,
         subject_id_col: str = "SUBJECT_ID",
         embedding_col: str = "average_embedding",
         onehot_col: str = "one_hot_encoding",
         labels_col: str = "labels",
         feature_type: str = "combined") -> None:
    """
    Main processing pipeline for embedding and ICD data.
    
    Args:
        embedding_file (str): Path to embedding pickle file
        icd_file (str): Path to ICD note word pickle file
        output_dir (str): Output directory for ML data
        subject_id_col (str): Name of the subject ID column
        embedding_col (str): Name of the embedding column
        onehot_col (str): Name of the one-hot encoding column
        labels_col (str): Name of the labels column
        feature_type (str): Type of features to create ('icd_code', 'embedding', 'combined')
    """
    try:
        # Load embedding data and create mapping
        df_embeddings, encoding_map = load_embedding_data(embedding_file, subject_id_col, embedding_col)
        
        # Process ICD data with embeddings
        df_processed = process_icd_data(icd_file, encoding_map, subject_id_col, embedding_col, onehot_col, feature_type)
        
        # Prepare ML data arrays
        features, labels, persons = prepare_ml_data(df_embeddings, df_processed, labels_col, "features")
        
        # Save ML data
        save_ml_data(features, labels, persons, output_dir)
        
        print("Processing completed successfully!")
        
    except Exception as e:
        print(f"Error during processing: {str(e)}")
        raise


def parse_arguments() -> argparse.Namespace:
    """
    Parse command line arguments.
    
    Returns:
        argparse.Namespace: Parsed arguments
    """
    parser = argparse.ArgumentParser(
        description="Process embedding and ICD data for machine learning",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    # File paths
    parser.add_argument(
        "--embedding-file",
        type=str,
        default="./embedding_0.6b.pkl",
        help="Path to the embedding pickle file"
    )
    
    parser.add_argument(
        "--icd-file",
        type=str,
        default="./icd_note_word.pkl",
        help="Path to the ICD note word pickle file"
    )
    
    parser.add_argument(
        "--output-dir",
        type=str,
        default="PULSNAR/balance/IOData/MLData",
        help="Output directory for ML data files"
    )
    
    # Column names
    parser.add_argument(
        "--subject-id-col",
        type=str,
        default="SUBJECT_ID",
        help="Name of the subject ID column"
    )
    
    parser.add_argument(
        "--embedding-col",
        type=str,
        default="average_embedding",
        help="Name of the embedding column"
    )
    
    parser.add_argument(
        "--onehot-col",
        type=str,
        default="one_hot_encoding",
        help="Name of the one-hot encoding column"
    )
    
    parser.add_argument(
        "--labels-col",
        type=str,
        default="labels",
        help="Name of the labels column"
    )
    
    # Feature type selection
    parser.add_argument(
        "--feature-type",
        type=str,
        choices=['icd_code', 'embedding', 'combined'],
        default="combined",
        help="Type of features to create: 'icd_code' (only ICD one-hot encoding), 'embedding' (only average embedding), or 'combined' (both concatenated)"
    )
    
    return parser.parse_args()


if __name__ == "__main__":
    # Parse command line arguments
    args = parse_arguments()
    
    # Run main processing pipeline
    main(
        embedding_file=args.embedding_file,
        icd_file=args.icd_file,
        output_dir=args.output_dir,
        subject_id_col=args.subject_id_col,
        embedding_col=args.embedding_col,
        onehot_col=args.onehot_col,
        labels_col=args.labels_col,
        feature_type=args.feature_type
    )