import os
os.chdir("PULSNAR/balance")

os.environ['OPENBLAS_NUM_THREADS'] = '8'
os.environ['OMP_NUM_THREADS'] = '8'
os.environ['MKL_NUM_THREADS'] = '8'
os.environ['NUM_THREADS'] = '8'

import pandas as pd
from PULSNAR import PULSNAR
from sklearn.utils import shuffle
import pickle
import sys
import numpy as np
import yaml
import argparse
from xgboost import XGBClassifier
from sklearn.metrics import average_precision_score, roc_auc_score, brier_score_loss, accuracy_score, matthews_corrcoef, f1_score
from sklearn.model_selection import cross_val_predict, StratifiedKFold
from copy import deepcopy
from sklearn.metrics import confusion_matrix


def get_data_for_ml(features_file=None, labels_file=None, persons_file=None):
    """
    Fetch saved data for ML models
    """
    print("fetch ML data from the file")
    if features_file is None or labels_file is None or persons_file is None:
        print("please provide file name for ml data")

    else:
        with open(features_file, "rb") as fh:
            X_all = pickle.load(fh)

        # labels
        with open(labels_file, "rb") as fh:
            Y_all = pickle.load(fh)

        # persons
        with open(persons_file, "rb") as fh:
            rec_ids_all = pickle.load(fh)

        return X_all, Y_all, rec_ids_all


def select_pos_unlab_records(X, Y, rec_ids):
    """
    divide data into positive and unlabeled groups
    """
    print("divide data into positive and unlabeled groups")
    ix0 = np.where(Y == 0)[0]
    ix1 = np.where(Y == 1)[0]
    print("n_pos and n_unlab count: ", len(ix1), len(ix0))
    return X[ix1], Y[ix1], rec_ids[ix1], X[ix0], Y[ix0], rec_ids[ix0]


def get_unlab_count_in_balanced_sets(Y_pos, Y_unlab):
    """
    compute the count of unlabeled records in each balanced datasets
    """
    print("compute balanced dataset counts")
    n_balanced_set = int(len(Y_unlab) / len(Y_pos))
    unlab_count_in_balanced_set = np.asarray([len(Y_pos)] * n_balanced_set)
    remain_recs = len(Y_unlab) - len(Y_pos) * n_balanced_set
    while remain_recs > 0:
        if remain_recs >= n_balanced_set:
            additional_recs = int(remain_recs / n_balanced_set)
            unlab_count_in_balanced_set += additional_recs
            remain_recs = len(Y_unlab) - np.sum(unlab_count_in_balanced_set)
        else:
            for i in range(remain_recs):
                unlab_count_in_balanced_set[i] += 1
            remain_recs = 0
    return unlab_count_in_balanced_set


def run_pulsnar_models(X_pos, Y_pos, rec_pos, X_unlab, Y_unlab, rec_unlab, unlab_count_in_balanced_set, n_iterations, user_param_file, iodata):
    """
    run XGBoost models with and without running PULSNAR algorithm and save predicted predictions and classification performance
    """
    # variables to store values
    before_acc, after_acc = [], []
    before_mcc, after_mcc = [], []
    before_auc, after_auc = [], []
    before_f1, after_f1 = [], []
    before_aps, after_aps = [], []
    before_bs, after_bs = [], []
    before_sensitivity, after_sensitivity = [], []
    before_specificity, after_specificity = [], []
    before_ppv, after_ppv = [], []
    before_tp, after_tp = [], []
    before_tn, after_tn = [], []
    before_fp, after_fp = [], []
    before_fn, after_fn = [], []

    unlab_calibrated_probs = []
    unlab_recs = []
    file_iterations = []
    iter_alpha = []
    iteration = []
    imp_features = {}

    # run ML models
    for i in range(n_iterations):
        start_idx = 0
        np.random.seed(1001 + i)
        unlab_idx = np.random.permutation(len(Y_unlab))
        for j, v in enumerate(unlab_count_in_balanced_set):
            iteration.append(i+1)
            end_idx = start_idx + v
            print("\nstart and end index: ", start_idx, end_idx)
            u_idx = unlab_idx[start_idx: end_idx]
            start_idx = end_idx
            X = np.concatenate([X_pos, X_unlab[u_idx]])  # merge the data
            Y = np.concatenate([Y_pos, [0] * v])
            rec_ids = np.concatenate([rec_pos, rec_unlab[u_idx]])
            X, Y, rec_ids = shuffle(X, Y, rec_ids, random_state=1001 + j)
            Y_true = Y
            X_save, Y_save, rec_ids_save = deepcopy(X), deepcopy(Y), deepcopy(rec_ids)  # keep a copy
            print("ML data shape (iteration, balanced set#): ", i, j, X.shape, len(Y), len(rec_ids), np.sum(Y))

            # instantiate PULSNAR classifier
            pls = PULSNAR.PULSNARClassifier(scar=False, csrdata=False, classifier='xgboost',
                                        n_clusters=0, covar_type='full', top50p_covars=False,
                                        bin_method='rice', bw_method='hist', lowerbw=0.05, upperbw=0.5, optim='local',
                                        calibration=True, calibration_data='PU', calibration_method='isotonic',
                                        calibration_n_bins=100, smooth_isotonic=False,
                                        classification_metrics=True,
                                        n_iterations=1, kfold=5, kflips=1,
                                        pulsnar_params_file=user_param_file)

            # get results
            res = pls.pulsnar(X, Y, tru_label=Y_true, rec_list=rec_ids)
            print("Estimated alpha: {0}".format(res['estimated_alpha']))
            iter_alpha.append(res['estimated_alpha'])

            # determine rec_id for probable positives
            u_df = pd.read_csv(res['prediction_file'], sep="\t", header=0)
            u_rec_ids, u_calib_probs = u_df['rec_id'], u_df['calibrated_prob']
            unlab_calibrated_probs.extend(u_calib_probs)
            unlab_recs.extend(u_rec_ids)
            file_iterations.extend([i + 1] * (len(u_rec_ids)))
            print("length of unlab calibration:  ", len(unlab_calibrated_probs), len(unlab_recs), len(file_iterations))
            u_calib_probs_sorted_idx = np.argsort(u_calib_probs)[::-1]
            probable_pos_count = int(res['estimated_alpha'] * v)
            probable_pos = u_rec_ids[u_calib_probs_sorted_idx[:probable_pos_count]]

            # ***** BEFORE PULSNAR ***** #
            # XGBoost parameters
            with open(user_param_file, 'r') as fi:
                mlparams = yaml.safe_load(fi)

            Xb, Yb, rec_ids_b = deepcopy(X_save), deepcopy(Y_save), deepcopy(rec_ids_save)
            pp_idx = np.isin(rec_ids_b, probable_pos).nonzero()[0]
            sel_idx = ~np.isin(np.arange(len(Yb)), pp_idx)  # indices after excluding probable positives

            # MODEL 1: model before PULSNAR i.e. on labeled positive and unlabeled examples
            model = XGBClassifier(**mlparams["XGB_params"])
            cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=1001 + j)
            y_pred_proba = cross_val_predict(model, Xb, Yb, cv=cv, method='predict_proba')

            # drop PP from metrics calculation
            y_pred_proba, Yb = y_pred_proba[sel_idx], Yb[sel_idx]
            print("length after dropping PP (before): ", len(Yb))
            y_pred = np.round(y_pred_proba).argmax(axis=1)
            accuracy = accuracy_score(Yb, y_pred)
            mcc = matthews_corrcoef(Yb, y_pred)
            auc = roc_auc_score(Yb, y_pred_proba[:, 1])
            bs = brier_score_loss(Yb, y_pred_proba[:, 1])
            aps = average_precision_score(Yb, y_pred_proba[:, 1])
            f1_val = f1_score(Yb, y_pred)
            tn, fp, fn, tp = confusion_matrix(Yb, y_pred).ravel()
            sensitivity = tp / (tp + fn)
            specificity = tn / (tn + fp)
            ppv = tp / (tp + fp)
            print("before accuracy: ", accuracy)
            print("before mcc: ", mcc)
            print("before auc: ", auc)
            print("before BS: ", bs)
            print("before APS: ", aps)
            print("before F1: ", f1_val)
            print("before sensitivity: ", sensitivity)
            print("before specificity: ", specificity)
            print("before PPV: ", ppv)
            before_acc.append(accuracy)
            before_mcc.append(mcc)
            before_auc.append(auc)
            before_f1.append(f1_val)
            before_aps.append(aps)
            before_bs.append(bs)
            before_sensitivity.append(sensitivity)
            before_specificity.append(specificity)
            before_ppv.append(ppv)
            before_tp.append(tp)
            before_tn.append(tn)
            before_fp.append(fp)
            before_fn.append(fn)


            # ***** AFTER PULSNAR ***** #
            # MODEL 2: model after PULSNAR i.e. on labeled positives, probable positives, and probable negatives
            Xa, Ya, rec_ids_a = deepcopy(X_save), deepcopy(Y_save), deepcopy(rec_ids_save)
            pp_idx = np.isin(rec_ids_a, probable_pos).nonzero()[0]  # probable positives indices
            sel_idx = ~np.isin(np.arange(len(Ya)), pp_idx)  # indices after excluding probable positives
            print("#labels to flip: ", len(pp_idx))
            Ya[pp_idx] = 1

            # get feature importance
            model = XGBClassifier(**mlparams["XGB_params"]).fit(Xa, Ya)
            feature_important = model.get_booster().get_score(importance_type='gain')
            for kk, vv in feature_important.items():
                imp_features.setdefault(kk, set()).add(vv)

            # model after PULSNAR
            model = XGBClassifier(**mlparams["XGB_params"])
            cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=1001 + j)
            y_pred_proba = cross_val_predict(model, Xa, Ya, cv=cv, method='predict_proba')

            # drop PP from metrics calculation
            y_pred_proba, Ya = y_pred_proba[sel_idx], Ya[sel_idx]
            print("length after dropping PP (after): ", len(Ya))
            y_pred = np.round(y_pred_proba).argmax(axis=1)
            accuracy = accuracy_score(Ya, y_pred)
            mcc = matthews_corrcoef(Ya, y_pred)
            auc = roc_auc_score(Ya, y_pred_proba[:, 1])
            bs = brier_score_loss(Ya, y_pred_proba[:, 1])
            aps = average_precision_score(Ya, y_pred_proba[:, 1])
            f1_val = f1_score(Ya, y_pred)
            tn, fp, fn, tp = confusion_matrix(Ya, y_pred).ravel()
            sensitivity = tp / (tp + fn)
            specificity = tn / (tn + fp)
            ppv = tp / (tp + fp)
            print("after accuracy: ", accuracy)
            print("after mcc: ", mcc)
            print("after auc: ", auc)
            print("after BS: ", bs)
            print("after APS: ", aps)
            print("after F1: ", f1_val)
            print("after sensitivity: ", sensitivity)
            print("after specificity: ", specificity)
            print("after PPV: ", ppv)
            after_acc.append(accuracy)
            after_mcc.append(mcc)
            after_auc.append(auc)
            after_f1.append(f1_val)
            after_aps.append(aps)
            after_bs.append(bs)
            after_sensitivity.append(sensitivity)
            after_specificity.append(specificity)
            after_ppv.append(ppv)
            after_tp.append(tp)
            after_tn.append(tn)
            after_fp.append(fp)
            after_fn.append(fn)

    print("Mean alpha: ", np.mean(iter_alpha))
    
    # write prediction to a file
    final_df_dict = {'iteration': file_iterations, 
                     'rec_id': unlab_recs, 
                     'calibrated_probs': unlab_calibrated_probs}
    final_df = pd.DataFrame(final_df_dict)
    final_df.to_csv(iodata["output_files"]["predictions_file"], sep="\t", index=False)

    # write classification results to file
    classification_df_dict = {'iteration': iteration,
                              'Before AUC': before_auc, 'After AUC': after_auc,
                              'Before Accuracy': before_acc, 'After Accuracy': after_acc,
                              'Before MCC': before_mcc, 'After MCC': after_mcc,
                              'Before APS': before_aps, 'After APS': after_aps,
                              'Before BS': before_bs, 'After BS': after_bs,
                              'Before F1': before_f1, 'After F1': after_f1,
                              'Before sensitivity': before_sensitivity, 'After sensitivity': after_sensitivity,
                              'Before specificity': before_specificity, 'After specificity': after_specificity,
                              'Before ppv': before_ppv, 'After ppv': after_ppv,
                              'Before tp': before_tp, 'After tp': after_tp,
                              'Before tn': before_tn, 'After tn': after_tn,
                              'Before fp': before_fp, 'After fp': after_fp,
                              'Before fn': before_fn, 'After fn': after_fn,
                              'estimated alpha': iter_alpha,}
    classification_df = pd.DataFrame(classification_df_dict)
    classification_df.to_csv(iodata["output_files"]["classification_perf_file"], index=True)



def main():
    """
    This program run PU learning model to estimate uncoded OUD and compute classification performance
    Use the following command to run this code:
    python apply_pulsnar_classification_performance.py -iofiles io_data.yaml -paramfiles ml_params.yaml
    """

    # load IO filenames and configuration from the YAML file
    parser = argparse.ArgumentParser()
    parser.add_argument("-iofiles", default="io_data.yaml", help="provide yaml file containing io files")
    parser.add_argument("-paramfiles", default="ml_params.yaml", help="provide ML parameter file")
    p_args = parser.parse_args()
    with open(p_args.iofiles, 'r') as fi:
        iodata = yaml.safe_load(fi)
    
    # STEP 1: get data for ml
    X_all, Y_all, rec_ids_all = get_data_for_ml(features_file=iodata['output_files']['features_file'],
                                                labels_file=iodata['output_files']['labels_file'],
                                                persons_file=iodata['output_files']['persons_file'])
    print("total number of records and positive records: ", X_all.shape, len(Y_all), len(rec_ids_all), np.sum(Y_all))

    # STEP 2: select positive and unlabeled records
    X_pos, Y_pos, rec_pos, X_unlab, Y_unlab, rec_unlab = select_pos_unlab_records(X_all, Y_all, rec_ids_all)

    # STEP 3: generate balanced datasets and apply PULSNAR
    unlab_count_in_balanced_set = get_unlab_count_in_balanced_sets(Y_pos, Y_unlab)

    # STEP 4: run PULSNAR for each balanced set
    run_pulsnar_models(X_pos, Y_pos, rec_pos, X_unlab, Y_unlab, rec_unlab, unlab_count_in_balanced_set, iodata["n_iterations"], p_args.paramfiles, iodata)

if __name__ == "__main__":
    main()