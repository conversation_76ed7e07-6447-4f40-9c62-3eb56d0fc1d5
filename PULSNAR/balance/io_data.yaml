input_files:
  # list of input files for the package
  oud_codes_file: "IOData/oud_concept_codes.tsv.gz"
  concepts_file: "IOData/concepts.tsv.gz"
  concept_ancestor_file: "IOData/concept_ancestors.tsv.gz"
  person_data_file: "IOData/oud_person_data.tsv.gz"
  person_condition_file: "IOData/oud_person_conditions_data.tsv.gz"
  person_drug_file: "IOData/oud_person_drugs_data.tsv.gz"
  person_procedure_file: "IOData/oud_person_procedure_data.tsv.gz"
  icd10_concept_code_id_file: "IOData/ICD10_ancestors/concept_code_id_dict.pkl"
  icd10_concept_ancestors_file: "IOData/ICD10_ancestors/concept_ancestors_dict.pkl"

output_files:
  # output files - pickle files
  descendant_ancestor_file: "IOData/pklFiles/descendant_ancestor_dict.pkl"
  concept_id_name_file: "IOData/pklFiles/concept_id_name_dict.pkl"
  person_oud_date_file: "IOData/pklFiles/person_oud_date_dict.pkl"
  person_data_file: "IOData/pklFiles/person_data_dict.pkl"

  # output files for ML data
  features_file: "IOData/MLData/features.pkl"
  labels_file: "IOData/MLData/labels.pkl"
  persons_file: "IOData/MLData/persons.pkl"
  covars_file: "IOData/MLData/covariates.pkl"

  # output file for classification performance
  classification_perf_file: "IOData/results_with_ancestors/classification_results.csv"
  predictions_file: "IOData/results_with_ancestors/prediction_results.tsv"
  imp_features_file: "IOData/results_with_ancestors/imp_features.tsv"

  # plot data
  shap_values_file: "IOData/SHAP_data/xgb_shap_values.pkl"
  X_SHAP_file: "IOData/SHAP_data/X_SHAP.pkl"
  Y_SHAP_file: "IOData/SHAP_data/Y_SHAP.pkl"
  SHAP_plot_file: "plots/shap_summary_plot_top_15_features_oct3.png"
  hist_plot_file: "plots/horizontal_histogram_oct3.png"


icd9_oud_codes: [44819555, 44820728, 44822989, 44824126, 44826517, 44827668, 44829933, 44829939, 44832247, 44834602,
                 44835800, 44835804, 44836978, 44836979, 44836982]

xgb_top15_features: ['Symptoms, signs and abnormal clinical and laboratory findings, not elsewhere classified', 'acetaminophen',
                     'SARS-CoV-2 (COVID-19) vaccine, mRNA spike protein', 'buprenorphine', 'Chronic pain syndrome',
                     'Diseases of the respiratory system', 'Alcohol related disorders', 'Outcome of delivery', 'fentanyl',
                     'Mental and behavioural disorders', 'Other psychoactive substance related disorders',
                     'Diseases of the skin and subcutaneous tissue', 'naloxone', 'codeine', 'Emergency use of U07.1 | COVID-19, virus identified']

n_iterations: 40