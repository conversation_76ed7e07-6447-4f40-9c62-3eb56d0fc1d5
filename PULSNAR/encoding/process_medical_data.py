#!/usr/bin/env python3
"""
Medical Data Processing Pipeline
Processes diagnosis and notes data to create self-harm prediction dataset
With flexible column names via command line arguments
"""

import argparse
import numpy as np
import pandas as pd
import re
from sklearn.preprocessing import MultiLabelBinarizer
from pathlib import Path
import logging
import sys
from typing import Optional, List, Dict, Any

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MedicalDataProcessor:
    """Main class for processing medical data with configurable column names"""
    
    def __init__(self, random_seed=45, subject_id_col='SUBJECT_ID', icd_code_col='ICD9_CODE', 
                 text_col='TEXT', labels_col='labels', icd_encoding_col='self_harm_icd_encod'):
        """
        Initialize the processor
        
        Args:
            random_seed (int): Random seed for reproducibility
            subject_id_col (str): Name of subject ID column
            icd_code_col (str): Name of ICD code column
            text_col (str): Name of text column
            labels_col (str): Name of labels column
            icd_encoding_col (str): Name of ICD encoding column
        """
        np.random.seed(random_seed)
        self.random_seed = random_seed
        self.subject_id_col = subject_id_col
        self.icd_code_col = icd_code_col
        self.text_col = text_col
        self.labels_col = labels_col
        self.icd_encoding_col = icd_encoding_col
        
    def load_diagnosis_data(self, file_path):
        """
        Load and group diagnosis data by subject ID
        
        Args:
            file_path (str): Path to diagnosis CSV file
            
        Returns:
            pd.DataFrame: Grouped diagnosis data
        """
        logger.info(f"Loading diagnosis data from: {file_path}")
        
        if not Path(file_path).exists():
            raise FileNotFoundError(f"Diagnosis file not found: {file_path}")
            
        df = pd.read_csv(file_path)
        logger.info(f"Loaded {len(df)} diagnosis records")
        
        # Validate required columns exist
        required_cols = [self.subject_id_col, self.icd_code_col]
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            raise ValueError(f"Missing required columns in diagnosis file: {missing_cols}")
        
        # Group by subject ID
        df_grouped = df.groupby([self.subject_id_col]).agg({
            self.icd_code_col: list,
        }).reset_index()
        
        logger.info(f"Grouped into {len(df_grouped)} unique subjects")
        return df_grouped
    
    def load_notes_data(self, file_path):
        """
        Load and group notes data by subject ID
        
        Args:
            file_path (str): Path to notes CSV file
            
        Returns:
            pd.DataFrame: Grouped notes data
        """
        logger.info(f"Loading notes data from: {file_path}")
        
        if not Path(file_path).exists():
            raise FileNotFoundError(f"Notes file not found: {file_path}")
            
        df = pd.read_csv(file_path, low_memory=False)
        logger.info(f"Loaded {len(df)} note records")
        
        # Validate required columns exist
        required_cols = [self.subject_id_col, self.text_col]
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            raise ValueError(f"Missing required columns in notes file: {missing_cols}")
        
        # Group by subject ID and get unique texts
        df_grouped = df.groupby([self.subject_id_col])[self.text_col].apply(
            lambda x: list(set(x))
        ).reset_index()
        
        logger.info(f"Grouped into {len(df_grouped)} unique subjects with notes")
        return df_grouped
    
    def merge_data(self, diagnosis_df, notes_df):
        """
        Merge diagnosis and notes data
        
        Args:
            diagnosis_df (pd.DataFrame): Grouped diagnosis data
            notes_df (pd.DataFrame): Grouped notes data
            
        Returns:
            pd.DataFrame: Merged data
        """
        logger.info("Merging diagnosis and notes data")
        
        merged_df = diagnosis_df.merge(notes_df, on=[self.subject_id_col], how='left')
        logger.info(f"Merged dataset contains {len(merged_df)} subjects")
        
        return merged_df
    
    def create_icd_encoding(self, df, self_harm_pattern):
        """
        Create multi-label binary encoding for ICD codes
        
        Args:
            df (pd.DataFrame): DataFrame with ICD code column
            self_harm_pattern (str): Regex pattern for self-harm codes
            
        Returns:
            tuple: (encoded_df, cleaned_codes)
        """
        logger.info("Creating ICD code encoding")
        
        # Clean ICD codes
        cleaned_codes = df[self.icd_code_col].apply(
            lambda x: [code for code in x if pd.notna(code)] if isinstance(x, list) else []
        )
        
        # Create multi-label binarizer
        mlb = MultiLabelBinarizer()
        encoded_matrix = mlb.fit_transform(cleaned_codes)
        
        # Create DataFrame with encoded features
        encoded_df = pd.DataFrame(
            encoded_matrix,
            columns=mlb.classes_,
            dtype=int
        )
        
        logger.info(f"Created encoding with {len(mlb.classes_)} unique ICD codes")
        
        # Remove self-harm codes from features
        col_to_keep = [
            col for col in encoded_df.columns 
            if not re.match(self_harm_pattern, str(col))
        ]
        encoded_df = encoded_df[col_to_keep]
        
        logger.info(f"Kept {len(col_to_keep)} non-self-harm ICD codes as features")
        
        return encoded_df, cleaned_codes
    
    def create_labels(self, icd_codes, self_harm_pattern):
        """
        Create binary labels for self-harm prediction
        
        Args:
            icd_codes (pd.Series): Series with ICD code lists
            self_harm_pattern (str): Regex pattern for self-harm codes
            
        Returns:
            pd.Series: Binary labels
        """
        logger.info("Creating self-harm labels")
        
        labels = icd_codes.apply(
            lambda x: int(any(re.search(self_harm_pattern, str(code)) for code in x if pd.notna(code))) 
            if isinstance(x, list) else 0
        )
        
        positive_cases = labels.sum()
        logger.info(f"Found {positive_cases} positive self-harm cases out of {len(labels)} total")
        
        return labels
    
    def calculate_word_counts(self, df):
        """
        Calculate word counts for text data
        
        Args:
            df (pd.DataFrame): DataFrame with text column
            
        Returns:
            pd.DataFrame: DataFrame with word_count column added
        """
        logger.info("Calculating word counts")
        
        df = df.copy()
        df['word_count'] = df[self.text_col].astype(str).str.split().str.len()
        
        word_count_stats = df['word_count'].describe()
        logger.info(f"Word count statistics:\n{word_count_stats}")
        
        return df
    
    def process_data(self, diagnosis_path, notes_path, output_path, self_harm_pattern=None):
        """
        Main processing pipeline
        
        Args:
            diagnosis_path (str): Path to diagnosis CSV file
            notes_path (str): Path to notes CSV file  
            output_path (str): Path for output pickle file
            self_harm_pattern (str): Regex pattern for self-harm codes
            
        Returns:
            pd.DataFrame: Processed dataset
        """
        if self_harm_pattern is None:
            self_harm_pattern = (
                r"E9500|E9501|E9502|E9503|E9504|E9505|E9506|E9507|E9509|"
                r"E9530|E9538|E954|E9550|E9554|E9559|E956|E9570|E9571|"
                r"E9572|E9579|E9580|E9581|E9583|E9585|E9588|E9589|E959"
            )
        
        logger.info("Starting medical data processing pipeline")
        
        # Load data
        diagnosis_df = self.load_diagnosis_data(diagnosis_path)
        notes_df = self.load_notes_data(notes_path)
        
        # Merge data
        merged_df = self.merge_data(diagnosis_df, notes_df)
        
        # Create ICD encoding
        icd_encoding, cleaned_codes = self.create_icd_encoding(merged_df, self_harm_pattern)
        
        # Add encoding to merged data
        merged_df[self.icd_encoding_col] = list(icd_encoding.values)
        
        # Create labels
        merged_df[self.labels_col] = self.create_labels(merged_df[self.icd_code_col], self_harm_pattern)
        
        # Calculate word counts
        merged_df = self.calculate_word_counts(merged_df)
        
        # Sort by word count
        df_sorted = merged_df.sort_values('word_count').reset_index(drop=True)
        
        # Save results
        logger.info(f"Saving processed data to: {output_path}")
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        df_sorted.to_pickle(output_path)
        
        logger.info(f"Processing complete! Final dataset length: {len(df_sorted)}")
        
        return df_sorted


def main(diagnosis_path: str = './datasets/1.4/DIAGNOSES_ICD.csv',
         notes_path: str = './datasets/1.4/NOTEEVENTS.csv',
         output_path: str = './icd_note_word.pkl',
         self_harm_pattern: str = None,
         random_seed: int = 45,
         verbose: bool = False,
         subject_id_col: str = 'SUBJECT_ID',
         icd_code_col: str = 'ICD9_CODE',
         text_col: str = 'TEXT',
         labels_col: str = 'labels',
         icd_encoding_col: str = 'self_harm_icd_encod') -> None:
    """
    Main processing pipeline for medical diagnosis and notes data.
    
    Args:
        diagnosis_path (str): Path to diagnosis CSV file
        notes_path (str): Path to notes CSV file  
        output_path (str): Path for output pickle file
        self_harm_pattern (str): Custom regex pattern for self-harm ICD codes
        random_seed (int): Random seed for reproducibility
        verbose (bool): Enable verbose logging
        subject_id_col (str): Name of subject ID column
        icd_code_col (str): Name of ICD code column
        text_col (str): Name of text column
        labels_col (str): Name of labels column
        icd_encoding_col (str): Name of ICD encoding column
    """
    # Set logging level
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        # Initialize processor
        processor = MedicalDataProcessor(
            random_seed=random_seed,
            subject_id_col=subject_id_col,
            icd_code_col=icd_code_col,
            text_col=text_col,
            labels_col=labels_col,
            icd_encoding_col=icd_encoding_col
        )
        
        # Process data
        result_df = processor.process_data(
            diagnosis_path=diagnosis_path,
            notes_path=notes_path,
            output_path=output_path,
            self_harm_pattern=self_harm_pattern
        )
        
        logger.info("Successfully completed processing!")
        
    except Exception as e:
        logger.error(f"Processing failed: {str(e)}")
        sys.exit(1)


def parse_arguments() -> argparse.Namespace:
    """
    Parse command line arguments.
    
    Returns:
        argparse.Namespace: Parsed arguments
    """
    parser = argparse.ArgumentParser(
        description="Process medical diagnosis and notes data for self-harm prediction",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    # File paths
    parser.add_argument(
        '--diagnosis-path',
        type=str,
        default='./datasets/1.4/DIAGNOSES_ICD.csv',
        help='Path to diagnosis CSV file'
    )
    
    parser.add_argument(
        '--notes-path', 
        type=str,
        default='./datasets/1.4/NOTEEVENTS.csv',
        help='Path to notes CSV file'
    )
    
    parser.add_argument(
        '--output-path',
        type=str,
        default='./icd_note_word.pkl',
        help='Path for output pickle file'
    )
    
    # Processing parameters
    parser.add_argument(
        '--self-harm-pattern',
        type=str,
        default=None,
        help='Custom regex pattern for self-harm ICD codes'
    )
    
    parser.add_argument(
        '--random-seed',
        type=int,
        default=45,
        help='Random seed for reproducibility'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    
    # Column name configuration
    parser.add_argument(
        '--subject-id-col',
        type=str,
        default='SUBJECT_ID',
        help='Name of subject ID column'
    )
    
    parser.add_argument(
        '--icd-code-col',
        type=str,
        default='ICD9_CODE',
        help='Name of ICD code column'
    )
    
    parser.add_argument(
        '--text-col',
        type=str,
        default='TEXT',
        help='Name of text column'
    )
    
    parser.add_argument(
        '--labels-col',
        type=str,
        default='labels',
        help='Name of output labels column'
    )
    
    parser.add_argument(
        '--icd-encoding-col',
        type=str,
        default='self_harm_icd_encod',
        help='Name of ICD encoding column'
    )
    
    return parser.parse_args()


if __name__ == "__main__":
    # Parse command line arguments
    args = parse_arguments()
    
    # Run main processing pipeline
    main(
        diagnosis_path=args.diagnosis_path,
        notes_path=args.notes_path,
        output_path=args.output_path,
        self_harm_pattern=args.self_harm_pattern,
        random_seed=args.random_seed,
        verbose=args.verbose,
        subject_id_col=args.subject_id_col,
        icd_code_col=args.icd_code_col,
        text_col=args.text_col,
        labels_col=args.labels_col,
        icd_encoding_col=args.icd_encoding_col
    )